// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class DocumentReminder : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The CreatedBy property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CreatedBy { get; set; }
#nullable restore
#else
        public string CreatedBy { get; set; }
#endif
        /// <summary>The CreatedDate property</summary>
        public DateTimeOffset? CreatedDate { get; set; }
        /// <summary>The Document property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document? Document { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document Document { get; set; }
#endif
        /// <summary>The EmailBody property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? EmailBody { get; set; }
#nullable restore
#else
        public string EmailBody { get; set; }
#endif
        /// <summary>The EmailFromAddress property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? EmailFromAddress { get; set; }
#nullable restore
#else
        public string EmailFromAddress { get; set; }
#endif
        /// <summary>The EmailSubject property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? EmailSubject { get; set; }
#nullable restore
#else
        public string EmailSubject { get; set; }
#endif
        /// <summary>The Href property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Href { get; set; }
#nullable restore
#else
        public string Href { get; set; }
#endif
        /// <summary>The Name property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Name { get; set; }
#nullable restore
#else
        public string Name { get; set; }
#endif
        /// <summary>The RecipientAdHocEmails property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<string>? RecipientAdHocEmails { get; set; }
#nullable restore
#else
        public List<string> RecipientAdHocEmails { get; set; }
#endif
        /// <summary>The RecipientContacts property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionContact? RecipientContacts { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionContact RecipientContacts { get; set; }
#endif
        /// <summary>The RecipientGroups property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup? RecipientGroups { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup RecipientGroups { get; set; }
#endif
        /// <summary>The RecipientUsers property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionUser? RecipientUsers { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionUser RecipientUsers { get; set; }
#endif
        /// <summary>The ReminderDate property</summary>
        public DateTimeOffset? ReminderDate { get; set; }
        /// <summary>The UpdatedBy property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? UpdatedBy { get; set; }
#nullable restore
#else
        public string UpdatedBy { get; set; }
#endif
        /// <summary>The UpdatedDate property</summary>
        public DateTimeOffset? UpdatedDate { get; set; }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.DocumentReminder"/> and sets the default values.
        /// </summary>
        public DocumentReminder()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.DocumentReminder"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.DocumentReminder CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.DocumentReminder();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "CreatedBy", n => { CreatedBy = n.GetStringValue(); } },
                { "CreatedDate", n => { CreatedDate = n.GetDateTimeOffsetValue(); } },
                { "Document", n => { Document = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document.CreateFromDiscriminatorValue); } },
                { "EmailBody", n => { EmailBody = n.GetStringValue(); } },
                { "EmailFromAddress", n => { EmailFromAddress = n.GetStringValue(); } },
                { "EmailSubject", n => { EmailSubject = n.GetStringValue(); } },
                { "Href", n => { Href = n.GetStringValue(); } },
                { "Name", n => { Name = n.GetStringValue(); } },
                { "RecipientAdHocEmails", n => { RecipientAdHocEmails = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
                { "RecipientContacts", n => { RecipientContacts = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionContact>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionContact.CreateFromDiscriminatorValue); } },
                { "RecipientGroups", n => { RecipientGroups = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup.CreateFromDiscriminatorValue); } },
                { "RecipientUsers", n => { RecipientUsers = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionUser>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionUser.CreateFromDiscriminatorValue); } },
                { "ReminderDate", n => { ReminderDate = n.GetDateTimeOffsetValue(); } },
                { "UpdatedBy", n => { UpdatedBy = n.GetStringValue(); } },
                { "UpdatedDate", n => { UpdatedDate = n.GetDateTimeOffsetValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("CreatedBy", CreatedBy);
            writer.WriteDateTimeOffsetValue("CreatedDate", CreatedDate);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document>("Document", Document);
            writer.WriteStringValue("EmailBody", EmailBody);
            writer.WriteStringValue("EmailFromAddress", EmailFromAddress);
            writer.WriteStringValue("EmailSubject", EmailSubject);
            writer.WriteStringValue("Href", Href);
            writer.WriteStringValue("Name", Name);
            writer.WriteCollectionOfPrimitiveValues<string>("RecipientAdHocEmails", RecipientAdHocEmails);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionContact>("RecipientContacts", RecipientContacts);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup>("RecipientGroups", RecipientGroups);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionUser>("RecipientUsers", RecipientUsers);
            writer.WriteDateTimeOffsetValue("ReminderDate", ReminderDate);
            writer.WriteStringValue("UpdatedBy", UpdatedBy);
            writer.WriteDateTimeOffsetValue("UpdatedDate", UpdatedDate);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
