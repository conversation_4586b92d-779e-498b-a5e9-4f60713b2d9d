using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

using DocuSign.Platform.Storage.Clients.Redis.ConnectionInfo;

namespace TaskManagementService.Configurations;

[ExcludeFromCodeCoverage]

public class RedisCacheConfiguration
{
    public IDictionary<string, AzureRedisConnectionInformation> ConnectionInfo { get; } = new Dictionary<string, AzureRedisConnectionInformation>(StringComparer.InvariantCultureIgnoreCase);
}
