// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Workitems.Item;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Workitems
{
    /// <summary>
    /// Builds and executes requests for operations under \v2\{accountId}\workitems
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class WorkitemsRequestBuilder : BaseRequestBuilder
    {
        /// <summary>Gets an item from the TaskManagementService.ServiceIntegrations.Clients.CLMV2API.v2.item.workitems.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Workitems.Item.WorkitemsItemRequestBuilder"/></returns>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Workitems.Item.WorkitemsItemRequestBuilder this[Guid position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                urlTplParams.Add("id", position);
                return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Workitems.Item.WorkitemsItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>Gets an item from the TaskManagementService.ServiceIntegrations.Clients.CLMV2API.v2.item.workitems.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Workitems.Item.WorkitemsItemRequestBuilder"/></returns>
        [Obsolete("This indexer is deprecated and will be removed in the next major version. Use the one with the typed parameter instead.")]
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Workitems.Item.WorkitemsItemRequestBuilder this[string position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                if (!string.IsNullOrWhiteSpace(position)) urlTplParams.Add("id", position);
                return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Workitems.Item.WorkitemsItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Workitems.WorkitemsRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public WorkitemsRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/v2/{accountId}/workitems", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Workitems.WorkitemsRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public WorkitemsRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/v2/{accountId}/workitems", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
