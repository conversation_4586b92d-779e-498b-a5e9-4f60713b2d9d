[AccountServerConfig]
$type=TaskManagementService.Core.Config.IAccountServerConfig

AuthTokenUrl&env:dev="https://account-tk1.tk.docusign.dev/oauth/token/"
AuthTokenUrl&env:test="https://account-tk1.tk.docusign.dev/oauth/token/"
AuthTokenUrl&env:stage="https://account-s.docusign.com/oauth/token/"
AuthTokenUrl&env:demo="https://account-d.docusign.com/oauth/token/"
AuthTokenUrl&env:prod="https://account.docusign.com/oauth/token/"
AuthTokenUrl=""

HttpClientRetryHandlerMaxTries=5

HttpClientRetryHandlerSleepIntervalMs=1000

ClientId="83c61cb8-5eaa-4fac-a284-4225b0ec2df6"

JwtScope="task_api"

IsLive=true

RetryCount=3

RetryWaitPeriodMs=200
