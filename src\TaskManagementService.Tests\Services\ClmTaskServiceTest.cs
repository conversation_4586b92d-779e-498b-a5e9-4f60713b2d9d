﻿using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Authentication;

using Moq;

using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Infrastructure.Services;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API;

using Xunit;
using NSubstitute;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models;
using TaskManagementService.Core.Enums;
using TaskManagementService.Infrastructure.Resources;
using System.Linq;

using NSubstitute.ExceptionExtensions;

using TaskGroup = TaskManagementService.Core.Models.TaskGroup;

namespace TaskManagementService.Tests.Services;

[Trait("TestType", "UnitTest")]
public class ClmTaskServiceTest
{
    private readonly Mock<ILogger<ClmTaskService>> _mockedILogger = new();
    private readonly Mock<IAuthenticationProvider> _mockedAuthenticationProvider = new();
    private readonly Mock<HttpClient> _mockedHttpClient = new();
    private readonly Mock<IRequestContextService> _mockedRequestContextService = new();

    public ClmTaskServiceTest()
    {
        _mockedRequestContextService.Setup(x => x.ClmApiBaseUrl).Returns(new Uri("https://test.com"));
        _mockedRequestContextService.Setup(x => x.IsClmAccount).Returns(true);
    }

    [Fact]
    public void GetCLMV2APIClient()
    {
        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object);
        var result = clmTaskService.GetCLMV2APIClient();
        Assert.NotNull(result);
        Assert.True(result.GetType() == typeof(CLMV2APIClient));
    }

    [Fact]
    public async Task GetTasksAsync()
    {
        var groupId = Guid.NewGuid();
        var taskSummary = new TaskSummary
        {
            WorkItems =
            [
                new TaskSummaryWorkItem
                {
                    Id = Guid.NewGuid(),
                    Name = "Task 1",
                    Source = "CLMWorkflow",
                    Type = HumanActivityConstants.ApproveDocumentsActivity,
                    Documents =
                    [
                        new Document
                        {
                            Uid = Guid.NewGuid().ToString(),
                            CreatedDate = DateTimeOffset.Now,
                            Name = "Document 1",
                        }
                    ],
                    WorkflowQueueId = groupId,
                }
            ],
            WorkflowQueues =
            [
                new TaskSummaryTaskGroup
                {
                    Name = "Group 1",
                    Id = groupId,
                    Permissions = new TaskGroupPermissions()
                    {
                        CanAssign = true,
                        CanClaim = true,
                        CanUnclaim = true
                    },
                    Members =
                    [
                        new TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskGroupMember
                        {
                            FullName = "Test User",
                            Id = Guid.NewGuid()
                        },
                        new TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskGroupMember
                        {
                            FullName = "Test User 2",
                            Id = Guid.NewGuid()
                        }

                    ]
                }
            ]
        };

        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);

        adapter.SendAsync(
           Arg.Any<RequestInformation>(),
           Arg.Any<ParsableFactory<TaskSummary>>(),
           Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
           Arg.Any<CancellationToken>())
           .ReturnsForAnyArgs(taskSummary);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        var result = await clmTaskService.GetTasksAsync(Guid.NewGuid(), new TaskFilter
        {
            GroupAssignees =
            [
                groupId.ToString(),
                "123"
            ],
        });

        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Empty(result[0].Assignees);
        Assert.True(string.IsNullOrEmpty(result[0].WorkflowName));
        Assert.True(string.IsNullOrEmpty(result[0].NavigationUrl));
        Assert.True(string.IsNullOrEmpty(result[0].Description));
        Assert.True(result.GetType() == typeof(List<UserTask>));
        Assert.True(result[0].Source == TaskSource.ClmWorkflow);
        Assert.True(result[0].Type == TaskType.Approve);
        Assert.True(result[0].Name == "Task 1");
        Assert.True(result[0].Title == "Task 1");
        Assert.True(result[0].Assignor == null);
        Assert.Single(result[0].Agreements);
    }

    [Fact]
    public async Task GetTasksAsyncException()
    {
        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);

        var apiException = new ApiException("New api exception");

        adapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<TaskSummary>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
            .ThrowsForAnyArgs(apiException);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        var result = await clmTaskService.GetTasksAsync(Guid.NewGuid(), new TaskFilter { });

        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetClmTaskHistoryAsync()
    {
        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);
        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        var taskId = Guid.NewGuid();

        var taskHistory = new ActivityHistory()
        {
            TaskId = taskId,
            Source = "CLMWorkflow",
            Href = "https://test.com/test-test-test-1234",
            HistoryEvents =
            [
                new Activity
                {
                    AssignedUser = "Test User",
                    Title = "Test History Event 1",
                    Description = "Task assigned to test user",
                    CreatedDate = DateTimeOffset.Now,
                    DueDate = DateTimeOffset.Now.AddDays(1),
                },
                new Activity
                {
                    AssignedUser = "Unassigned",
                    Title = "Test History Event 2",
                    Description = "Task unassigned from test user",
                    CreatedDate = DateTimeOffset.Now,
                    DueDate = DateTimeOffset.Now.AddDays(2),
                }
            ]
        };

        adapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<ActivityHistory>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
            .ReturnsForAnyArgs(taskHistory);
        var result = await clmTaskService.GetClmTaskHistoryAsync(Guid.NewGuid(), taskId);

        Assert.NotNull(result);
        Assert.Equal(2, result.AuditEvents.Count());
        Assert.Equal(TaskSource.ClmWorkflow, result.Source);
        Assert.Equal(taskId.ToString(), result.TaskId);
        Assert.Equal("Test User", result.AuditEvents.First().AssignedUser);
        Assert.False(result.AuditEvents.First().IsCompleted);
    }

    [Fact]
    public async Task GetClmTaskHistoryAsyncException()
    {
        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);
        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        var apiException = new ApiException("New api exception");

        adapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<ActivityHistory>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
            .ThrowsForAnyArgs(apiException);
        var result = await clmTaskService.GetClmTaskHistoryAsync(Guid.NewGuid(), Guid.NewGuid());

        Assert.NotNull(result);
        Assert.Equal(string.Empty, result.TaskId);
    }

    [Fact]
    public async Task GetTasksGroupsAsync()
    {
        var workflowGroups = new ApiCollectionWorkflowQueue
        {
            Items = [
                new WorkflowQueue
                {
                    Name = "Group 1",
                    Href = "https://test.com/test-test-test-1234",
                },
                new WorkflowQueue
                {
                    Name = "Group 2",
                    Href = "https://test.com/test-test-test-5678",
                },
                new WorkflowQueue
                {
                    Name = "Group 3",
                    Href = "https://test.com/test-test-test-91011",
                }
            ]
        };

        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);

        adapter.SendAsync(
           Arg.Any<RequestInformation>(),
           Arg.Any<ParsableFactory<ApiCollectionWorkflowQueue>>(),
           Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
           Arg.Any<CancellationToken>())
           .ReturnsForAnyArgs(workflowGroups);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        var result = await clmTaskService.GetTasksGroupsAsync(Guid.NewGuid(), includeMembers: false);

        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.True(result.GetType() == typeof(List<TaskGroup>));
        Assert.True(result[0].Name == "Group 1");
        Assert.True(result[1].Name == "Group 2");
        Assert.True(result[2].Name == "Group 3");
        Assert.True(result[0].Id == "test-test-test-1234");
        Assert.True(result[1].Id == "test-test-test-5678");
        Assert.True(result[2].Id == "test-test-test-91011");
        Assert.True(result[0].Source == TaskSource.ClmWorkflow);
    }

    [Fact]
    public async Task GetTasksGroupsAsyncException()
    {
        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);
        var apiException = new ApiException("New api exception");

        adapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<ApiCollectionWorkflowQueue>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
            .ThrowsForAnyArgs(apiException);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        var result = await clmTaskService.GetTasksGroupsAsync(Guid.NewGuid(), includeMembers: false);

        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetTasksGroupsAsyncWithIncludeMembersTrue()
    {
        var mockedILogger = Substitute.For<ILogger<ClmTaskService>>();
        var mockedAuthenticationProvider = Substitute.For<IAuthenticationProvider>();
        var mockedHttpClient = Substitute.For<HttpClient>();
        var mockedRequestContextService = Substitute.For<IRequestContextService>();
        var mockAdapter = Substitute.For<IRequestAdapter>();
        var mockClmV2ApiClient = new CLMV2APIClient(mockAdapter);

        mockedRequestContextService.ClmApiBaseUrl.Returns(new Uri("https://clm.example.com/"));
        mockedRequestContextService.IsClmAccount.Returns(true);

        // Arrange
        var accountId = Guid.NewGuid();
        var groupId1 = Guid.NewGuid();
        var groupId2 = Guid.NewGuid();

        var workflowQueues = new ApiCollectionWorkflowQueue
        {
            Items =
            [
                new WorkflowQueue
                {
                    Name = "Group 1",
                    Href = $"https://clm.example.com/v2/{accountId}/workflowqueues/{groupId1}",
                },
                new WorkflowQueue
                {
                    Name = "Group 2",
                    Href = $"https://clm.example.com/v2/{accountId}/workflowqueues/{groupId2}",
                }
            ]
        };

        var groupsWithMembers = new ApiCollectionTaskSummaryTaskGroup
        {
            Items =
                [
                    new TaskSummaryTaskGroup
                    {
                        Id = groupId1,
                        Name = "Group 1",
                        Members =
                        [
                            new TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskGroupMember
                            {
                                Id = Guid.NewGuid(),
                                FullName = "Member 1",
                                Email = "<EMAIL>"
                            },
                            new TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskGroupMember
                            {
                                Id = Guid.NewGuid(),
                                FullName = "Member 2",
                                Email = "<EMAIL>"
                            }
                        ]
                    },
                    new TaskSummaryTaskGroup
                    {
                        Id = groupId2,
                        Name = "Group 2",
                        Members =
                        [
                            new TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskGroupMember
                            {
                                Id = Guid.NewGuid(),
                                FullName = "Member 3",
                                Email = "<EMAIL>"
                            }
                        ]
                    }
                ]
        };

        // Setup mock responses
        mockAdapter.SendAsync(
            Arg.Any<RequestInformation>(),
            Arg.Any<ParsableFactory<ApiCollectionWorkflowQueue>>(),
            Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
            Arg.Any<CancellationToken>())
            .Returns(workflowQueues);

        mockAdapter.SendAsync(
            Arg.Any<RequestInformation>(),
            Arg.Any<ParsableFactory<ApiCollectionTaskSummaryTaskGroup>>(),
            Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
            Arg.Any<CancellationToken>())
            .Returns(groupsWithMembers);

        var clmTaskService = new ClmTaskService(
            mockedILogger,
            mockedAuthenticationProvider,
            mockedHttpClient,
            mockedRequestContextService,
            mockClmV2ApiClient);

        // Act
        var result = await clmTaskService.GetTasksGroupsAsync(accountId, includeMembers: true);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);

        // Verify first group
        Assert.Equal(groupId1.ToString(), result[0].Id);
        Assert.Equal("Group 1", result[0].Name);
        Assert.Equal(TaskSource.ClmWorkflow, result[0].Source);
        Assert.Equal(2, result[0].Members.Count());

        var members1 = result[0].Members.ToList();
        Assert.Equal("Member 1", members1[0].FullName);
        Assert.Equal("<EMAIL>", members1[0].Email);
        Assert.Equal("Member 2", members1[1].FullName);
        Assert.Equal("<EMAIL>", members1[1].Email);

        // Verify second group
        Assert.Equal(groupId2.ToString(), result[1].Id);
        Assert.Equal("Group 2", result[1].Name);
        Assert.Equal(TaskSource.ClmWorkflow, result[1].Source);
        Assert.Single(result[1].Members);

        var members2 = result[1].Members.ToList();
        Assert.Equal("Member 3", members2[0].FullName);
        Assert.Equal("<EMAIL>", members2[0].Email);
    }

    [Fact]
    public async Task PostUnassignTaskAsync()
    {
        var taskGuid = Guid.NewGuid();
        var accountGuid = Guid.NewGuid();

        var workItem = new WorkItem
        {
            Name = "Task 1",
            Assignee = new User
            {
                Href = $"{_mockedRequestContextService.Object.ClmApiBaseUrl}v2/{accountGuid}/members/********-0000-0000-0000-************",
            }
        };

        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);

        adapter.SendAsync(
           Arg.Any<RequestInformation>(),
           Arg.Any<ParsableFactory<WorkItem>>(),
           Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
           Arg.Any<CancellationToken>())
           .ReturnsForAnyArgs(workItem);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        var result = await clmTaskService.PostUnassignTaskAsync(accountGuid, taskGuid);

        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task PostUnassignTaskAsyncValidationException()
    {
        var taskGuid = Guid.NewGuid();
        var accountGuid = Guid.NewGuid();

        var workItem = new WorkItem
        {
            Name = "Task 1",
            Assignee = new User
            {
                Href = $"{_mockedRequestContextService.Object.ClmApiBaseUrl}v2/{accountGuid}/members/{Guid.NewGuid()}",
            }
        };

        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);

        adapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<WorkItem>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
            .ReturnsForAnyArgs(workItem);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);

        try
        {
            var result = await clmTaskService.PostUnassignTaskAsync(accountGuid, taskGuid);
        }
        catch (ValidationException e)
        {
            Assert.NotNull(e);
            Assert.Equal("Href of returned workitem must match requested href", e.Message);
        }
    }

    [Fact]
    public async Task PostUnassignTaskAsyncNullValidationException()
    {
        var taskGuid = Guid.NewGuid();
        var accountGuid = Guid.NewGuid();

        var workItem = new WorkItem
        {
            Name = "Task 1",
            Assignee = new User
            {
                Href = null,
            }
        };

        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);

        adapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<WorkItem>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
            .ReturnsForAnyArgs(workItem);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);

        try
        {
            var result = await clmTaskService.PostUnassignTaskAsync(accountGuid, taskGuid);
        }
        catch (ValidationException e)
        {
            Assert.NotNull(e);
            Assert.Equal("Href of returned workitem must not be null", e.Message);
        }
    }

    [Fact]
    public async Task PostUnassignTaskAsyncUnprocessableException()
    {
        var taskGuid = Guid.NewGuid();
        var accountGuid = Guid.NewGuid();

        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);
        var apiException = new ApiException("New api exception")
        {
            ResponseStatusCode = 422
        };

        adapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<WorkItem>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
            .ThrowsForAnyArgs(apiException);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        try
        {
            var result = await clmTaskService.PostUnassignTaskAsync(accountGuid, taskGuid);
        }
        catch (ApiException e)
        {
            Assert.NotNull(e);
            Assert.Equal("New api exception", e.Message);
            Assert.Equal(422, e.ResponseStatusCode);
        }
    }

    [Fact]
    public async Task PostAssignTaskAsync()
    {
        var assigneeGuid = Guid.NewGuid().ToString();
        var taskGuid = Guid.NewGuid();
        var accountGuid = Guid.NewGuid();

        var workItem = new WorkItem
        {
            Name = "Task 1",
            Assignee = new User
            {
                Href = $"{_mockedRequestContextService.Object.ClmApiBaseUrl}v2/{accountGuid}/members/{assigneeGuid}"
            }
        };

        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);

        adapter.SendAsync(
           Arg.Any<RequestInformation>(),
           Arg.Any<ParsableFactory<WorkItem>>(),
           Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
           Arg.Any<CancellationToken>())
           .ReturnsForAnyArgs(workItem);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        var result = await clmTaskService.PostAssignTaskAsync(accountGuid, taskGuid, assigneeGuid);

        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task PostAssignTaskAsyncValidationException()
    {
        var assigneeGuid = Guid.NewGuid().ToString();
        var taskGuid = Guid.NewGuid();
        var accountGuid = Guid.NewGuid();

        var workItem = new WorkItem
        {
            Name = "Task 1",
            Assignee = new User
            {
                Href = $"{_mockedRequestContextService.Object.ClmApiBaseUrl}v2/{accountGuid}/members/{Guid.NewGuid()}"
            }
        };

        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);

        adapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<WorkItem>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
            .ReturnsForAnyArgs(workItem);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);

        try
        {
            var result = await clmTaskService.PostAssignTaskAsync(accountGuid, taskGuid, assigneeGuid);
        }
        catch (ValidationException e)
        {
            Assert.NotNull(e);
            Assert.Equal("Href of returned workitem must match requested href", e.Message);
        }
    }

    [Fact]
    public async Task PostAssignTaskAsyncNullValidationException()
    {
        var assigneeGuid = Guid.NewGuid().ToString();
        var taskGuid = Guid.NewGuid();
        var accountGuid = Guid.NewGuid();

        var workItem = new WorkItem
        {
            Name = "Task 1",
            Assignee = new User
            {
                Href = null
            }
        };

        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);

        adapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<WorkItem>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
            .ReturnsForAnyArgs(workItem);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);

        try
        {
            var result = await clmTaskService.PostAssignTaskAsync(accountGuid, taskGuid, assigneeGuid);
        }
        catch (ValidationException e)
        {
            Assert.NotNull(e);
            Assert.Equal("Href of returned workitem must not be null", e.Message);
        }
    }

    [Fact]
    public async Task PostAssignTaskAsyncUnprocessableException()
    {
        var assigneeGuid = Guid.NewGuid().ToString();
        var taskGuid = Guid.NewGuid();
        var accountGuid = Guid.NewGuid();

        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);
        var apiException = new ApiException("New api exception")
        {
            ResponseStatusCode = 422
        };

        adapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<WorkItem>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
            .ThrowsForAnyArgs(apiException);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);

        try
        {
            var result = await clmTaskService.PostAssignTaskAsync(accountGuid, taskGuid, assigneeGuid);
        }
        catch (ApiException e)
        {
            Assert.NotNull(e);
            Assert.Equal("New api exception", e.Message);
            Assert.Equal(422, e.ResponseStatusCode);
        }
    }

    [Fact]
    public async Task GetTasksWithUrlAsync()
    {
        var taskSummary = new TaskSummary
        {
            WorkItems =
            [
                new TaskSummaryWorkItem
                {
                    Id = new Guid("d1d17bf2-780f-f011-b886-089df4b97061"),
                    Name = "Task 1",
                    Source = "CLMWorkflow",
                    Type = HumanActivityConstants.ApproveDocumentsActivity,
                    Documents =
                    [
                        new Document
                        {
                            Uid = Guid.NewGuid().ToString(),
                            CreatedDate = DateTimeOffset.Now,
                            Name = "Document 1",
                        }
                    ],
                    WorkItemUrl = "https://localhost/atlas/Task/Choice?aid=*********&wfid=d1d17bf2-780f-f011-b886-089df4b97061"
                },
                new TaskSummaryWorkItem
                {
                    Id = new Guid("d1d17bf2-780f-f011-b886-089df4b97061"),
                    Name = "Task 2",
                    Source = "CLMWorkflow",
                    Type = HumanActivityConstants.ChoiceActivity,
                    Documents =
                    [
                        new Document
                        {
                            Uid = Guid.NewGuid().ToString(),
                            CreatedDate = DateTimeOffset.Now,
                            Name = "Document 2",
                        }
                    ],
                    WorkItemUrl = "https://localhost/atlas/Documents/DocExplorer?aid=*********&Id=&wfid=d1d17bf2-780f-f011-b886-089df4b97061&ru=https%3A%2F%2Flocalhost%2Fatlas%2Fbpm%2FWorkList.aspx%3Faid%3D*********"
                },
                new TaskSummaryWorkItem
                {
                    Id = new Guid("87a982ea-c514-f011-810b-005056843ad4"),
                    Name = "Task 3",
                    Source = "CLMWorkflow",
                    Type = HumanActivityConstants.FullPageFillFormActivity,
                    Documents =
                    [
                        new Document
                        {
                            Uid = Guid.NewGuid().ToString(),
                            CreatedDate = DateTimeOffset.Now,
                            Name = "Document 2",
                        }
                    ],
                    WorkItemUrl = "https://qarna11.springcm.com/atlas/bpm/actions/FullPageFillForm.aspx?iter=&wfid=87a982ea-c514-f011-810b-005056843ad4&ldUid=aeee6f91-bf14-f011-a8b2-00505684a9b4&ru=https%3A%2F%2Fqarna11.springcm.com%2Fatlas%2Fbpm%2FWorkList.aspx%3Faid%3D197837"
                },
                new TaskSummaryWorkItem
                {
                    Id = new Guid("53ddedb8-bf14-f011-810b-005056843ad4"),
                    Name = "Task 4",
                    Source = "CLMWorkflow",
                    Type = HumanActivityConstants.EditFormActivity,
                    Documents =
                    [
                        new Document
                        {
                            Uid = Guid.NewGuid().ToString(),
                            CreatedDate = DateTimeOffset.Now,
                            Name = "Document 2",
                        }
                    ],
                    WorkItemUrl = "https://qarna11.springcm.com/atlas/Documents/Preview.aspx?aid=197837&wfid=53ddedb8-bf14-f011-810b-005056843ad4&iter=&ldUid=aeee6f91-bf14-f011-a8b2-00505684a9b4&ru=https%3A%2F%2Fqarna11.springcm.com%2Fatlas%2Fbpm%2FWorkList.aspx%3Faid%3D197837"
                },
                new TaskSummaryWorkItem
                {
                    Id = new Guid("cd4dfd59-b56f-ef11-b86b-089df4b97061"),
                    Name = "Task 5",
                    Source = "CLMWorkflow",
                    Type = HumanActivityConstants.RoutingActivity,
                    Documents =
                    [
                        new Document
                        {
                            Uid = Guid.NewGuid().ToString(),
                            CreatedDate = DateTimeOffset.Now,
                            Name = "Document 2",
                        }
                    ],
                    WorkItemUrl = "https://localhost/atlas/Documents/View?aid=*********&Id=c1813510-db6e-ef11-b86b-089df4b97061&taskUid=cd4dfd59-b56f-ef11-b86b-089df4b9706"
                }
            ]
        };

        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);

        adapter.SendAsync(
           Arg.Any<RequestInformation>(),
           Arg.Any<ParsableFactory<TaskSummary>>(),
           Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
           Arg.Any<CancellationToken>())
           .ReturnsForAnyArgs(taskSummary);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        var result = await clmTaskService.GetTasksAsync(Guid.NewGuid(), new TaskFilter { });

        Assert.NotNull(result);
        Assert.True(result.Count == 5);

        var task1 = result[0];
        Assert.Empty(task1.Assignees);
        Assert.True(string.IsNullOrEmpty(task1.WorkflowName));
        Assert.True(string.IsNullOrEmpty(task1.Description));
        Assert.True(result.GetType() == typeof(List<UserTask>));
        Assert.True(task1.Source == TaskSource.ClmWorkflow);
        Assert.True(task1.Type == TaskType.Approve);
        Assert.True(task1.Name == "Task 1");
        Assert.True(task1.Title == "Task 1");
        Assert.True(task1.Assignor == null);
        Assert.True(task1.NavigationUrl == "https://localhost/atlas/workflow/tasks?wfid=d1d17bf2-780f-f011-b886-089df4b97061");

        var task2 = result[1];
        Assert.Empty(task2.Assignees);
        Assert.True(string.IsNullOrEmpty(task2.WorkflowName));
        Assert.True(string.IsNullOrEmpty(task2.Description));
        Assert.True(result.GetType() == typeof(List<UserTask>));
        Assert.True(task2.Source == TaskSource.ClmWorkflow);
        Assert.True(task2.Type == TaskType.Choice);
        Assert.True(task2.Name == "Task 2");
        Assert.True(task2.Title == "Task 2");
        Assert.True(task2.Assignor == null);
        Assert.True(task2.NavigationUrl == "https://localhost/atlas/workflow/tasks?wfid=d1d17bf2-780f-f011-b886-089df4b97061");

        var task3 = result[2];
        Assert.Empty(task3.Assignees);
        Assert.True(string.IsNullOrEmpty(task3.WorkflowName));
        Assert.True(string.IsNullOrEmpty(task3.Description));
        Assert.True(result.GetType() == typeof(List<UserTask>));
        Assert.True(task3.Source == TaskSource.ClmWorkflow);
        Assert.True(task3.Type == TaskType.FullPageFillForm);
        Assert.True(task3.Name == "Task 3");
        Assert.True(task3.Title == "Task 3");
        Assert.True(task3.Assignor == null);
        Assert.True(task3.NavigationUrl == "https://qarna11.springcm.com/atlas/workflow/tasks?wfid=87a982ea-c514-f011-810b-005056843ad4");

        var task4 = result[3];
        Assert.Empty(task4.Assignees);
        Assert.True(string.IsNullOrEmpty(task4.WorkflowName));
        Assert.True(string.IsNullOrEmpty(task4.Description));
        Assert.True(result.GetType() == typeof(List<UserTask>));
        Assert.True(task4.Source == TaskSource.ClmWorkflow);
        Assert.True(task4.Type == TaskType.EditForm);
        Assert.True(task4.Name == "Task 4");
        Assert.True(task4.Title == "Task 4");
        Assert.True(task4.Assignor == null);
        Assert.True(task4.NavigationUrl == "https://qarna11.springcm.com/atlas/workflow/tasks?wfid=53ddedb8-bf14-f011-810b-005056843ad4");

        var task5 = result[4];
        Assert.Empty(task5.Assignees);
        Assert.True(string.IsNullOrEmpty(task5.WorkflowName));
        Assert.True(string.IsNullOrEmpty(task5.Description));
        Assert.True(result.GetType() == typeof(List<UserTask>));
        Assert.True(task5.Source == TaskSource.ClmWorkflow);
        Assert.True(task5.Type == TaskType.Routing);
        Assert.True(task5.Name == "Task 5");
        Assert.True(task5.Title == "Task 5");
        Assert.True(task5.Assignor == null);
        Assert.True(task5.NavigationUrl == "https://localhost/atlas/workflow/tasks?wfid=cd4dfd59-b56f-ef11-b86b-089df4b97061");
    }

    [Fact]
    public async Task GetTasksWithTaskSortAsync()
    {
        var groupId = Guid.NewGuid();
        var taskSummary = new TaskSummary
        {
            WorkItems =
            [
                new TaskSummaryWorkItem
                {
                    Id = Guid.NewGuid(),
                    Name = "Task 1",
                    Source = "CLMWorkflow",
                    Type = HumanActivityConstants.ApproveDocumentsActivity,
                    Documents =
                    [
                        new Document
                        {
                            Uid = Guid.NewGuid().ToString(),
                            CreatedDate = DateTimeOffset.Now,
                            Name = "Document 1",
                        }
                    ],
                    WorkflowQueueId = groupId,
                }
            ],
            WorkflowQueues =
            [
                new TaskSummaryTaskGroup
                {
                    Name = "Group 1",
                    Id = groupId,
                    Permissions = new TaskGroupPermissions()
                    {
                        CanAssign = true,
                        CanClaim = true,
                        CanUnclaim = true
                    },
                    Members =
                    [
                        new TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskGroupMember
                        {
                            FullName = "Test User",
                            Id = Guid.NewGuid()
                        },
                        new TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskGroupMember
                        {
                            FullName = "Test User 2",
                            Id = Guid.NewGuid()
                        }

                    ]
                }
            ]
        };

        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);

        adapter.SendAsync(
           Arg.Is<RequestInformation>(r => r.RequestOptions.ToString().Contains("TaskAssignedDate") && r.RequestOptions.ToString().Contains("PageSize")),
           Arg.Any<ParsableFactory<TaskSummary>>(),
           Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
           Arg.Any<CancellationToken>())
           .ReturnsForAnyArgs(taskSummary);

        var clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        var result = await clmTaskService.GetTasksAsync(Guid.NewGuid(), new TaskFilter
        {
            GroupAssignees =
            [
                groupId.ToString(),
                "123"
            ],
            TaskSort = new TaskSort
            {
                ResultCount = 2,
                SortColumn = SortColumn.AssignedDate
            }
        });

        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Empty(result[0].Assignees);
        Assert.True(string.IsNullOrEmpty(result[0].WorkflowName));
        Assert.True(string.IsNullOrEmpty(result[0].NavigationUrl));
        Assert.True(string.IsNullOrEmpty(result[0].Description));
        Assert.True(result.GetType() == typeof(List<UserTask>));
        Assert.True(result[0].Source == TaskSource.ClmWorkflow);
        Assert.True(result[0].Type == TaskType.Approve);
        Assert.True(result[0].Name == "Task 1");
        Assert.True(result[0].Title == "Task 1");
        Assert.True(result[0].Assignor == null);
        Assert.Single(result[0].Agreements);

        adapter.SendAsync(
                Arg.Is<RequestInformation>(r => r.RequestOptions.ToString().Contains("TaskSentDate")),
                Arg.Any<ParsableFactory<TaskSummary>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
            .ReturnsForAnyArgs(taskSummary);

        clmTaskService = new ClmTaskService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        result = await clmTaskService.GetTasksAsync(Guid.NewGuid(), new TaskFilter
        {
            GroupAssignees =
            [
                groupId.ToString(),
                "123"
            ],
            TaskSort = new TaskSort
            {
                SortColumn = SortColumn.DueDate
            }
        });

        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Empty(result[0].Assignees);
        Assert.True(string.IsNullOrEmpty(result[0].WorkflowName));
        Assert.True(string.IsNullOrEmpty(result[0].NavigationUrl));
        Assert.True(string.IsNullOrEmpty(result[0].Description));
        Assert.True(result.GetType() == typeof(List<UserTask>));
        Assert.True(result[0].Source == TaskSource.ClmWorkflow);
        Assert.True(result[0].Type == TaskType.Approve);
        Assert.True(result[0].Name == "Task 1");
        Assert.True(result[0].Title == "Task 1");
        Assert.True(result[0].Assignor == null);
        Assert.Single(result[0].Agreements);
    }
}
