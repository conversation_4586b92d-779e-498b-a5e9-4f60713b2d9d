﻿using MediatR;

using OpenTelemetry.Resources;

using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

namespace TaskManagementService.Core.Handlers;

public class GetUserTaskHistoryQueryHandler : IRequestHandler<GetUserTaskHistoryQuery, TaskHistory>
{
    private readonly ILogger<GetUserTaskHistoryQueryHandler> _logger;
    private readonly IClmTaskService _clmTaskServices;
    private readonly IESignTaskService _eSignTaskService;

    public GetUserTaskHistoryQueryHandler(ILogger<GetUserTaskHistoryQueryHandler> logger, IClmTaskService clmTaskServices, IESignTaskService eSignTaskService)
    {
        _logger = logger;
        _clmTaskServices = clmTaskServices;
        _eSignTaskService = eSignTaskService;
    }

    public async Task<TaskHistory> Handle(GetUserTaskHistoryQuery request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));

        _logger.LogInformation("Attempting to retrieve task history for {Source} task: {TaskId}", request.TaskSource, request.TaskId);
        switch (request.TaskSource)
        {
            case TaskSource.ClmWorkflow:
                return await _clmTaskServices.GetClmTaskHistoryAsync(request.AccountId, request.TaskId);
            case TaskSource.ESignature:
                return await _eSignTaskService.GetEnvelopeHistoryAsync(request.AccountId, request.TaskId, request.Locale, cancellationToken);
            case TaskSource.IdVerification:
                break;
            case TaskSource.AssignmentsApi:
                break;
            default:
                _logger.LogError("Incorrect Source given when trying to retrieve Task History: {Source}", request.TaskSource);
                break;
        }

        return new TaskHistory();
    }
}
