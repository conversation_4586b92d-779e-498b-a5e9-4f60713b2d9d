﻿{
  "$schema": "https://raw.githubusercontent.com/DotNetAnalyzers/StyleCopAnalyzers/master/StyleCop.Analyzers/StyleCop.Analyzers/Settings/stylecop.schema.json",
  "settings": {
    "documentationRules": {
      "companyName": "DocuSign Incorporated",
      "documentPrivateFields": false,
      "documentExposedElements": false,
      "documentInterfaces": false,
      "documentInternalElements": false,
      "documentPrivateElements": false,
      "documentationCulture": "en-US",
      "xmlHeader": false
    },
    "layoutRules": {
      "newlineAtEndOfFile": "require"
    },
    "indentation": {
      "indentationSize": 4,
      "useTabs": false
    },
    "namingRules": {},
    "orderingRules": {
      "systemUsingDirectivesFirst": true,
      "usingDirectivesPlacement": "outsideNamespace",
      "blankLinesBetweenUsingGroups": "require"
    }
  }
}