// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class Filter : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The compareSentInitialDate property</summary>
        public bool? CompareSentInitialDate { get; set; }
        /// <summary>The fields property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Field>? Fields { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Field> Fields { get; set; }
#endif
        /// <summary>The gate property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Gate { get; set; }
#nullable restore
#else
        public string Gate { get; set; }
#endif
        /// <summary>The includedEnvelopeStatuses property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<string>? IncludedEnvelopeStatuses { get; set; }
#nullable restore
#else
        public List<string> IncludedEnvelopeStatuses { get; set; }
#endif
        /// <summary>The recipientNameSubstring property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? RecipientNameSubstring { get; set; }
#nullable restore
#else
        public string RecipientNameSubstring { get; set; }
#endif
        /// <summary>The subjectSubstring property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? SubjectSubstring { get; set; }
#nullable restore
#else
        public string SubjectSubstring { get; set; }
#endif
        /// <summary>The timeRanges property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.TimeRange>? TimeRanges { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.TimeRange> TimeRanges { get; set; }
#endif
        /// <summary>The userIds property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<Guid?>? UserIds { get; set; }
#nullable restore
#else
        public List<Guid?> UserIds { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Filter"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Filter CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Filter();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "compareSentInitialDate", n => { CompareSentInitialDate = n.GetBoolValue(); } },
                { "fields", n => { Fields = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Field>(global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Field.CreateFromDiscriminatorValue)?.AsList(); } },
                { "gate", n => { Gate = n.GetStringValue(); } },
                { "includedEnvelopeStatuses", n => { IncludedEnvelopeStatuses = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
                { "recipientNameSubstring", n => { RecipientNameSubstring = n.GetStringValue(); } },
                { "subjectSubstring", n => { SubjectSubstring = n.GetStringValue(); } },
                { "timeRanges", n => { TimeRanges = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.TimeRange>(global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.TimeRange.CreateFromDiscriminatorValue)?.AsList(); } },
                { "userIds", n => { UserIds = n.GetCollectionOfPrimitiveValues<Guid?>()?.AsList(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteBoolValue("compareSentInitialDate", CompareSentInitialDate);
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Field>("fields", Fields);
            writer.WriteStringValue("gate", Gate);
            writer.WriteCollectionOfPrimitiveValues<string>("includedEnvelopeStatuses", IncludedEnvelopeStatuses);
            writer.WriteStringValue("recipientNameSubstring", RecipientNameSubstring);
            writer.WriteStringValue("subjectSubstring", SubjectSubstring);
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.TimeRange>("timeRanges", TimeRanges);
            writer.WriteCollectionOfPrimitiveValues<Guid?>("userIds", UserIds);
        }
    }
}
#pragma warning restore CS0618
