using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using Moq;

using TaskManagementService.Core.Handlers;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Queries;

using Xunit;

namespace TaskManagementService.Tests.Handlers;

[Trait("TestType", "UnitTest")]
public class AssignTaskCommandHandlerTest
{
    [Fact]
    public void Handle()
    {
        var mockLogger = new Mock<ILogger<AssignTaskCommandHandler>>();
        var mockClmTaskService = new Mock<IClmTaskService>();

        var mockAccountId = new Guid("********-0004-0001-000b-************");
        var mockTaskId = new Guid("********-0004-0001-000b-************");
        var mockAssigneeId = "********-0004-0001-000b-************";

        mockClmTaskService.Setup(x => x.PostAssignTaskAsync(mockAccountId, mockTaskId, mockAssigneeId))
            .Returns(Task.FromResult(new List<string>()));

        var mockAssignTaskCommand = new AssignTaskCommand(mockAccountId, mockTaskId, mockAssigneeId);

        var assignTaskCommandHandler = new AssignTaskCommandHandler(mockLogger.Object, mockClmTaskService.Object);

        var result = assignTaskCommandHandler.Handle(mockAssignTaskCommand, System.Threading.CancellationToken.None);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task NullHandle()
    {
        var mockLogger = new Mock<ILogger<AssignTaskCommandHandler>>();
        var mockClmTaskService = new Mock<IClmTaskService>();

        var assignTaskCommandHandler = new AssignTaskCommandHandler(mockLogger.Object, mockClmTaskService.Object);

        await Assert.ThrowsAsync<ArgumentNullException>(() => assignTaskCommandHandler.Handle(null, System.Threading.CancellationToken.None));
    }
}
