﻿using MediatR;

using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Queries;

namespace TaskManagementService.Core.Handlers;

public class VoidESignEnvelopeCommandHandler : IRequestHandler<VoidESignEnvelopeCommand, Unit>
{
    private readonly ILogger<VoidESignEnvelopeCommandHandler> _logger;
    private readonly IESignTaskService _eSignTaskServices;

    public VoidESignEnvelopeCommandHandler(ILogger<VoidESignEnvelopeCommandHandler> logger, IESignTaskService eSignTaskServices)
    {
        _logger = logger;
        _eSignTaskServices = eSignTaskServices;
    }

    public async Task<Unit> Handle(VoidESignEnvelopeCommand request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));

        _logger.LogInformation("Attempting to void envelope: {TaskId}", request.TaskId);
        await _eSignTaskServices.VoidEnvelopeAsync(request.AccountId, request.TaskId, request.VoidedReason);
        _logger.LogInformation("After void attempt for envelope: {TaskId}", request.TaskId);

        return Unit.Value;
    }
}
