replicaCount: 1
autoscaling:
  minReplicas: 1
  maxReplicas: 2

environment:
  name: Dev
  index: 0

env:
  envOverrides:
    - name: "ASPNETCORE_ENVIRONMENT"
      value: "Development"
    # Workaround for rosetta emulation issues in dotnet 8 https://github.com/dotnet/runtime/issues/94909
    - name: "DOTNET_EnableWriteXorExecute"
      value: "0"
    - name: "OptionalFeatures__EnableRedis"
      value: "false"
    - name: "ConnectionStrings__RedisConnectionString"
      value: "redis:6379,abortConnect=false,ssl=false"

# Mount secrets from Azure Key Vault
secretProvider:
  enabled: false

# Configure virtual service
virtualService:
  validation:
    skipList: ['task-management-service-{YourFirstName}-{YourLastName}']
periodicTests:
  enabled: false

publicApi:
  enabled: true
internalApi:
  enabled: false
authorization:
  allow:
    enabled: true
    rules:
      - from:
          - source:
              principals:
                - cluster.local/ns/istio-system/sa/istio-ingressgateway-service-account
          - source:
              principals:
                - "cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Release.Name }}-serviceaccount"
          - source:
              namespaces: ["{{ .Release.Namespace }}"]

authentication:
  enabled: true
  jwtRules:
    - issuer: "https://services.dev.docusign.net/id-authentication-gateway"
      jwksUri: https://services.dev.docusign.net/id-authentication-gateway/v1/openid-configuration/jwks
      forwardOriginalToken: true

rollout:
  canary:
    strategy:
      steps:
        - setCanaryScale:
            replicas: 1
        - setWeight: 0
        - setCanaryScale:
            matchTrafficWeight: true
        - setWeight: 50
        - pause: {duration: 5s}
        - setWeight: 100
