using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using Moq;

using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Handlers;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

using Xunit;

using TaskGroupMember = TaskManagementService.Core.Models.TaskGroupMember;

namespace TaskManagementService.Tests.Handlers;

[Trait("TestType", "UnitTest")]
public class GetUserTasksGroupsQueryHandlerTest
{
    [Fact]
    public async Task Handle()
    {
        var mockLogger = new Mock<ILogger<GetUserTasksGroupsQueryHandler>>();
        var mockTaskService = new Mock<ITaskService>();

        var mockAccountId = new Guid("*************-0001-000b-************");
        var mockTaskGroupId = new Guid("*************-0001-000b-************");
        var mockTaskGroupMemberId = new Guid("********-0004-0001-000b-************");
        var mockTaskGroupName = "test";
        var mockTaskGroupMemberName = "member name";
        var mockTaskGroupMember = new TaskGroupMember { Id = mockTaskGroupMemberId, FullName = mockTaskGroupMemberName };

        var mockGetUserTasksGroupsQuery =
            new GetUserTasksGroupsQuery(mockAccountId, false);

        var mockTaskGroup = new TaskGroup() { Id = mockTaskGroupId.ToString(), Name = mockTaskGroupName, Members = [mockTaskGroupMember], Source = TaskSource.ClmWorkflow };

        mockTaskService
            .Setup(x => x.GetTasksGroupsAsync(mockAccountId, false))
            .Returns(Task.FromResult(new List<TaskGroup> { mockTaskGroup }));

        var getUserTasksGroupsQueryHandler =
            new GetUserTasksGroupsQueryHandler(mockLogger.Object, [mockTaskService.Object]);

        var result = await getUserTasksGroupsQueryHandler.Handle(mockGetUserTasksGroupsQuery, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Single(result);
        var taskGroup = result[0];
        Assert.Equal(mockTaskGroupId.ToString(), taskGroup.Id);
        Assert.Equal(mockTaskGroupName, taskGroup.Name);
        Assert.Equal(TaskSource.ClmWorkflow, taskGroup.Source);

        var members = taskGroup.Members.ToList();
        Assert.Single(members);
        var member = members[0];
        Assert.Equal(mockTaskGroupMemberId, member.Id);
        Assert.Equal(mockTaskGroupMemberName, member.FullName);
    }

    [Fact]
    public async Task NullHandle()
    {
        var mockLogger = new Mock<ILogger<GetUserTasksGroupsQueryHandler>>();
        var mockTaskService = new Mock<ITaskService>();

        var getUserTasksGroupsQueryHandler =
            new GetUserTasksGroupsQueryHandler(mockLogger.Object, [mockTaskService.Object]);

        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            getUserTasksGroupsQueryHandler.Handle(null, CancellationToken.None));
    }
}
