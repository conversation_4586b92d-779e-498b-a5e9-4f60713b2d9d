﻿using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace TaskManagementService.Infrastructure.Serialization;

internal sealed class ParseStringConverter : JsonConverter<long>
{
    public static readonly ParseStringConverter Singleton = new();

    public override bool CanConvert(Type t) => t == typeof(long);

    public override long Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var value = reader.GetString();
        return long.TryParse(value, out var l) ? l : 0;
    }

    public override void Write(Utf8JsonWriter writer, long value, JsonSerializerOptions options) =>
        JsonSerializer.Serialize(writer, value.ToString(CultureInfo.InvariantCulture), options);
}
