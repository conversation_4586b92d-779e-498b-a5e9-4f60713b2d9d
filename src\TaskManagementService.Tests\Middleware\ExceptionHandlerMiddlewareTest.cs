﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Kiota.Abstractions;

using Moq;

using TaskManagementService.Core.Exceptions;
using TaskManagementService.Middleware;

using Xunit;

namespace TaskManagementService.Tests.Middleware;

[Trait("TestType", "UnitTest")]
[SuppressMessage("Usage", "CA2201:Do not raise reserved exception types", Justification = "In tests it's ok to use reserved exception types")]
public class ExceptionHandlerMiddlewareTest
{
    private readonly Mock<ILogger<ExceptionHandlerMiddleware>> _loggerMock = new();

    [Fact]
    public async Task CatchAnyException()
    {
        // Arrange
        var httpContext = new DefaultHttpContext();
        var memoryStream = new MemoryStream();
        httpContext.Response.Body = memoryStream;

        var middleware = new ExceptionHandlerMiddleware((_) => throw new Exception(), _loggerMock.Object);

        // Act
        await middleware.InvokeAsync(httpContext);

        // Assert
        var errorDetails = await GetErrorDetailsAsync(memoryStream);
        Assert.Equal(StatusCodes.Status500InternalServerError, httpContext.Response.StatusCode);
        Assert.Equal("An error occurred while processing your request.", errorDetails.UserMessage);
    }

    [Fact]
    public async Task CatchApiException()
    {
        // Arrange
        const int statusCode = StatusCodes.Status403Forbidden;
        var httpContext = new DefaultHttpContext();
        var memoryStream = new MemoryStream();
        httpContext.Response.Body = memoryStream;

        var middleware = new ExceptionHandlerMiddleware((_) => throw new ApiException { ResponseStatusCode = statusCode }, _loggerMock.Object);

        // Act
        await middleware.InvokeAsync(httpContext);

        // Assert
        var errorDetails = await GetErrorDetailsAsync(memoryStream);
        Assert.Equal(statusCode, httpContext.Response.StatusCode);
        Assert.Equal("An error occurred while processing your request.", errorDetails.UserMessage);
    }

    [Fact]
    public async Task CatchValidationException()
    {
        // Arrange
        const string validationMessage = "Signature is incorrect, please replace";
        var httpContext = new DefaultHttpContext();
        var memoryStream = new MemoryStream();
        httpContext.Response.Body = memoryStream;

        var middleware = new ExceptionHandlerMiddleware((_) => throw new ValidationException(validationMessage), _loggerMock.Object);

        // Act
        await middleware.InvokeAsync(httpContext);

        // Assert
        var errorDetails = await GetErrorDetailsAsync(memoryStream);
        Assert.Equal(StatusCodes.Status400BadRequest, httpContext.Response.StatusCode);
        Assert.Equal(validationMessage, errorDetails.UserMessage);
    }

    [Fact]
    public async Task CatchNotSupportedExceptionException()
    {
        // Arrange
        const string validationMessage = "This action is not supported for current user.";
        var httpContext = new DefaultHttpContext();
        var memoryStream = new MemoryStream();
        httpContext.Response.Body = memoryStream;

        var middleware = new ExceptionHandlerMiddleware((_) => throw new NotSupportedException(validationMessage), _loggerMock.Object);

        // Act
        await middleware.InvokeAsync(httpContext);

        // Assert
        var errorDetails = await GetErrorDetailsAsync(memoryStream);
        Assert.Equal(StatusCodes.Status400BadRequest, httpContext.Response.StatusCode);
        Assert.Equal(validationMessage, errorDetails.UserMessage);
    }

    [Fact]
    public async Task CatchTaskManagementServiceException()
    {
        // Arrange
        var httpContext = new DefaultHttpContext();
        var memoryStream = new MemoryStream();
        httpContext.Response.Body = memoryStream;

        var errorDetails = new ErrorDetails
        {
            ErrorCode = "METHOD_NOT_ALLOWED",
            HttpStatusCode = StatusCodes.Status405MethodNotAllowed,
            UserMessage = "Some message to show to the user",
            DeveloperMessage = "Some message to log as an error",
        };
        var middleware = new ExceptionHandlerMiddleware((_) => throw new TaskManagementServiceException(errorDetails), _loggerMock.Object);

        // Act
        await middleware.InvokeAsync(httpContext);

        // Assert
        var errorDetailsActual = await GetErrorDetailsAsync(memoryStream);
        Assert.Equal(errorDetails.HttpStatusCode, httpContext.Response.StatusCode);
        Assert.Equal(errorDetails.ErrorCode, errorDetailsActual.ErrorCode);
        Assert.Equal(errorDetails.UserMessage, errorDetailsActual.UserMessage);
        Assert.Null(errorDetailsActual.HttpStatusCode);
        Assert.Null(errorDetailsActual.DeveloperMessage);
    }

    private static async Task<ErrorDetails> GetErrorDetailsAsync(MemoryStream stream)
    {
        stream.Position = 0;
        using var reader = new StreamReader(stream);
        var responseBody = await reader.ReadToEndAsync();
        Assert.NotEmpty(responseBody);
        var errorDetails = JsonSerializer.Deserialize<ErrorDetails>(responseBody);
        Assert.NotNull(errorDetails);
        return errorDetails;
    }
}
