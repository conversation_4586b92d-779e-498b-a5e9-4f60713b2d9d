<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <CentralPackageTransitivePinningEnabled>true</CentralPackageTransitivePinningEnabled>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="AccordProject.Concerto" Version="0.6.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.Redis" Version="8.0.1" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
    <PackageVersion Include="AutoFixture" Version="4.18.1" />
    <PackageVersion Include="coverlet.collector" Version="6.0.4" />
    <PackageVersion Include="coverlet.msbuild" Version="6.0.4" />
    <PackageVersion Include="DocuSign.AccountServer.TokenExchangeClient" Version="1.0.50" />
    <PackageVersion Include="DocuSign.Adm.Common.AspNet.OAuth" Version="0.1.27.556" />
    <PackageVersion Include="DocuSign.eSign.dll" Version="8.3.0" />
    <PackageVersion Include="DocuSign.Msf.AspNet.HeaderPropagation" Version="1.1.14" />
    <PackageVersion Include="DocuSign.Msf.AspNet.OpenTelemetry.SemanticConventions" Version="1.4.29" />
    <PackageVersion Include="DocuSign.Msf.AspNet.OpenTelemetry" Version="1.4.29" />
    <PackageVersion Include="DocuSign.Msf.AspNet.VersionEndpoint" Version="1.2.9" />
    <PackageVersion Include="DocuSign.OneConfig.CodeGen.Build" Version="7.0.22" />
    <PackageVersion Include="DocuSign.OneConfig.Core" Version="7.0.22" />
    <PackageVersion Include="DocuSign.OneConfig.Extensions.DotNet" Version="7.0.22" />
    <PackageVersion Include="DocuSign.OneConfig.Extensions.Msf" Version="7.0.22" />
    <PackageVersion Include="DocuSign.Platform.Extensions.Configuration.FileExtensions" Version="6.1.2" />
    <PackageVersion Include="DocuSign.Platform.Runtime" Version="23.2.0.39846" />
    <PackageVersion Include="Flurl.Http" Version="4.0.2" />
    <PackageVersion Include="Mapster" Version="7.4.0" />
    <PackageVersion Include="Mapster.DependencyInjection" Version="1.0.1" />
    <PackageVersion Include="MediatR" Version="12.5.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.20" />
    <PackageVersion Include="Microsoft.AspNetCore.HeaderPropagation" Version="8.0.20" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.20" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Hybrid" Version="9.1.0-preview.1.25064.3" />
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Http.Resilience" Version="8.10.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.14.0" />
    <PackageVersion Include="Microsoft.Kiota.Authentication.Azure" Version="1.19.1" />
    <PackageVersion Include="Microsoft.Kiota.Bundle" Version="1.19.1" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageVersion Include="Moq" Version="[4.20.72]" />
    <PackageVersion Include="Moq.Contrib.HttpClient" Version="1.4.0" />
    <!-- Update package version with care to ensure SponsorLink is not enabled. -->
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.4" />
    <PackageVersion Include="NSubstitute" Version="5.3.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.GrpcNetClient" Version="1.9.0-beta.1" />
    <PackageVersion Include="Polly.Contrib.WaitAndRetry" Version="1.1.1" />
    <PackageVersion Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageVersion Include="Polly" Version="8.6.3" />
    <PackageVersion Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageVersion Include="Snapshooter.Xunit" Version="1.0.1" />
    <PackageVersion Include="StyleCop.Analyzers" Version="1.2.0-beta.556" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="7.3.2" />
    <PackageVersion Include="Swashbuckle.AspNetCore.Annotations" Version="7.3.2" />
    <PackageVersion Include="System.Drawing.Common" Version="8.0.20" />
    <PackageVersion Include="System.Text.Encodings.Web" Version="8.0.0" />
    <PackageVersion Include="System.Text.Json" Version="8.0.6" />
    <PackageVersion Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageVersion Include="xRetry" Version="1.9.0" />
    <PackageVersion Include="xunit.console" Version="2.9.3" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.1.4" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="DocuSign.Platform.Storage.Clients.Redis.ConnectionInfo" Version="6.1.2" />
    <PackageVersion Include="Microsoft.Azure.StackExchangeRedis" Version="3.2.1" />
    <PackageVersion Include="Azure.ResourceManager.Redis" Version="1.5.1" />
  </ItemGroup>
  <ItemGroup>
    <PackageVersion Include="Microsoft.VisualStudio.Threading.Analyzers" Version="17.12.19" />
    <PackageVersion Include="Microsoft.VisualStudio.SlnGen" Version="12.0.4" />
    <PackageVersion Include="Microsoft.SourceLink.GitHub" Version="8.0.0" PrivateAssets="All" />
    <PackageVersion Include="AsyncFixer" Version="1.6.0" />
  </ItemGroup>
</Project>
