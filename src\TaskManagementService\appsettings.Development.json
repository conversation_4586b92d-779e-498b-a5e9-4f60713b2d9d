{"Logging": {"LogLevel": {"Default": "Information", "System": "Information", "Grpc": "Information", "Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "None"}}, "ForwardedHeaders": {"AllowedHosts": ["services.dev.docusign.net", "*.services.dev.docusign.net", "*.exp.services.dev.docusign.net"]}, "Security": {"Authorities": ["https://account-tk1.tk.docusign.dev/"]}, "OptionalFeatures": {"EnableRedis": false}, "HealthChecks": {"Optimizely": {"LogOnlyMode": false}}, "ConnectionStrings": {"RedisConnectionString": "localhost:6379,abortConnect=false,ssl=false"}, "OneConfig": {"Optimizely": {"EnableGlobalProject": false, "Projects": {}}}}