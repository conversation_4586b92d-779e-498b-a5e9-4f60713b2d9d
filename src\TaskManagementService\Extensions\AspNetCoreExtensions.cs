﻿using System;
using System.Diagnostics;

using DocuSign.Adm.Utilities;
using DocuSign.Msf.AspNet.OpenTelemetry.SemanticConventions;

using Microsoft.AspNetCore.Http;

namespace TaskManagementService.Extensions;

public static class AspNetCoreExtensions
{
    /// <summary>Enriches an HTTP request activity with additional tags.
    /// </summary>
    public static void EnrichHttpRequest(Activity activity, HttpRequest request)
    {
        ArgumentNullException.ThrowIfNull(activity);
        ArgumentNullException.ThrowIfNull(request);

        if (request.ContentLength.HasValue)
        {
            activity.SetTag(TraceSemanticConventions.AttributeHttpRequestContentLength, request.ContentLength);
        }
    }

    /// <summary>Enriches an HTTP response activity with additional tags.
    /// </summary>
    public static void EnrichHttpResponse(Activity activity, HttpResponse response)
    {
        ArgumentNullException.ThrowIfNull(activity);
        ArgumentNullException.ThrowIfNull(response);

        var accountId = response
                            .HttpContext
                            .Request
                            .RouteValues
                            .TryGetValue("accountId", out var accId) &&
                        Guid.TryParse(accId as string, out var accGuid) ? accGuid : accId;

        var userId = response
                         .HttpContext
                         .Request
                         .RouteValues
                         .TryGetValue("userId", out var usrId) &&
                     Guid.TryParse((string)usrId, out var usrGuid) ? usrGuid : usrId;

        if (accountId is not null)
        {
            activity.SetTag(TraceSemanticConventions.AttributeDocuSignAccountId, accountId);
            activity.SetTag("accountId", accountId);
        }

        if (userId is not null)
        {
            activity.SetTag(TraceSemanticConventions.AttributeDocuSignUserId, userId);
            activity.SetTag("userId", userId);
        }

        if (response.ContentLength.HasValue)
        {
            activity.SetTag(TraceSemanticConventions.AttributeHttpResponseContentLength, response.ContentLength);
        }
    }

    /// <summary>Enriches an HTTP exception activity with additional tags.
    /// </summary>
    public static void EnrichHttpException(Activity activity, Exception ex)
    {
        ArgumentNullException.ThrowIfNull(activity, nameof(activity));
        ArgumentNullException.ThrowIfNull(ex, nameof(ex));

        activity.SetTag(TraceSemanticConventions.AttributeExceptionMessage, ex.GetShortDescription());
        activity.SetTag(TraceSemanticConventions.AttributeExceptionType, ex.GetType().Name);
        activity.SetTag(
            TraceSemanticConventions.AttributeExceptionStacktrace,
            ex.GetShortDescription(includeStack: true));
        activity.SetStatus(ActivityStatusCode.Error);
    }
}
