using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

using DocuSign.Adm.Common.AspNet.OAuth.Claims;

using MediatR;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

using Moq;

using Snapshooter.Xunit;

using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;
using TaskManagementService.Middleware;
using TaskManagementService.Services;

using Xunit;

namespace TaskManagementService.Tests.Middleware;

[Trait("TestType", "UnitTest")]
public class RequestContextMiddlewareTest
{
    private readonly Mock<HttpContext> _contextMock;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly RequestContextMiddleware _middleware;
    private bool _isNextCalled;

    public RequestContextMiddlewareTest()
    {
        var loggerMock = new Mock<ILogger<RequestContextMiddleware>>();
        _contextMock = new Mock<HttpContext>();
        _mediatorMock = new Mock<IMediator>();
        _middleware = new RequestContextMiddleware(
            next: _ =>
                {
                    _isNextCalled = true;
                    return Task.CompletedTask;
                },
            loggerMock.Object);
    }

    [Fact]
    public async Task InvokeAsyncValidContextSetsRequestContextProperties()
    {
        // Arrange
        var claims = new List<Claim>
        {
            new(DocuSignClaimTypes.UserId, new Guid("dfbb2a1b-e54c-4239-948d-c499923764f5").ToString()),
            new(DocuSignClaimTypes.Scope, DocuSignClaimValues.ClmSpringReadScope)
        };
        var claimsIdentity = new ClaimsIdentity(claims, "TestAuthType");
        var claimsPrincipal = new ClaimsPrincipal(claimsIdentity);

        _contextMock.Setup(c => c.User).Returns(claimsPrincipal);
        _contextMock.Setup(c => c.Request.Headers).Returns(new HeaderDictionary
        {
            { "Authorization", "Bearer token" },
            { "TraceParent", "trace-parent" },
            { RequestContextMiddleware.ShardIdHeaderName, "shard-id" }
        });
        _contextMock.Setup(c => c.Request.Path).Returns("/accounts/********-1234-1234-1234-************/");
        _contextMock.Setup(c => c.Request.RouteValues).Returns(new RouteValueDictionary
        {
            { RequestContextMiddleware.AccountIdRouteValueName, "********-1234-1234-1234-************" }
        });

        _mediatorMock.Setup(m => m.Send(It.IsAny<GetUserAccountQuery>(), CancellationToken.None))
            .ReturnsAsync(new UserAccount(new Guid("68cae8dd-68c6-4328-8506-e44937103bc7"), new Guid("1a8b6af4-2994-4ac4-a68a-d030039d8890"), "Test Account", new Uri("https://esign.com"), new Uri("https://clm.com")));

        var requestContext = new RequestContextService();

        // Act
        await _middleware.InvokeAsync(_contextMock.Object, requestContext, _mediatorMock.Object);

        // Assert
        Assert.True(_isNextCalled);
        Snapshot.Match(requestContext);
    }

    [Fact]
    public void GetAccountIdReturnAccountIdUsingRegex()
    {
        // Arrange
        var requestMock = new Mock<HttpRequest>();
        requestMock.Setup(x => x.Headers).Returns(new HeaderDictionary());
        var guid = Guid.NewGuid();
        requestMock.Setup(x => x.Path).Returns(new PathString($"/accounts/{guid}/foo"));
        requestMock.Setup(x => x.RouteValues).Returns(new RouteValueDictionary()
        {
            { RequestContextMiddleware.AccountIdRouteValueName, "123" }
        });
        _contextMock.Setup(x => x.Request).Returns(requestMock.Object);

        // Act
        var result = RequestContextMiddleware.GetAccountId(_contextMock.Object);

        // Assert
        Assert.Equal(guid, result);
    }

    [Fact]
    public void GetDocusignShardIdWithShardIdHeaderReturnsNoShardId()
    {
        // Arrange
        var requestMock = new Mock<HttpRequest>();
        requestMock.Setup(x => x.Headers).Returns(new HeaderDictionary());
        _contextMock.Setup(x => x.Request).Returns(requestMock.Object);

        // Act
        var result = RequestContextMiddleware.GetDocusignShardId(_contextMock.Object);

        // Assert
        Assert.Equal(string.Empty, result);
    }

    [Fact]
    public void GetDocusignShardIdWithShardIdHeaderReturnsShardId()
    {
        // Arrange
        var requestMock = new Mock<HttpRequest>();
        requestMock.Setup(x => x.Headers).Returns(new HeaderDictionary
        {
            { RequestContextMiddleware.ShardIdHeaderName, "shard-id" }
        });
        _contextMock.Setup(x => x.Request).Returns(requestMock.Object);

        // Act
        var result = RequestContextMiddleware.GetDocusignShardId(_contextMock.Object);

        // Assert
        Assert.Equal("shard-id", result);
    }

    [Fact]
    public void GetDocusignShardIdWithLleShardIdHeaderReturnsLleShardId()
    {
        // Arrange
        var requestMock = new Mock<HttpRequest>();
        requestMock.Setup(x => x.Headers).Returns(new HeaderDictionary
        {
            { RequestContextMiddleware.ShardIdHeaderName, string.Empty },
            { RequestContextMiddleware.LleShardIdHeaderName, "lle-shard-id" }
        });
        requestMock.Setup(x => x.Host).Returns(new HostString("example.exp.docusign.com"));
        _contextMock.Setup(x => x.Request).Returns(requestMock.Object);

        // Act
        var result = RequestContextMiddleware.GetDocusignShardId(_contextMock.Object);

        // Assert
        Assert.Equal("lle-shard-id", result);
    }

    [Fact]
    public void GetDocusignShardIdWithHostMatchingRegexReturnsShardIdFromHost()
    {
        // Arrange
        var requestMock = new Mock<HttpRequest>();
        requestMock.Setup(x => x.Headers).Returns(new HeaderDictionary
        {
            { RequestContextMiddleware.ShardIdHeaderName, string.Empty },
            { "Host", "s1.bar.foo" },
        });
        requestMock.Setup(x => x.Host).Returns(new HostString("s1.docusign.com"));
        _contextMock.Setup(x => x.Request).Returns(requestMock.Object);

        // Act
        var result = RequestContextMiddleware.GetDocusignShardId(_contextMock.Object);

        // Assert
        Assert.Equal("s1.bar", result);
    }

    [Fact]
    public void GetDocusignShardIdWithPublicHostReturnsEmptyString()
    {
        // Arrange
        var requestMock = new Mock<HttpRequest>();
        requestMock.Setup(x => x.Headers).Returns(new HeaderDictionary
        {
            { RequestContextMiddleware.ShardIdHeaderName, string.Empty },
            { "Host", "s1.bar.foo" },
        });
        requestMock.Setup(x => x.Host).Returns(new HostString("api.docusign.com"));
        _contextMock.Setup(x => x.Request).Returns(requestMock.Object);

        // Act
        var result = RequestContextMiddleware.GetDocusignShardId(_contextMock.Object);

        // Assert
        Assert.Equal(string.Empty, result);
    }
}
