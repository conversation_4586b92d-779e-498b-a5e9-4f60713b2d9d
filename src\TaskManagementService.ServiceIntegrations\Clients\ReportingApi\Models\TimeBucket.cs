// <auto-generated/>
using System.Runtime.Serialization;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public enum TimeBucket
    #pragma warning restore CS1591
    {
        [EnumMember(Value = "Daily")]
        #pragma warning disable CS1591
        Daily,
        #pragma warning restore CS1591
        [EnumMember(Value = "Weekly")]
        #pragma warning disable CS1591
        Weekly,
        #pragma warning restore CS1591
        [EnumMember(Value = "Monthly")]
        #pragma warning disable CS1591
        Monthly,
        #pragma warning restore CS1591
        [EnumMember(Value = "Yearly")]
        #pragma warning disable CS1591
        Yearly,
        #pragma warning restore CS1591
        [EnumMember(Value = "All")]
        #pragma warning disable CS1591
        All,
        #pragma warning restore CS1591
    }
}
