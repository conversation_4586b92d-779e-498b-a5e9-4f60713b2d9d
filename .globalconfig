; Global analyzer config
; https://docs.microsoft.com/en-us/dotnet/fundamentals/code-analysis/configuration-files#global-analyzerconfig
; Top level entry required to mark this as a global AnalyzerConfig file
is_global = true

; CA1014: Mark assemblies with CLSCompliant
dotnet_diagnostic.CA1014.severity = none

; CA1824: Mark assemblies with NeutralResourcesLanguageAttribute
dotnet_diagnostic.CA1824.severity = none
