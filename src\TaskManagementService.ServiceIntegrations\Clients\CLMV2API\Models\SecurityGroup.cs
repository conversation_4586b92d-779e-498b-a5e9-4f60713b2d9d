// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class SecurityGroup : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The AccessType property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? AccessType { get; set; }
#nullable restore
#else
        public string AccessType { get; set; }
#endif
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The Item property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Group? Item { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Group Item { get; set; }
#endif
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityGroup"/> and sets the default values.
        /// </summary>
        public SecurityGroup()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityGroup"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityGroup CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityGroup();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "AccessType", n => { AccessType = n.GetStringValue(); } },
                { "Item", n => { Item = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Group>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Group.CreateFromDiscriminatorValue); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("AccessType", AccessType);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Group>("Item", Item);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
