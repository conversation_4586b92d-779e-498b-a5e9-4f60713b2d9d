﻿using System.Globalization;
using System.Text.Json;

using Flurl;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Infrastructure.ServiceIntegrations.EnvelopeApi.Models;

namespace TaskManagementService.Infrastructure.ServiceIntegrations.EnvelopeApi;

public sealed class EnvelopeApiService(
    ILogger<EnvelopeApiService> logger,
    HttpClient httpClient,
    IRequestContextService requestContextService)
    : IEnvelopeApiService
{
    public const string AuthorizationHeaderName = "Authorization";

    public async Task<List<TasksSourceCount>> GetTasksCountAsync(CancellationToken cancellationToken = default)
    {
        var eSignBaseUrl = requestContextService.ESignApiBaseUrl.ToString();
        var requestUrl = string.Format(CultureInfo.InvariantCulture, eSignBaseUrl)
            .AppendPathSegment($"v2.1/accounts/{requestContextService.AccountId}/users/{requestContextService.UserId}/reports/dashboard");

        using var httpRequest = new HttpRequestMessage(HttpMethod.Get, requestUrl.ToUri());
        httpRequest.Headers.TryAddWithoutValidation(AuthorizationHeaderName, requestContextService.Authorization);

        var response = await httpClient.SendAsync(httpRequest, cancellationToken);
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var taskReport = JsonSerializer.Deserialize<ESignTaskReportApiDto>(content) ?? new ESignTaskReportApiDto();
            return [new TasksSourceCount(TaskSource.ESignature, taskReport.AwaitingMySignatureCount)];
        }

        logger.LogError("Failed to get tasks count from eSign API. Status code: {StatusCode}", response.StatusCode);
        return [new TasksSourceCount(TaskSource.ESignature, 0)];
    }
}
