{"sdk": {"version": "8.0.414", "allowPrerelease": false, "rollForward": "latestMinor"}, "msbuild-sdks": {"Microsoft.Build.Traversal": "4.1.82"}, "scriptShell": "pwsh", "scripts": {"build": "dotnet restore -r linux-x64 --verbosity minimal dirs.proj && dotnet build --no-restore -c Release -f net8.0 -r linux-x64 --no-self-contained --verbosity minimal dirs.proj && dotnet publish --no-build -c Release -f net8.0 -r linux-x64 --no-self-contained --verbosity minimal dirs.proj", "ado:build": "dotnet r build", "skaffold:build": "dotnet r build && docker build src/TaskManagementService --platform linux/amd64 -t $env:IMAGE && docker push $env:IMAGE"}}