﻿using MediatR;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

namespace TaskManagementService.Core.Handlers;

internal sealed class GetUserTasksCountQueryHandler(
    ILogger<GetUserTasksCountQueryHandler> logger,
    IRequestContextService requestContextService,
    IEnvelopeApiService envelopeApiService,
    IClmTaskService clmTaskService)
    : IRequestHandler<GetUserTasksCountQuery, GetUserTasksCountQueryResponse>
{
    public async Task<GetUserTasksCountQueryResponse> Handle(GetUserTasksCountQuery request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));

        logger.LogInformation("Getting tasks count for account: {AccountId}, User: {UserId}", requestContextService.AccountId, requestContextService.UserId);

        var tasks = await envelopeApiService.GetTasksCountAsync(cancellationToken);

        var clmTasks = await clmTaskService.GetTasksCountAsync(cancellationToken);
        clmTasks.ForEach(task => { tasks.Add(task); });

        return new GetUserTasksCountQueryResponse(new TasksCount(tasks));
    }
}
