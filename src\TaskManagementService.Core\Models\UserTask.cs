﻿using TaskManagementService.Core.Enums;

namespace TaskManagementService.Core.Models;

public class UserTask
{
    private readonly List<UserTaskAgreement> _agreements = [];

    public Guid Id { get; set; }
    public TaskSource? Source { get; set; }
    public TaskType? Type { get; set; }
    public string NavigationUrl { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public TaskUser? Assignor { get; set; }
    public string CreatedDate { get; set; } = string.Empty;
    public string DueDate { get; set; } = string.Empty;
    public string WorkflowName { get; set; } = string.Empty;
    public string AssignedDate { get; set; } = string.Empty;
    public IEnumerable<TaskCollaborator>? Assignees { get; set; }
    public IEnumerable<Guid> AgreementIds { get; set; } = [];
    public IEnumerable<UserTaskAgreement> Agreements => _agreements;
    public TaskGroupWithPermissions? TaskGroupAssignee { get; set; }
    public string Title { get; set; } = string.Empty;

    public void AddAgreement(UserTaskAgreement agreement)
    {
        ArgumentNullException.ThrowIfNull(agreement, nameof(agreement));
        _agreements.Add(agreement);
    }
}
