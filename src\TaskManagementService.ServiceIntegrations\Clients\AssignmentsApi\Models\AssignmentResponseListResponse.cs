// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class AssignmentResponseListResponse : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The items property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse>? Items { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse> Items { get; set; }
#endif
        /// <summary>The nextPage property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? NextPage { get; set; }
#nullable restore
#else
        public string NextPage { get; set; }
#endif
        /// <summary>The resultSetSize property</summary>
        public int? ResultSetSize { get; private set; }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponseListResponse"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponseListResponse CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponseListResponse();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "items", n => { Items = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse>(global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse.CreateFromDiscriminatorValue)?.AsList(); } },
                { "nextPage", n => { NextPage = n.GetStringValue(); } },
                { "resultSetSize", n => { ResultSetSize = n.GetIntValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse>("items", Items);
            writer.WriteStringValue("nextPage", NextPage);
        }
    }
}
#pragma warning restore CS0618
