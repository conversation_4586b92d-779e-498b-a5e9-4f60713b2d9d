﻿using System.Text;

namespace TaskManagementService.Periodic.Tests.Utils;

public static class AnalyzeSampleDotnetRequests
{
    public static async Task<HttpResponseMessage> GetServiceLivenessAsync(Uri endpoint)
    {
        using var httpClient = new HttpClient();
        return await httpClient.GetAsync(endpoint);
    }

    public static async Task<HttpResponseMessage> PostServiceLivenessAsync(Uri endpoint, string message)
    {
        using var httpClient = new HttpClient();
        using var content = new StringContent(message, Encoding.UTF8, "application/json");
        return await httpClient.PostAsync(endpoint, content);
    }
}
