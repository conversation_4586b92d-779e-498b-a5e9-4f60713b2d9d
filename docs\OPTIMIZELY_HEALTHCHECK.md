# Optimizely Health Check

## Overview

The `OptimizelyHealthCheck` class provides health monitoring for the Optimizely integration in the Task Management Service. It validates both the configuration and connectivity to Optimizely's CDN for all projects with valid SDK keys.

**Important**: This health check operates in **log-only mode**, meaning it performs all validations and logs the results, but always returns `Healthy` status to avoid impacting the overall service health.

## Features

- **Configuration Validation**: Checks if Optimizely is properly configured
- **Datafile Validation**: For all projects with valid SDK keys, validates that datafiles are accessible from Optimizely CDN
- **Configurable Log-Only Mode**: Can be configured to either log validation results only or return appropriate health status
- **Comprehensive Error Handling**: Handles network errors, timeouts, and invalid responses gracefully
- **Integration**: Fully integrated with ASP.NET Core Health Checks framework

## Configuration

The health check behavior is controlled through the `HealthChecks` section in `appsettings.json`:

```json
{
  "HealthChecks": {
    "Optimizely": {
      "LogOnlyMode": true
    }
  }
}
```

### Configuration Options

- **`LogOnlyMode`** (boolean, default: `true`):
  - `true`: Health check performs all validations and logs results, but always returns `Healthy` status
  - `false`: Health check returns appropriate status (`Healthy`, `Degraded`, or `Unhealthy`) based on validation results

### Environment-Specific Configuration

Different environments can have different settings:

**Production** (`appsettings.json`): Log-only mode enabled (default)
```json
{
  "HealthChecks": {
    "Optimizely": {
      "LogOnlyMode": true
    }
  }
}
```

**Development** (`appsettings.Development.json`): Full health check validation
```json
{
  "HealthChecks": {
    "Optimizely": {
      "LogOnlyMode": false
    }
  }
}
```

## Implementation Details

### Health Check Logic

The health check behavior depends on the `LogOnlyMode` configuration:

#### Log-Only Mode (LogOnlyMode = true)

1. **Disabled State**: Returns `Healthy` with description including "(log-only mode)"
2. **Validation**: Performs all validations and logs results
3. **Return Status**: Always returns `Healthy` regardless of validation results
4. **Error Handling**: Logs all errors but doesn't fail the health check

#### Standard Mode (LogOnlyMode = false)

1. **Disabled State**: Returns `Unhealthy` if no projects are configured when Optimizely appears to be enabled
2. **Validation Results**:
   - All validations pass → `Healthy`
   - Some projects fail validation → `Degraded`
   - All projects fail validation → `Unhealthy`
3. **Error Handling**: Returns appropriate unhealthy status for configuration or validation errors

3. **Project-specific Validation**: For each configured project with a valid SDK key:
   - Makes HTTP request to `https://cdn.optimizely.com/datafiles/{sdkKey}.json`
   - Validates response is successful and contains valid JSON
   - Logs validation results (success or failure)

4. **Result Aggregation (Log-Only Mode)**:
   - **Always returns `Healthy`** regardless of validation results
   - Logs detailed information about validation issues
   - Provides status summary including number of valid projects and issues found
   - Exception handling also returns `Healthy` while logging errors

### HTTP Validation

The health check validates datafile availability by:
- Making HTTP GET requests to Optimizely CDN endpoints
- Using 30-second timeout for each request
- Validating response status and JSON format
- Logging detailed information for failed requests (without failing the health check)

### Dependencies

* `IOptions<OneConfigOptimizelyOptions>`: Optimizely configuration
* `ILogger<OptimizelyHealthCheck>`: Logging
* `HttpClient`: HTTP requests to Optimizely CDN

## Registration

The health check is registered in `Program.cs` using:

```csharp
healthChecksBuilder.AddTypeActivatedCheck<OptimizelyHealthCheck>("optimizely");
```

Note: Uses `AddTypeActivatedCheck` to support dependency injection of `HttpClient` .

## Testing

Access the health check via the `/health` endpoint:

```bash
curl http://localhost:5000/health
```

Example response when Optimizely is disabled:
```json
{
  "status": "Healthy",
  "totalDuration": "00:00:00.0121388",
  "entries": {
    "optimizely": {
      "data": {},
      "description": "Optimizely is enabled but no projects configured (log-only mode)",
      "duration": "00:00:00.0070526",
      "status": "Healthy",
      "tags": []
    }
  }
}
```

Example response when Optimizely projects are configured and validated:
```json
{
  "status": "Healthy",
  "totalDuration": "00:00:00.4491942",
  "entries": {
    "optimizely": {
      "data": {},
      "description": "Optimizely validation completed (log-only mode). 1/1 projects valid. Issues: 0",
      "duration": "00:00:00.4452973",
      "status": "Healthy",
      "tags": []
    }
  }
}
```

## Log-Only Mode Benefits

The log-only mode provides several advantages:

- **Service Reliability**: Optimizely connectivity issues don't affect overall service health
- **Monitoring & Observability**: Full validation and detailed logging for monitoring purposes
- **Non-Blocking**: External dependency issues don't prevent the service from being marked as healthy
- **Debugging**: Comprehensive logs help identify and troubleshoot Optimizely integration issues

## Error Scenarios

All error scenarios are logged but do not cause health check failures:

### Network Errors
- Connection timeouts to Optimizely CDN
- DNS resolution failures
- HTTP error responses (404, 500, etc.)

### Invalid Responses
- Empty response body
- Non-JSON response content
- Malformed JSON

### Configuration Errors
- Missing or invalid SDK keys
- Null configuration objects

All errors are logged with appropriate detail levels while maintaining `Healthy` status.
