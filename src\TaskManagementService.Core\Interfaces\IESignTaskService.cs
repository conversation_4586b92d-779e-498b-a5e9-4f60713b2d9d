using TaskManagementService.Core.Models;

namespace TaskManagementService.Core.Interfaces;

public interface IESignTaskService
{
    public Task<TaskHistory> GetEnvelopeHistoryAsync(Guid accountId, Guid envelopeId, string? locale = null, CancellationToken cancellationToken = default);
    public Task VoidEnvelopeAsync(Guid accountId, string envelopeId, string voidedReason);
    public Task ResendEnvelopeAsync(Guid accountId, string envelopeId);
    public Task<long> GetIdvTasksCountAsync(Guid accountId);
}
