<Project>
  <PropertyGroup>
    <!-- Useful variable for file paths -->
    <RepoRoot>$(MSBuildThisFileDirectory)</RepoRoot>

    <!-- Auto generation of assemblyinfo.cs by default -->
    <GenerateAssemblyInfo Condition=" '$(GenerateAssemblyInfo)' == '' ">true</GenerateAssemblyInfo>

    <!-- Default assembly info properties -->
    <Company>DocuSign Incorporated</Company>
    <Product>DocuSign Online Signing Service</Product>
    <Copyright>Copyright (c) DocuSign Incorporated. All rights reserved.</Copyright>
    <PackageTags>microservices</PackageTags>
    <Authors>eng-task-management</Authors>

    <!-- Versioning -->
    <!-- Read version from the environment variables first. These will be populated by the build pipeline. -->
    <VersionMajor>$(VERSION_MAJOR)</VersionMajor>
    <VersionMinor>$(VERSION_MINOR)</VersionMinor>
    <VersionBuild>$(VERSION_BUILD)</VersionBuild>
    <VersionRevision>$(VERSION_REVISION)</VersionRevision>

    <!-- If building locally and there are no environment variables set, then initialize with defaults -->
    <VersionMajor Condition="$(VersionMajor) == ''">1</VersionMajor>
    <VersionMinor Condition="$(VersionMinor) == ''">0</VersionMinor>
    <VersionBuild Condition="$(VersionBuild) == ''">0</VersionBuild>
    <VersionRevision Condition="$(VersionRevision) == ''">0</VersionRevision>
    <Version Condition="$(Version) == ''">$(VersionMajor).$(VersionMinor).$(VersionBuild).$(VersionRevision)</Version>

    <!-- Force all warnings to be treated as errors. This won't include Code Analysis errors. -->
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>

    <!-- Use latest C# -->
    <LangVersion>latest</LangVersion>

    <!-- Enable static code analyzers -->
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisMode>All</AnalysisMode>
    <AnalysisLevel>latest</AnalysisLevel>
    <CodeAnalysisTreatWarningsAsErrors>true</CodeAnalysisTreatWarningsAsErrors>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>

    <!-- https://github.com/dotnet/roslyn/issues/41640 -->
    <!-- Workaround for https://github.com/dotnet/roslyn/issues/53720 -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);CS1591;CS1573</NoWarn>

    <!-- Disable global using -->
    <DisableImplicitNamespaceImports>true</DisableImplicitNamespaceImports>
    <ImplicitUsings>false</ImplicitUsings>

    <!-- Suppress "You are using a preview version of .NET. See: https://aka.ms/dotnet-core-preview" message -->
    <SuppressNETCoreSdkPreviewMessage>true</SuppressNETCoreSdkPreviewMessage>

    <!-- Add stylecop location -->
    <CodeAnalysisConfigPath Condition=" '$(CodeAnalysisConfigPath)' == '' ">$(MSBuildThisFileDirectory)build\</CodeAnalysisConfigPath>
    <CodeAnalysisConfigFilePath Condition=" '$(CodeAnalysisConfigFilePath)' == '' ">$(CodeAnalysisConfigPath)stylecop.json</CodeAnalysisConfigFilePath>
    <CodeAnalysisConfigRuleSetFilePath Condition=" '$(CodeAnalysisConfigRuleSetFilePath)' == '' ">$(CodeAnalysisConfigPath)stylecop.ruleset</CodeAnalysisConfigRuleSetFilePath>
  </PropertyGroup>

  <ItemGroup>
    <ProjectCapability Include="DynamicDependentFile" />
    <ProjectCapability Include="DynamicFileNesting" />
    <ProjectCapability Include="ConfigurableFileNesting" />
    <ProjectCapability Include="ConfigurableFileNestingFeatureEnabled" />
  </ItemGroup>
</Project>
