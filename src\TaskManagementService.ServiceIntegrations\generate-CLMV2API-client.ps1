# restore the Kiota tool
dotnet tool restore

# list the installed dotnet tools
dotnet tool list --local

# Define the URI of the Swagger file
$swaggerUri = 'http://localhost/api/swagger/docs/v1'

# Informative message about downloading the Swagger file
Write-Host "Downloading Swagger file from the specified URL: $swaggerUri"

# Using Invoke-WebRequest to fetch the file and save it to the Contracts sub-folder
Invoke-WebRequest -Uri $swaggerUri -OutFile '.\Contracts\CLMV2API.yml'

# Informative message about the location of the downloaded file
Write-Host '✅ Swagger file downloaded successfully and saved the file to ".\Contracts\CLMV2API.json".'

# Generate the client using the Kiota tool
Write-Host '⏳ Generating the client using the Kiota tool...'
dotnet kiota generate `
    --language csharp `
    --class-name CLMV2APIClient `
    --namespace-name TaskManagementService.ServiceIntegrations.Clients.CLMV2API `
    --serializer Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory `
    --deserializer Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory `
    --structured-mime-types application/json `
    --openapi ./contracts/CLMV2API.yml `
    --output ./Clients/CLMV2API `
    --clean-output `
    --include-path **/workitems/**/ `
    --include-path **/members/**/ `
    --exclude-path **/v201411/**

dotnet kiota info -d ".\contracts\CLMV2API.yml" -l CSharp

Write-Host '✅ Generated the CLM V2 API client using the Kiota tool.'
