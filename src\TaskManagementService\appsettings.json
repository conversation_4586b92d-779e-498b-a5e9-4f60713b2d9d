{"Logging": {"LogLevel": {"Default": "Information", "System": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "Warning", "Microsoft.Extensions.Diagnostics.HealthChecks": "Warning", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information"}}, "AllowedHosts": "*", "ForwardedHeaders": {"ForwardedHeaders": "All", "ForwardLimit": 2, "AllowedHosts": ["services.docusign.net", "*.services.docusign.net"]}, "Security": {"Authorities": ["https://account.docusign.com/"]}, "OptionalFeatures": {"EnableRedis": true}, "HealthChecks": {"Optimizely": {"LogOnlyMode": true}}, "OneConfig": {"Optimizely": {"EnableGlobalProject": false}}}