﻿using System;
using System.Diagnostics.CodeAnalysis;
using System.Linq;

using Microsoft.Extensions.DependencyInjection;

namespace TaskManagementService.Tests;

public static class TestExtensions
{
    public static void RemoveServices([NotNull] this IServiceCollection services, params Type[] types)
    {
        ArgumentNullException.ThrowIfNull(types);

        foreach (var type in types)
        {
            var descriptor = services.FirstOrDefault(s => s.ServiceType == type);
            services.Remove(descriptor);
        }
    }
}
