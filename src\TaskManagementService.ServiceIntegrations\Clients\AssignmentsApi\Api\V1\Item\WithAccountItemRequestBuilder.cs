// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
using TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments;
namespace TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item
{
    /// <summary>
    /// Builds and executes requests for operations under \api\v1\{accountId}
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class WithAccountItemRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The Assignments property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.AssignmentsRequestBuilder Assignments
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.AssignmentsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.WithAccountItemRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public WithAccountItemRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/v1/{accountId}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.WithAccountItemRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public WithAccountItemRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/v1/{accountId}", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
