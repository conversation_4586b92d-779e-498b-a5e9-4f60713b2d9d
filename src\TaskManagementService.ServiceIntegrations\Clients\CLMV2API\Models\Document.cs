// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class Document : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The AccessLevel property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel? AccessLevel { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel AccessLevel { get; set; }
#endif
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The AttachedDocuments property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument? AttachedDocuments { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument AttachedDocuments { get; set; }
#endif
        /// <summary>The AttributeGroups property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document_AttributeGroups? AttributeGroups { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document_AttributeGroups AttributeGroups { get; set; }
#endif
        /// <summary>The ContentCreatedDate property</summary>
        public DateTimeOffset? ContentCreatedDate { get; set; }
        /// <summary>The CreatedBy property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CreatedBy { get; set; }
#nullable restore
#else
        public string CreatedBy { get; set; }
#endif
        /// <summary>The CreatedDate property</summary>
        public DateTimeOffset? CreatedDate { get; set; }
        /// <summary>The Description property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Description { get; set; }
#nullable restore
#else
        public string Description { get; set; }
#endif
        /// <summary>The DocumentProcessTrackingActivities property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentProcessTrackingActivity? DocumentProcessTrackingActivities { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentProcessTrackingActivity DocumentProcessTrackingActivities { get; set; }
#endif
        /// <summary>The DocumentReminders property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentReminder? DocumentReminders { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentReminder DocumentReminders { get; set; }
#endif
        /// <summary>The DownloadDocumentHref property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? DownloadDocumentHref { get; private set; }
#nullable restore
#else
        public string DownloadDocumentHref { get; private set; }
#endif
        /// <summary>The EosParentInfo property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo? EosParentInfo { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo EosParentInfo { get; set; }
#endif
        /// <summary>The Extension property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Extension { get; set; }
#nullable restore
#else
        public string Extension { get; set; }
#endif
        /// <summary>The HistoryItems property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionHistoryItem? HistoryItems { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionHistoryItem HistoryItems { get; set; }
#endif
        /// <summary>The Href property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Href { get; set; }
#nullable restore
#else
        public string Href { get; set; }
#endif
        /// <summary>The IsInTrash property</summary>
        public bool? IsInTrash { get; set; }
        /// <summary>The Lock property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.DocumentLock? Lock { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.DocumentLock Lock { get; set; }
#endif
        /// <summary>The LockStatus property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? LockStatus { get; set; }
#nullable restore
#else
        public string LockStatus { get; set; }
#endif
        /// <summary>The Name property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Name { get; set; }
#nullable restore
#else
        public string Name { get; set; }
#endif
        /// <summary>The NativeFileSize property</summary>
        public long? NativeFileSize { get; set; }
        /// <summary>The PageCount property</summary>
        public int? PageCount { get; set; }
        /// <summary>The ParentFolder property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder? ParentFolder { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder ParentFolder { get; set; }
#endif
        /// <summary>The Path property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Path { get; set; }
#nullable restore
#else
        public string Path { get; set; }
#endif
        /// <summary>The PdfFileSize property</summary>
        public long? PdfFileSize { get; set; }
        /// <summary>The PreviewUrl property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? PreviewUrl { get; set; }
#nullable restore
#else
        public string PreviewUrl { get; set; }
#endif
        /// <summary>The RelatedDocuments property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument? RelatedDocuments { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument RelatedDocuments { get; set; }
#endif
        /// <summary>The Score property</summary>
        public float? Score { get; set; }
        /// <summary>The ShareLinks property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionShareLink? ShareLinks { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionShareLink ShareLinks { get; set; }
#endif
        /// <summary>The Uid property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Uid { get; set; }
#nullable restore
#else
        public string Uid { get; set; }
#endif
        /// <summary>The UpdatedBy property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? UpdatedBy { get; set; }
#nullable restore
#else
        public string UpdatedBy { get; set; }
#endif
        /// <summary>The UpdatedDate property</summary>
        public DateTimeOffset? UpdatedDate { get; set; }
        /// <summary>The Version property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Version { get; set; }
#nullable restore
#else
        public string Version { get; set; }
#endif
        /// <summary>The Versions property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument? Versions { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument Versions { get; set; }
#endif
        /// <summary>The WorkItems property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkItem? WorkItems { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkItem WorkItems { get; set; }
#endif
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document"/> and sets the default values.
        /// </summary>
        public Document()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "AccessLevel", n => { AccessLevel = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel.CreateFromDiscriminatorValue); } },
                { "AttachedDocuments", n => { AttachedDocuments = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument.CreateFromDiscriminatorValue); } },
                { "AttributeGroups", n => { AttributeGroups = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document_AttributeGroups>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document_AttributeGroups.CreateFromDiscriminatorValue); } },
                { "ContentCreatedDate", n => { ContentCreatedDate = n.GetDateTimeOffsetValue(); } },
                { "CreatedBy", n => { CreatedBy = n.GetStringValue(); } },
                { "CreatedDate", n => { CreatedDate = n.GetDateTimeOffsetValue(); } },
                { "Description", n => { Description = n.GetStringValue(); } },
                { "DocumentProcessTrackingActivities", n => { DocumentProcessTrackingActivities = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentProcessTrackingActivity>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentProcessTrackingActivity.CreateFromDiscriminatorValue); } },
                { "DocumentReminders", n => { DocumentReminders = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentReminder>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentReminder.CreateFromDiscriminatorValue); } },
                { "DownloadDocumentHref", n => { DownloadDocumentHref = n.GetStringValue(); } },
                { "EosParentInfo", n => { EosParentInfo = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo.CreateFromDiscriminatorValue); } },
                { "Extension", n => { Extension = n.GetStringValue(); } },
                { "HistoryItems", n => { HistoryItems = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionHistoryItem>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionHistoryItem.CreateFromDiscriminatorValue); } },
                { "Href", n => { Href = n.GetStringValue(); } },
                { "IsInTrash", n => { IsInTrash = n.GetBoolValue(); } },
                { "Lock", n => { Lock = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.DocumentLock>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.DocumentLock.CreateFromDiscriminatorValue); } },
                { "LockStatus", n => { LockStatus = n.GetStringValue(); } },
                { "Name", n => { Name = n.GetStringValue(); } },
                { "NativeFileSize", n => { NativeFileSize = n.GetLongValue(); } },
                { "PageCount", n => { PageCount = n.GetIntValue(); } },
                { "ParentFolder", n => { ParentFolder = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder.CreateFromDiscriminatorValue); } },
                { "Path", n => { Path = n.GetStringValue(); } },
                { "PdfFileSize", n => { PdfFileSize = n.GetLongValue(); } },
                { "PreviewUrl", n => { PreviewUrl = n.GetStringValue(); } },
                { "RelatedDocuments", n => { RelatedDocuments = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument.CreateFromDiscriminatorValue); } },
                { "Score", n => { Score = n.GetFloatValue(); } },
                { "ShareLinks", n => { ShareLinks = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionShareLink>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionShareLink.CreateFromDiscriminatorValue); } },
                { "Uid", n => { Uid = n.GetStringValue(); } },
                { "UpdatedBy", n => { UpdatedBy = n.GetStringValue(); } },
                { "UpdatedDate", n => { UpdatedDate = n.GetDateTimeOffsetValue(); } },
                { "Version", n => { Version = n.GetStringValue(); } },
                { "Versions", n => { Versions = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument.CreateFromDiscriminatorValue); } },
                { "WorkItems", n => { WorkItems = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkItem>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkItem.CreateFromDiscriminatorValue); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel>("AccessLevel", AccessLevel);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument>("AttachedDocuments", AttachedDocuments);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document_AttributeGroups>("AttributeGroups", AttributeGroups);
            writer.WriteDateTimeOffsetValue("ContentCreatedDate", ContentCreatedDate);
            writer.WriteStringValue("CreatedBy", CreatedBy);
            writer.WriteDateTimeOffsetValue("CreatedDate", CreatedDate);
            writer.WriteStringValue("Description", Description);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentProcessTrackingActivity>("DocumentProcessTrackingActivities", DocumentProcessTrackingActivities);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentReminder>("DocumentReminders", DocumentReminders);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo>("EosParentInfo", EosParentInfo);
            writer.WriteStringValue("Extension", Extension);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionHistoryItem>("HistoryItems", HistoryItems);
            writer.WriteStringValue("Href", Href);
            writer.WriteBoolValue("IsInTrash", IsInTrash);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.DocumentLock>("Lock", Lock);
            writer.WriteStringValue("LockStatus", LockStatus);
            writer.WriteStringValue("Name", Name);
            writer.WriteLongValue("NativeFileSize", NativeFileSize);
            writer.WriteIntValue("PageCount", PageCount);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder>("ParentFolder", ParentFolder);
            writer.WriteStringValue("Path", Path);
            writer.WriteLongValue("PdfFileSize", PdfFileSize);
            writer.WriteStringValue("PreviewUrl", PreviewUrl);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument>("RelatedDocuments", RelatedDocuments);
            writer.WriteFloatValue("Score", Score);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionShareLink>("ShareLinks", ShareLinks);
            writer.WriteStringValue("Uid", Uid);
            writer.WriteStringValue("UpdatedBy", UpdatedBy);
            writer.WriteDateTimeOffsetValue("UpdatedDate", UpdatedDate);
            writer.WriteStringValue("Version", Version);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument>("Versions", Versions);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkItem>("WorkItems", WorkItems);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
