﻿namespace TaskManagementService.Core.Models;

public class TaskHistoryAuditEvent
{
    public string CreatedDate { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string DueDate { get; set; } = string.Empty;
    public bool IsCompleted { get; set; }
    public string AssignedUser { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
}
