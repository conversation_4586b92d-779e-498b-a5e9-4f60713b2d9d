﻿using System.Text.Json;

using xRetry;

using Xunit;
using Xunit.Abstractions;

namespace TaskManagementService.Integration.Tests;

public sealed class HealthCheckTests(ITestOutputHelper testOutputHelper) : IntegrationTestBase
{
    private static readonly JsonSerializerOptions JsonSerializerOptions = new() { WriteIndented = true };

    [RetryFact(3, 2000)]
    [Trait("Priority", "1")]
    [Trait("Category", "PreDeployment")]
    [Trait("Category", "InflightDeployment")]
    [Trait("Category", "PostDeployment")]
    [Trait("Category", "PullRequestNoMocks")]
    public async Task HealthCheckTestsAsync()
    {
        using var httpClient = new HttpClient();
        var response = await httpClient.GetAsync(HealthCheckEndpoint);

        var content = await response.Content.ReadAsStringAsync();
        using var jsonDocument = JsonDocument.Parse(content);
        var formattedJson = JsonSerializer.Serialize(jsonDocument.RootElement, JsonSerializerOptions);
        testOutputHelper.WriteLine($"Service Liveness Content: {formattedJson}");

        Assert.True(response.IsSuccessStatusCode, "Service should be healthy.");
    }
}
