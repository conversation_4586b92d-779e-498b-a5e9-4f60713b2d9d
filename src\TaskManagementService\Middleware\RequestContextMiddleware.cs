﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

using DocuSign.Adm.Common.AspNet.OAuth.Claims;

using Flurl;

using MediatR;

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

using TaskManagementService.Core.Queries;
using TaskManagementService.Services;

namespace TaskManagementService.Middleware;

public sealed class RequestContextMiddleware
{
    public const string ShardIdHeaderName = "DocuSign-Shard-Id";
    public const string LleShardIdHeaderName = "LLE-Shard-Id";
    public const string AccountIdRouteValueName = "accountId";

    private static readonly Regex ShardIdHostRegex = new(@"^(s\d+\.[a-zA-Z0-9]{2,3})(?=\.).+$", RegexOptions.Compiled, TimeSpan.FromSeconds(2d));
    private static readonly Regex AccountIdPathRegex = new("/accounts?/([0-9A-Fa-f]{8}[-][0-9A-Fa-f]{4}[-][0-9A-Fa-f]{4}[-][0-9A-Fa-f]{4}[-][0-9A-Fa-f]{12})/", RegexOptions.Compiled, TimeSpan.FromSeconds(2d));
    private static readonly HashSet<string> PublicHosts = ["api.dev.docusign.net", "api-s.docusign.com", "api-d.docusign.com", "api.docusign.com", "api.local"];

    private readonly RequestDelegate _next;
    private readonly ILogger<RequestContextMiddleware> _logger;

    public RequestContextMiddleware(RequestDelegate next, ILogger<RequestContextMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, RequestContextService requestContext, IMediator mediator)
    {
        ArgumentNullException.ThrowIfNull(context, nameof(context));
        ArgumentNullException.ThrowIfNull(context.Request, nameof(context.Request));
        ArgumentNullException.ThrowIfNull(requestContext, nameof(requestContext));
        ArgumentNullException.ThrowIfNull(mediator, nameof(mediator));

        requestContext.AccountId = GetAccountId(context);
        requestContext.ShardId = GetDocusignShardId(context);
        requestContext.Authorization = context.Request.Headers.Authorization.ToString();
        requestContext.TraceParent = GetTraceParent(context);
        requestContext.RequestPath = context.Request.Path.ToString();
        requestContext.Issuer = GetIssuer(context.User.Claims);

        requestContext.IsClmAccount = context.User.Claims.Any(c =>
            c.Type == DocuSignClaimTypes.Scope && !string.IsNullOrWhiteSpace(c.Value) &&
            (c.Value.Equals(DocuSignClaimValues.ClmSpringReadScope, StringComparison.OrdinalIgnoreCase) ||
             c.Value.Equals(DocuSignClaimValues.ClmSpringWriteScope, StringComparison.OrdinalIgnoreCase)));

        if (context.User.Identity?.IsAuthenticated == true && requestContext.AccountId != Guid.Empty)
        {
            requestContext.AddClaims(context.User.Claims);
            requestContext.UserId = GetUserId(context.User.Claims);

            var userAccount = await mediator.Send(new GetUserAccountQuery(requestContext));
            _logger.LogInformation("User account retrieved: {UserAccount}", userAccount);

            requestContext.AccountName = userAccount.AccountName ?? string.Empty;
            requestContext.ESignApiBaseUrl = userAccount.ESignApiBaseUrl.AppendPathSegment("/restapi").ToUri();
            requestContext.ClmApiBaseUrl = userAccount.ClmApiBaseUrl;
        }

        await _next(context);
    }

    private static Guid GetUserId(IEnumerable<Claim> claims)
    {
        var userIdValue = claims.FirstOrDefault(c =>
                   !string.IsNullOrWhiteSpace(c.Type) && c.Type.Equals(DocuSignClaimTypes.UserId, StringComparison.OrdinalIgnoreCase))
               ?.Value;

        return Guid.TryParse(userIdValue, out var userId) ? userId : Guid.Empty;
    }

    private static Uri GetIssuer(IEnumerable<Claim> claims)
    {
        var issuer = claims.FirstOrDefault(c =>
                !string.IsNullOrWhiteSpace(c.Type) && c.Type.Equals(DocuSignClaimTypes.Issuer, StringComparison.OrdinalIgnoreCase))
            ?.Value;

        return Uri.TryCreate(issuer, UriKind.Absolute, out var issuerUri) ? issuerUri : null;
    }

    public static Guid GetAccountId(HttpContext context)
    {
        ArgumentNullException.ThrowIfNull(context, nameof(context));

        if (context.Request.RouteValues.TryGetValue(AccountIdRouteValueName, out var accountIdInRoute) &&
            Guid.TryParse(accountIdInRoute?.ToString() ?? string.Empty, out var accountIdFromRoute))
        {
            return accountIdFromRoute;
        }

        var accountIdMatch = AccountIdPathRegex.Match(context.Request.Path);
        if (accountIdMatch.Success && accountIdMatch.Groups.Count > 1)
        {
            return Guid.TryParse(accountIdMatch.Groups[1].Value, out var accountId) ? accountId : Guid.Empty;
        }

        return Guid.Empty;
    }

    private static string GetTraceParent(HttpContext context) => context?.Request.Headers.TraceParent.ToString() ?? string.Empty;

    public static string GetDocusignShardId(HttpContext context)
    {
        ArgumentNullException.ThrowIfNull(context, nameof(context));

        var shardId = context.Request.Headers[ShardIdHeaderName].ToString();

        var currentHost = context.Request.Host.Host;
        if (string.IsNullOrWhiteSpace(shardId) && currentHost.Contains(".exp.", StringComparison.InvariantCultureIgnoreCase))
        {
            shardId = context.Request.Headers[LleShardIdHeaderName].ToString();
        }

        if (string.IsNullOrWhiteSpace(shardId) && !PublicHosts.Contains(currentHost))
        {
            var host = context.Request.Headers.Host.ToString();
            if (!string.IsNullOrWhiteSpace(host))
            {
                var matches = ShardIdHostRegex.Match(host);
                if (matches.Success && matches.Groups.Count > 1)
                {
                    shardId = matches.Groups[1].Value;
                }
            }
        }

        return shardId;
    }
}
