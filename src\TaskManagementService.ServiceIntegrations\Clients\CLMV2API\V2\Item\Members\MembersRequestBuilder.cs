// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members
{
    /// <summary>
    /// Builds and executes requests for operations under \v2\{accountId}\members
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class MembersRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The current property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.CurrentRequestBuilder Current
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.CurrentRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>Gets an item from the TaskManagementService.ServiceIntegrations.Clients.CLMV2API.v2.item.members.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.MembersItemRequestBuilder"/></returns>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.MembersItemRequestBuilder this[string position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                urlTplParams.Add("id", position);
                return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.MembersItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.MembersRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public MembersRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/v2/{accountId}/members", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.MembersRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public MembersRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/v2/{accountId}/members", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
