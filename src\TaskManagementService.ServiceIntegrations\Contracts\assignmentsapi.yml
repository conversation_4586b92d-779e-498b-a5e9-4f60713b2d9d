openapi: 3.0.1
info:
  title: Assignments API
  version: '1.0'
servers:
  - url: /assignments
paths:
  '/api/v1/{accountId}/Assignments':
    post:
      tags:
        - Assignments
      summary: Create an assignment.
      operationId: PostAssignmentV1
      parameters:
        - name: accountId
          in: path
          description: Account Id from route.
          required: true
          schema:
            type: string
        - name: X-DocuSign-ActingUserId
          in: header
          required: true
          schema:
            type: string
            format: uuid
            example: 6c897b19-076d-4449-8094-55ad2700ec1e
        - name: X-DocuSign-CallingService
          in: header
          schema:
            type: string
            default: CLM.Collaboration
            example: CLM.Collaboration
      requestBody:
        description: The request body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAssignmentRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/CreateAssignmentRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/CreateAssignmentRequest'
      responses:
        '400':
          description: Bad context
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '201':
          description: Assignment response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentResponse'
        '422':
          description: Bad payload
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
  '/api/v1/{accountId}/Assignments/{assignmentId}':
    get:
      tags:
        - Assignments
      summary: Get assignment.
      operationId: GetAssignmentByIdV1
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
        - name: assignmentId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: X-DocuSign-ActingUserId
          in: header
          required: true
          schema:
            type: string
            format: uuid
            example: e05bd340-d09c-4293-a6a7-56917c32b41d
        - name: X-DocuSign-CallingService
          in: header
          schema:
            type: string
            default: CLM.Collaboration
            example: CLM.Collaboration
      responses:
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentResponse'
        '404':
          description: Assignment not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
    patch:
      tags:
        - Assignments
      summary: Updates an assignment.
      operationId: UpdateAssignmentV1
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
        - name: assignmentId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: X-DocuSign-ActingUserId
          in: header
          required: true
          schema:
            type: string
            format: uuid
            example: d8dd96c2-c9d6-4782-83fd-964613893f30
        - name: X-DocuSign-CallingService
          in: header
          schema:
            type: string
            default: CLM.Collaboration
            example: CLM.Collaboration
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAssignmentRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateAssignmentRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateAssignmentRequest'
      responses:
        '400':
          description: Bad context
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentResponse'
        '422':
          description: Bad payload
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '404':
          description: Assignment not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
    delete:
      tags:
        - Assignments
      summary: Deletes an assignment.
      operationId: DeleteAssignmentV1
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
        - name: assignmentId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: X-DocuSign-ActingUserId
          in: header
          required: true
          schema:
            type: string
            format: uuid
            example: a08bb8b8-d1d9-4a09-a8e4-ed3eb17ffe2a
        - name: X-DocuSign-CallingService
          in: header
          schema:
            type: string
            default: CLM.Collaboration
            example: CLM.Collaboration
      responses:
        '400':
          description: Bad context
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '204':
          description: No Content
        '404':
          description: Assignment not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
  '/api/v1/{accountId}/Assignments/assignees/{assigneeId}':
    get:
      tags:
        - Assignments
      summary: Gets assignments by assignee Id.
      operationId: GetAssignmentsByAssigneeV1
      parameters:
        - name: accountId
          in: path
          description: ''
          required: true
          schema:
            type: string
        - name: assigneeId
          in: path
          description: ''
          required: true
          schema:
            type: string
            format: uuid
        - name: pagingCursor
          in: query
          description: ''
          schema:
            type: string
            default: ''
        - name: count
          in: query
          description: ''
          schema:
            maximum: 1000
            minimum: 1
            type: integer
            format: int32
            default: 20
        - name: X-DocuSign-ActingUserId
          in: header
          required: true
          schema:
            type: string
            format: uuid
            example: ********-afbe-49e0-bb6e-a87a3c5d1028
        - name: X-DocuSign-CallingService
          in: header
          schema:
            type: string
            default: CLM.Collaboration
            example: CLM.Collaboration
      responses:
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentResponseListResponse'
  '/api/v1/{accountId}/Assignments/search':
    post:
      tags:
        - Assignments
      summary: Find assignments by search.
      operationId: AssignmentsBySearchV1
      parameters:
        - name: accountId
          in: path
          description: ''
          required: true
          schema:
            type: string
        - name: pagingCursor
          in: query
          description: ''
          schema:
            type: string
            default: ''
        - name: count
          in: query
          description: ''
          schema:
            maximum: 1000
            minimum: 1
            type: integer
            format: int32
            default: 20
        - name: X-DocuSign-ActingUserId
          in: header
          required: true
          schema:
            type: string
            format: uuid
            example: a70e1596-0bd6-4658-8d78-dbd00aa84008
        - name: X-DocuSign-CallingService
          in: header
          schema:
            type: string
            default: CLM.Collaboration
            example: CLM.Collaboration
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssignmentsSearchRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AssignmentsSearchRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AssignmentsSearchRequest'
      responses:
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentResponseListResponse'
        '422':
          description: Bad payload
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
  '/api/v1/{accountId}/Assignments/contexts/{contextId}':
    get:
      tags:
        - Assignments
      summary: Gets assignments by context.
      operationId: GetAssignmentsByContextV1
      parameters:
        - name: accountId
          in: path
          description: ''
          required: true
          schema:
            type: string
        - name: contextId
          in: path
          description: ''
          required: true
          schema:
            type: string
            format: uuid
        - name: status
          in: query
          description: ''
          schema:
            type: array
            items:
              type: string
        - name: pagingCursor
          in: query
          description: ''
          schema:
            type: string
            default: ''
        - name: includeHistory
          in: query
          description: ''
          schema:
            type: boolean
            default: false
        - name: count
          in: query
          description: ''
          schema:
            maximum: 1000
            minimum: 1
            type: integer
            format: int32
            default: 20
        - name: X-DocuSign-ActingUserId
          in: header
          required: true
          schema:
            type: string
            format: uuid
            example: e7b94f82-3cce-4fa6-9e67-bd6598a14528
        - name: X-DocuSign-CallingService
          in: header
          schema:
            type: string
            default: CLM.Collaboration
            example: CLM.Collaboration
      responses:
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssignmentResponseListResponse'
    delete:
      tags:
        - Assignments
      summary: Deletes all assignments for a context.
      operationId: DeleteAssignmentsByContextV1
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
        - name: contextId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: X-DocuSign-ActingUserId
          in: header
          required: true
          schema:
            type: string
            format: uuid
            example: 03eab281-777d-45f8-ba75-a2fabd74f439
        - name: X-DocuSign-CallingService
          in: header
          schema:
            type: string
            default: CLM.Collaboration
            example: CLM.Collaboration
      responses:
        '400':
          description: Bad context
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '204':
          description: No Content
  '/api/v1/{accountId}/Assignments/{assignmentId}/history':
    get:
      tags:
        - Assignments
      summary: Get assignment's history.
      operationId: GetAssignmentHistoryV1
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
        - name: assignmentId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: X-DocuSign-ActingUserId
          in: header
          required: true
          schema:
            type: string
            format: uuid
            example: 60c36084-a209-456e-846b-54a2e372e7a2
        - name: X-DocuSign-CallingService
          in: header
          schema:
            type: string
            default: CLM.Collaboration
            example: CLM.Collaboration
      responses:
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HistoryEventBaseListResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
  '/api/v1/{accountId}/Assignments/{assignmentId}/assignees/{assigneeId}':
    patch:
      tags:
        - Assignments
      summary: Updates an assignee.
      operationId: UpdateAssigneeV1
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
        - name: assignmentId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: assigneeId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: X-DocuSign-ActingUserId
          in: header
          required: true
          schema:
            type: string
            format: uuid
            example: dd345063-02e5-4ad8-9c6f-bbcc0e320ea1
        - name: X-DocuSign-CallingService
          in: header
          schema:
            type: string
            default: CLM.Collaboration
            example: CLM.Collaboration
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAssigneeRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/UpdateAssigneeRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/UpdateAssigneeRequest'
      responses:
        '400':
          description: Bad context
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssigneeResponse'
        '422':
          description: Bad payload
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '404':
          description: Assignment / Assignee not foound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
    delete:
      tags:
        - Assignments
      summary: Deletes an assignee from assignment.
      operationId: DeleteAssigneeV1
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
        - name: assignmentId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: assigneeId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: X-DocuSign-ActingUserId
          in: header
          required: true
          schema:
            type: string
            format: uuid
            example: b255a331-e40d-4007-a1a4-************
        - name: X-DocuSign-CallingService
          in: header
          schema:
            type: string
            default: CLM.Collaboration
            example: CLM.Collaboration
      responses:
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '204':
          description: No Content
        '404':
          description: Assignment / Assignee not foound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
    put:
      tags:
        - Assignments
      summary: Replaces an assignee in the assignment.
      operationId: ReplaceAssigneeV1
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
        - name: assignmentId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: assigneeId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: X-DocuSign-ActingUserId
          in: header
          required: true
          schema:
            type: string
            format: uuid
            example: 54f2881a-0869-4ddf-b2f6-c5c3c3f17687
        - name: X-DocuSign-CallingService
          in: header
          schema:
            type: string
            default: CLM.Collaboration
            example: CLM.Collaboration
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssigneeRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AssigneeRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AssigneeRequest'
      responses:
        '400':
          description: Bad context
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssigneeResponse'
        '422':
          description: Bad payload
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '404':
          description: Assignment / Assignee not foound
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
  '/api/v1/{accountId}/Assignments/{assignmentId}/assignees':
    post:
      tags:
        - Assignments
      summary: Add a new assignee to assignment.
      operationId: AddAssigneeV1
      parameters:
        - name: accountId
          in: path
          required: true
          schema:
            type: string
        - name: assignmentId
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: X-DocuSign-ActingUserId
          in: header
          required: true
          schema:
            type: string
            format: uuid
            example: 319fb2e3-5d50-4972-8fc6-044a42f2ee00
        - name: X-DocuSign-CallingService
          in: header
          schema:
            type: string
            default: CLM.Collaboration
            example: CLM.Collaboration
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssigneeRequest'
          text/json:
            schema:
              $ref: '#/components/schemas/AssigneeRequest'
          application/*+json:
            schema:
              $ref: '#/components/schemas/AssigneeRequest'
      responses:
        '400':
          description: Bad context
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '401':
          description: Unauthorized access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AssigneeResponse'
        '422':
          description: Bad payload
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
        '404':
          description: Assignment not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorDetails'
components:
  schemas:
    AssigneeActionUpdatedHistoryEvent:
      allOf:
        - $ref: '#/components/schemas/HistoryEventBase'
        - type: object
          properties:
            action:
              type: string
              nullable: true
            assigneeId:
              type: string
              format: uuid
            actingUserId:
              type: string
              format: uuid
              nullable: true
          additionalProperties: false
    AssigneeAddedHistoryEvent:
      allOf:
        - $ref: '#/components/schemas/HistoryEventBase'
        - type: object
          properties:
            actingUserId:
              type: string
              format: uuid
            details:
              $ref: '#/components/schemas/AssigneeDetails'
          additionalProperties: false
    AssigneeDetails:
      type: object
      properties:
        assigneeId:
          type: string
          format: uuid
        weight:
          type: integer
          format: int32
          nullable: true
        isGroup:
          type: boolean
          nullable: true
        groupDetails:
          $ref: '#/components/schemas/GroupDetails'
      additionalProperties: false
    AssigneeRemovedHistoryEvent:
      allOf:
        - $ref: '#/components/schemas/HistoryEventBase'
        - type: object
          properties:
            actingUserId:
              nullable: true
            assigneeId:
              type: string
              format: uuid
          additionalProperties: false
    AssigneeReplacedDetails:
      type: object
      properties:
        previousAssignee:
          $ref: '#/components/schemas/AssigneeDetails'
        currentAssignee:
          $ref: '#/components/schemas/AssigneeDetails'
      additionalProperties: false
    AssigneeReplacedHistoryEvent:
      allOf:
        - $ref: '#/components/schemas/HistoryEventBase'
        - type: object
          properties:
            actingUserId:
              type: string
              format: uuid
            details:
              $ref: '#/components/schemas/AssigneeReplacedDetails'
          additionalProperties: false
    AssigneeRequest:
      type: object
      properties:
        assigneeId:
          type: string
          format: uuid
        weight:
          type: integer
          format: int32
          nullable: true
        isGroup:
          type: boolean
          nullable: true
        group:
          $ref: '#/components/schemas/GroupRequest'
      additionalProperties: false
    AssigneeResponse:
      type: object
      properties:
        assigneeId:
          type: string
          format: uuid
        weight:
          type: integer
          format: int32
          nullable: true
        isGroup:
          type: boolean
          nullable: true
        action:
          type: string
          nullable: true
        actingUserId:
          type: string
          nullable: true
        group:
          $ref: '#/components/schemas/GroupResponse'
      additionalProperties: false
    AssignmentCancelledHistoryEvent:
      allOf:
        - $ref: '#/components/schemas/HistoryEventBase'
        - type: object
          properties:
            actingUserId:
              type: string
              format: uuid
          additionalProperties: false
    AssignmentCompletedHistoryEvent:
      allOf:
        - $ref: '#/components/schemas/HistoryEventBase'
        - type: object
          additionalProperties: false
    AssignmentCreatedHistoryEvent:
      allOf:
        - $ref: '#/components/schemas/HistoryEventBase'
        - type: object
          properties:
            description:
              type: string
              nullable: true
            assignerId:
              type: string
              format: uuid
            expirationDate:
              type: string
              format: date-time
            dueDate:
              type: string
              format: date-time
              nullable: true
            actingUserId:
              type: string
              format: uuid
          additionalProperties: false
    AssignmentExpiredHistoryEvent:
      allOf:
        - $ref: '#/components/schemas/HistoryEventBase'
        - type: object
          additionalProperties: false
    AssignmentPropertiesUpdatedHistoryEvent:
      allOf:
        - $ref: '#/components/schemas/HistoryEventBase'
        - type: object
          properties:
            actingUserId:
              type: string
              format: uuid
          additionalProperties: false
    AssignmentResponse:
      type: object
      properties:
        assignmentId:
          type: string
          format: uuid
        assignerId:
          type: string
          format: uuid
        assignees:
          type: array
          items:
            $ref: '#/components/schemas/AssigneeResponse'
          nullable: true
        contextId:
          type: string
          format: uuid
        subContextId:
          type: string
          format: uuid
          nullable: true
        createdDate:
          type: string
          format: date-time
        expirationDate:
          type: string
          format: date-time
        dueDate:
          type: string
          format: date-time
          nullable: true
        status:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            type: string
          nullable: true
        historyItems:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/StageAddedHistoryEvent'
              - $ref: '#/components/schemas/AssigneeAddedHistoryEvent'
              - $ref: '#/components/schemas/AssigneeRemovedHistoryEvent'
              - $ref: '#/components/schemas/AssigneeReplacedHistoryEvent'
              - $ref: '#/components/schemas/AssignmentCreatedHistoryEvent'
              - $ref: '#/components/schemas/AssignmentExpiredHistoryEvent'
              - $ref: '#/components/schemas/AssignmentCancelledHistoryEvent'
              - $ref: '#/components/schemas/AssignmentCompletedHistoryEvent'
              - $ref: '#/components/schemas/AssigneeActionUpdatedHistoryEvent'
              - $ref: '#/components/schemas/AssignmentPropertiesUpdatedHistoryEvent'
          nullable: true
      additionalProperties: false
    AssignmentResponseListResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/AssignmentResponse'
          nullable: true
        nextPage:
          type: string
          nullable: true
        resultSetSize:
          type: integer
          format: int32
          readOnly: true
      additionalProperties: false
    AssignmentsSearchRequest:
      type: object
      properties:
        assigneeIds:
          type: array
          items:
            type: string
            format: uuid
          nullable: true
      additionalProperties: false
    CreateAssignmentRequest:
      required:
        - assignees
        - assignerId
        - contextId
      type: object
      properties:
        assignerId:
          type: string
          format: uuid
        assignees:
          type: array
          items:
            $ref: '#/components/schemas/AssigneeRequest'
        contextId:
          type: string
          format: uuid
        subContextId:
          type: string
          format: uuid
          nullable: true
        expirationDate:
          type: string
          format: date-time
          nullable: true
        dueDate:
          type: string
          format: date-time
          nullable: true
        description:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            type: string
          nullable: true
      additionalProperties: false
    ErrorDetails:
      type: object
      properties:
        userMessage:
          type: string
          nullable: true
        developerMessage:
          type: string
          nullable: true
        errorCode:
          type: string
          nullable: true
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          nullable: true
      additionalProperties: { }
    GroupDetails:
      type: object
      properties:
        isAutoAssigned:
          type: boolean
        assignedId:
          type: string
          format: uuid
        assignedPattern:
          type: string
          nullable: true
      additionalProperties: false
    GroupRequest:
      type: object
      properties:
        assigneeId:
          type: string
          format: uuid
        assigneePattern:
          type: string
          nullable: true
        assignableUsers:
          type: array
          items:
            $ref: '#/components/schemas/GroupUser'
          nullable: true
      additionalProperties: false
    GroupResponse:
      type: object
      properties:
        assigneePattern:
          type: string
          nullable: true
        assignee:
          $ref: '#/components/schemas/GroupUser'
      additionalProperties: false
    GroupUser:
      type: object
      properties:
        id:
          type: string
          format: uuid
        weight:
          type: integer
          format: int32
      additionalProperties: false
    HistoryEventBase:
      required:
        - eventType
      type: object
      properties:
        eventType:
          type: string
          nullable: true
        createdAt:
          type: string
          format: date-time
      additionalProperties: false
      discriminator:
        propertyName: eventType
        mapping:
          StageAddedHistoryEvent: '#/components/schemas/StageAddedHistoryEvent'
          AssigneeAddedHistoryEvent: '#/components/schemas/AssigneeAddedHistoryEvent'
          AssigneeRemovedHistoryEvent: '#/components/schemas/AssigneeRemovedHistoryEvent'
          AssigneeReplacedHistoryEvent: '#/components/schemas/AssigneeReplacedHistoryEvent'
          AssignmentCreatedHistoryEvent: '#/components/schemas/AssignmentCreatedHistoryEvent'
          AssignmentExpiredHistoryEvent: '#/components/schemas/AssignmentExpiredHistoryEvent'
          AssignmentCancelledHistoryEvent: '#/components/schemas/AssignmentCancelledHistoryEvent'
          AssignmentCompletedHistoryEvent: '#/components/schemas/AssignmentCompletedHistoryEvent'
          AssigneeActionUpdatedHistoryEvent: '#/components/schemas/AssigneeActionUpdatedHistoryEvent'
          AssignmentPropertiesUpdatedHistoryEvent: '#/components/schemas/AssignmentPropertiesUpdatedHistoryEvent'
    HistoryEventBaseListResponse:
      type: object
      properties:
        items:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/StageAddedHistoryEvent'
              - $ref: '#/components/schemas/AssigneeAddedHistoryEvent'
              - $ref: '#/components/schemas/AssigneeRemovedHistoryEvent'
              - $ref: '#/components/schemas/AssigneeReplacedHistoryEvent'
              - $ref: '#/components/schemas/AssignmentCreatedHistoryEvent'
              - $ref: '#/components/schemas/AssignmentExpiredHistoryEvent'
              - $ref: '#/components/schemas/AssignmentCancelledHistoryEvent'
              - $ref: '#/components/schemas/AssignmentCompletedHistoryEvent'
              - $ref: '#/components/schemas/AssigneeActionUpdatedHistoryEvent'
              - $ref: '#/components/schemas/AssignmentPropertiesUpdatedHistoryEvent'
          nullable: true
        nextPage:
          type: string
          nullable: true
        resultSetSize:
          type: integer
          format: int32
          readOnly: true
      additionalProperties: false
    StageAddedHistoryEvent:
      allOf:
        - $ref: '#/components/schemas/HistoryEventBase'
        - type: object
          properties:
            actingUserId:
              type: string
              format: uuid
            initialAssignees:
              type: array
              items:
                $ref: '#/components/schemas/AssigneeDetails'
              nullable: true
          additionalProperties: false
    UpdateAssigneeRequest:
      required:
        - action
      type: object
      properties:
        action:
          minLength: 1
          type: string
        actingUserId:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            type: string
          nullable: true
      additionalProperties: false
    UpdateAssignmentRequest:
      type: object
      properties:
        expirationDate:
          type: string
          format: date-time
          nullable: true
        dueDate:
          type: string
          format: date-time
          nullable: true
        description:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            type: string
          nullable: true
      additionalProperties: false
  securitySchemes:
    Bearer:
      type: http
      description: JWT Bearer token from DocuSign authorization server
      scheme: bearer
      bearerFormat: JWT
security:
  - Bearer: [ ]