using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

using DocuSign.eSign.Api;
using DocuSign.eSign.Client;
using DocuSign.eSign.Model;

using Microsoft.Extensions.Logging;

using Moq;
using Moq.Protected;

using NSubstitute;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Infrastructure.Interfaces;
using TaskManagementService.Infrastructure.ServiceIntegrations.EnvelopeApi;
using TaskManagementService.Infrastructure.Services;

using Xunit;

namespace TaskManagementService.Tests.Services;

[Trait("TestType", "UnitTest")]
public class ESignTaskServiceTest
{
    private readonly Mock<IEnvelopeApiConfig> _mockEnvelopeApiConfig;
    private readonly Mock<IEsignApiClient> _mockEsignApiClient;
    private readonly Mock<IEnvelopesApi> _mockEnvelopesApi;
    private readonly Mock<ISigningGroupsApi> _mockSigningGroupsApi;
    private readonly Mock<IRequestContextService> _mockRequestContextService;
    private readonly Mock<ILogger<EnvelopeApiService>> _mockLogger;
    private readonly Mock<HttpClient> _mockHttpClient;

    public ESignTaskServiceTest()
    {
        _mockEnvelopeApiConfig = new Mock<IEnvelopeApiConfig>();
        _mockEsignApiClient = new Mock<IEsignApiClient>();
        _mockEnvelopesApi = new Mock<IEnvelopesApi>();
        _mockSigningGroupsApi = new Mock<ISigningGroupsApi>();
        _mockRequestContextService = new Mock<IRequestContextService>();
        _mockLogger = new Mock<ILogger<EnvelopeApiService>>();

        _mockRequestContextService.Setup(m => m.ESignApiBaseUrl).Returns(new Uri("https://docusign.com"));

        // Default HttpClient (can be replaced in tests)
        _mockHttpClient = new Mock<HttpClient>();

        // Default EsignApiClient setups
        _mockEsignApiClient
            .Setup(m => m.GetEsignEnvelopeApiClientAsync(It.IsAny<DocuSignClient>()))
            .ReturnsAsync(_mockEnvelopesApi.Object);

        _mockEsignApiClient
            .Setup(m => m.GetEsignSigningGroupsApiClientAsync(It.IsAny<DocuSignClient>()))
            .ReturnsAsync(_mockSigningGroupsApi.Object);
    }

    [Fact]
    public async Task TestSearchTasksAsync()
    {
        var assigneeUserId1 = Guid.NewGuid();
        var assigneeUserId2 = Guid.NewGuid();

        var signingGroups = new SigningGroupInformation(
        [
            new SigningGroup
            {
                CreatedBy = "createdBy",
                GroupEmail = "<EMAIL>",
                GroupName = "groupName",
                GroupType = "groupType",
                ModifiedBy = "modifiedBy"
            }
        ]);

        var envelope1 = new Envelope { EnvelopeId = Guid.NewGuid().ToString(), Sender = new UserInfo { UserId = Guid.NewGuid().ToString() }, Recipients = GenerateRecipients(assigneeUserId1), Status = "sent" };
        var envelope2 = new Envelope { EnvelopeId = Guid.NewGuid().ToString(), Sender = new UserInfo { UserId = Guid.NewGuid().ToString() }, Recipients = GenerateRecipients(assigneeUserId2), Status = "sent" };
        var envelope3 = new Envelope { EnvelopeId = Guid.NewGuid().ToString(), Sender = new UserInfo { UserId = Guid.NewGuid().ToString() }, Recipients = GenerateRecipients(assigneeUserId1), Status = "authfailed" };

        _mockEnvelopesApi
            .Setup(m => m.ListStatusChangesAsync(It.IsAny<string>(), It.Is<EnvelopesApi.ListStatusChangesOptions>(o => o.orderBy == "sent" && o.count == "2")))
            .ReturnsAsync((string accountId, EnvelopesApi.ListStatusChangesOptions options) =>
            {
                if (options.folderIds == "awaiting_my_signature")
                {
                    return new EnvelopesInformation { Envelopes = [envelope1, envelope2] };
                }

                if (options.status == "AuthFailed")
                {
                    return new EnvelopesInformation { Envelopes = [envelope3] };
                }

                return null;
            });

        _mockSigningGroupsApi
            .Setup(m => m.ListAsync(It.IsAny<string>(), It.IsAny<SigningGroupsApi.ListOptions>()))
            .ReturnsAsync(signingGroups);

        _mockRequestContextService.Setup(m => m.UserId).Returns(assigneeUserId1);

        var eSignTaskService = new ESignTaskService(
            _mockEnvelopeApiConfig.Object,
            _mockEsignApiClient.Object,
            _mockHttpClient.Object,
            _mockRequestContextService.Object,
            _mockLogger.Object);

        var result = await eSignTaskService.GetTasksAsync(Guid.NewGuid(), new TaskFilter { AssignedUserIds = new[] { assigneeUserId1 }, TaskSort = new TaskSort { ResultCount = 2, SortColumn = SortColumn.AssignedDate } });

        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(TaskType.NeedsToSign, result.First().Type);

        var result2 = await eSignTaskService.GetTasksGroupsAsync(Guid.NewGuid(), includeMembers: false);
        Assert.NotNull(result2);
        Assert.Single(result2);
    }

    [Fact]
    public async Task GetTasksGroupsAsyncReturnEmptyListForApiException()
    {
        _mockSigningGroupsApi
            .Setup(m => m.ListAsync(It.IsAny<string>(), null))
            .ThrowsAsync(new ApiException(400, "Test Exception"));

        var eSignTaskService = new ESignTaskService(
            _mockEnvelopeApiConfig.Object,
            _mockEsignApiClient.Object,
            _mockHttpClient.Object,
            _mockRequestContextService.Object,
            _mockLogger.Object);

        var response = await eSignTaskService.GetTasksGroupsAsync(Guid.NewGuid(), includeMembers: false);

        Assert.NotNull(response);
        Assert.Empty(response);
    }

    [Fact]
    public async Task TestVoidEnvelopeAsyncWhenUserIsNotSender()
    {
        _mockEnvelopesApi
            .Setup(m => m.UpdateAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Envelope>(), It.IsAny<EnvelopesApi.UpdateOptions>()))
            .ReturnsAsync(new EnvelopeUpdateSummary());
        var envelope = new Envelope { EnvelopeId = Guid.NewGuid().ToString(), Sender = new UserInfo { UserId = Guid.NewGuid().ToString() } };
        _mockEnvelopesApi
            .Setup(m => m.GetEnvelopeAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<EnvelopesApi.GetEnvelopeOptions>()))
            .ReturnsAsync(envelope);

        var eSignTaskService = new ESignTaskService(
            _mockEnvelopeApiConfig.Object,
            _mockEsignApiClient.Object,
            _mockHttpClient.Object,
            _mockRequestContextService.Object,
            _mockLogger.Object);

        await Assert.ThrowsAsync<ValidationException>(() => eSignTaskService.VoidEnvelopeAsync(Guid.NewGuid(), Guid.NewGuid().ToString(), "Test Reason"));
    }

    [Fact]
    public Task TestVoidEnvelopeAsyncWhenUserIsSender()
    {
        _mockEnvelopesApi
            .Setup(m => m.UpdateAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Envelope>(), It.IsAny<EnvelopesApi.UpdateOptions>()))
            .ReturnsAsync(new EnvelopeUpdateSummary());

        var userId = Guid.NewGuid();
        var envelope = new Envelope { EnvelopeId = Guid.NewGuid().ToString(), Sender = new UserInfo { UserId = userId.ToString() } };
        _mockRequestContextService.Setup(x => x.UserId).Returns(userId);
        _mockEnvelopesApi
            .Setup(m => m.GetEnvelopeAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<EnvelopesApi.GetEnvelopeOptions>()))
            .ReturnsAsync(envelope);

        var eSignTaskService = new ESignTaskService(
            _mockEnvelopeApiConfig.Object,
            _mockEsignApiClient.Object,
            _mockHttpClient.Object,
            _mockRequestContextService.Object,
            _mockLogger.Object);

        return eSignTaskService.VoidEnvelopeAsync(Guid.NewGuid(), Guid.NewGuid().ToString(), "Test Reason");
    }

    [Fact]
    public async Task TestResendEnvelopeAsyncWhenUserIsNotSender()
    {
        _mockEnvelopesApi
            .Setup(m => m.UpdateAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Envelope>(), It.IsAny<EnvelopesApi.UpdateOptions>()))
            .ReturnsAsync(new EnvelopeUpdateSummary());

        var envelope = new Envelope { EnvelopeId = Guid.NewGuid().ToString(), Sender = new UserInfo { UserId = Guid.NewGuid().ToString() } };
        _mockEnvelopesApi
            .Setup(m => m.GetEnvelopeAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<EnvelopesApi.GetEnvelopeOptions>()))
            .ReturnsAsync(envelope);

        var eSignTaskService = new ESignTaskService(
            _mockEnvelopeApiConfig.Object,
            _mockEsignApiClient.Object,
            _mockHttpClient.Object,
            _mockRequestContextService.Object,
            _mockLogger.Object);

        await Assert.ThrowsAsync<ValidationException>(() => eSignTaskService.ResendEnvelopeAsync(Guid.NewGuid(), Guid.NewGuid().ToString()));
    }

    [Fact]
    public Task TestResendEnvelopeAsyncWhenUserIsSender()
    {
        _mockEnvelopesApi
            .Setup(m => m.UpdateAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Envelope>(), It.IsAny<EnvelopesApi.UpdateOptions>()))
            .ReturnsAsync(new EnvelopeUpdateSummary());

        var userId = Guid.NewGuid();
        var envelope = new Envelope { EnvelopeId = Guid.NewGuid().ToString(), Sender = new UserInfo { UserId = userId.ToString() } };
        _mockRequestContextService.Setup(x => x.UserId).Returns(userId);
        _mockEnvelopesApi
            .Setup(m => m.GetEnvelopeAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<EnvelopesApi.GetEnvelopeOptions>()))
            .ReturnsAsync(envelope);

        var eSignTaskService = new ESignTaskService(
            _mockEnvelopeApiConfig.Object,
            _mockEsignApiClient.Object,
            _mockHttpClient.Object,
            _mockRequestContextService.Object,
            _mockLogger.Object);

        return eSignTaskService.ResendEnvelopeAsync(Guid.NewGuid(), Guid.NewGuid().ToString());
    }

    [Fact]
    public async Task TestGetEnvelopeHistoryAsync()
    {
        var httpMessageHandlerMock = new Mock<HttpMessageHandler>();
        var apiResponse = new EnvelopeAuditEventResponse()
        {
            AuditEvents = [
                new EnvelopeAuditEvent
                {
                    EventFields = [
                        new NameValue { Name = "logTime", Value = "12:01:03" },
                        new NameValue { Name = "Message", Value = "Fake Test Message" },
                    ]
                },
                new EnvelopeAuditEvent
                {
                    EventFields = [
                        new NameValue { Name = "logTime", Value = "12:02:03" },
                        new NameValue { Name = "Message", Value = "Fake Test Message 2" },
                    ]
                }
            ]
        };
        var responseContent = JsonSerializer.Serialize(apiResponse);
        var response = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
            Content = new StringContent(responseContent)
        };

        httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(response);

        var httpClient = new HttpClient(httpMessageHandlerMock.Object);

        _mockRequestContextService.Setup(m => m.ESignApiBaseUrl).Returns(new Uri("https://docusign.com/restapi"));

        var eSignTaskService = new ESignTaskService(
            _mockEnvelopeApiConfig.Object,
            _mockEsignApiClient.Object,
            httpClient,
            _mockRequestContextService.Object,
            _mockLogger.Object);

        var result = await eSignTaskService.GetEnvelopeHistoryAsync(Guid.NewGuid(), Guid.NewGuid());

        Assert.NotNull(result);
        Assert.Equal(2, result.AuditEvents.Count());
        Assert.Equal("Fake Test Message", result.AuditEvents.First().Message);
        Assert.Equal("12:01:03", result.AuditEvents.First().CreatedDate);
        Assert.Equal(TaskSource.ESignature, result.Source);

        httpClient.Dispose();
        response.Dispose();
    }

    [Fact]
    public void CreateUserTasksFromEnvelopeWithDuplicateAssignees()
    {
        var userId = Guid.NewGuid().ToString();
        var envelope = new Envelope
        {
            EnvelopeId = Guid.NewGuid().ToString(),
            Status = "sent",
            EmailSubject = "Test Subject",
            Sender = new UserInfo { UserId = Guid.NewGuid().ToString() },
            Recipients = new Recipients
            {
                Signers =
                [
                    new Signer
                    {
                        UserId = userId,
                        Name = "Duplicate User",
                        Email = "<EMAIL>",
                        RoutingOrder = "1",
                        Status = "sent"
                    },
                    new Signer
                    {
                        UserId = userId,
                        Name = "Duplicate User",
                        Email = "<EMAIL>",
                        RoutingOrder = "2",
                        Status = "sent"
                    }
                ]
            }
        };

        var methodInfo = typeof(ESignTaskService).GetMethod(
            "CreateUserTaskFromEnvelope",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        var result = methodInfo!.Invoke(null, [envelope, userId]) as UserTask;

        Assert.NotNull(result);
        Assert.Equal(TaskType.NeedsToSign, result.Type);
        Assert.Equal(envelope.EmailSubject, result.Title);
    }

    [Fact]
    public void CreateUserTasksFromEnvelopeWithMultipleRecipients()
    {
        var userId = Guid.NewGuid().ToString();
        var envelope = new Envelope
        {
            EnvelopeId = Guid.NewGuid().ToString(),
            Status = "sent",
            EmailSubject = "Test Subject",
            Sender = new UserInfo { UserId = Guid.NewGuid().ToString() },
            Recipients = new Recipients
            {
                Signers =
                [
                    new Signer
                    {
                        UserId = userId,
                        Name = "User",
                        Email = "<EMAIL>",
                        RoutingOrder = "3",
                        Status = "sent"
                    },
                    new Signer
                    {
                        SigningGroupId = Guid.NewGuid().ToString(),
                        RoutingOrder = "4",
                        Status = "sent",
                        SigningGroupName = "Task Group",
                        SigningGroupUsers =
                        [
                            new() { UserId = userId, Email = "<EMAIL>", UserName = "User" },
                        ]
                    }
                ]
            }
        };

        var methodInfo = typeof(ESignTaskService).GetMethod(
            "CreateUserTaskFromEnvelope",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        var result = methodInfo!.Invoke(null, [envelope, userId]) as UserTask;

        Assert.NotNull(result);
        Assert.Equal(TaskType.NeedsToSign, result.Type);
        Assert.Equal(envelope.EmailSubject, result.Title);
        Assert.NotNull(result.Assignees);
        Assert.Equal(3, result.Assignees.First().Order);
    }

    [Fact]
    public void CreateUserTasksFromEnvelopeWithAuthFailedStatus()
    {
        var guid = Guid.NewGuid().ToString();
        var envelope = new Envelope
        {
            EnvelopeId = Guid.NewGuid().ToString(),
            Status = "authfailed",
            EmailSubject = "Test Subject",
            Sender = new UserInfo { UserId = Guid.NewGuid().ToString() },
            Recipients = new Recipients
            {
                Signers =
                [
                    new Signer
                    {
                        UserId = guid,
                        Name = "Signer 1",
                        Email = "<EMAIL>",
                        RoutingOrder = "1",
                        Status = "sent"
                    }
                ]
            }
        };

        var methodInfo = typeof(ESignTaskService).GetMethod(
            "CreateUserTaskFromEnvelope",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        var result = methodInfo!.Invoke(null, [envelope, guid]) as UserTask;

        Assert.NotNull(result);
        Assert.Equal(TaskType.VerificationFailed, result.Type);
        Assert.Equal(envelope.EmailSubject, result.Title);
    }

    [Fact]
    public async Task VerifyEsignBaseUrl()
    {
        _mockRequestContextService.Setup(m => m.ESignApiBaseUrl).Returns(new Uri("https://docusign.com/restapi"));
        var assigneeUserId1 = Guid.NewGuid();
        var assigneeUserId2 = Guid.NewGuid();

        var signingGroups = new SigningGroupInformation(
        [
            new SigningGroup
            {
                CreatedBy = "createdBy",
                GroupEmail = "<EMAIL>",
                GroupName = "groupName",
                GroupType = "groupType",
                ModifiedBy = "modifiedBy"
            }
        ]);

        var envelope1 = new Envelope { EnvelopeId = Guid.NewGuid().ToString(), Sender = new UserInfo { UserId = Guid.NewGuid().ToString() }, Recipients = GenerateRecipients(assigneeUserId1), Status = "sent" };
        var envelope2 = new Envelope { EnvelopeId = Guid.NewGuid().ToString(), Sender = new UserInfo { UserId = Guid.NewGuid().ToString() }, Recipients = GenerateRecipients(assigneeUserId2), Status = "sent" };
        var envelope3 = new Envelope { EnvelopeId = Guid.NewGuid().ToString(), Sender = new UserInfo { UserId = Guid.NewGuid().ToString() }, Recipients = GenerateRecipients(assigneeUserId1), Status = "authfailed" };

        _mockEnvelopesApi
            .Setup(m => m.ListStatusChangesAsync(It.IsAny<string>(), It.Is<EnvelopesApi.ListStatusChangesOptions>(o => o.count == "2")))
            .ReturnsAsync((string accountId, EnvelopesApi.ListStatusChangesOptions options) =>
            {
                if (options.folderIds == "awaiting_my_signature")
                {
                    return new EnvelopesInformation { Envelopes = [envelope1, envelope2] };
                }

                if (options.status == "AuthFailed")
                {
                    return new EnvelopesInformation { Envelopes = [envelope3] };
                }

                return null;
            });

        _mockSigningGroupsApi
            .Setup(m => m.ListAsync(It.IsAny<string>(), It.IsAny<SigningGroupsApi.ListOptions>()))
            .ReturnsAsync(signingGroups);

        _mockEsignApiClient
            .Setup(m => m.GetEsignEnvelopeApiClientAsync(It.Is<DocuSignClient>(client => client.GetBasePath().Contains("https://docusign.com/restapi"))))
            .ReturnsAsync(_mockEnvelopesApi.Object);

        _mockEsignApiClient
            .Setup(m => m.GetEsignSigningGroupsApiClientAsync(It.Is<DocuSignClient>(client => client.GetBasePath().Contains("https://docusign.com/restapi"))))
            .ReturnsAsync(_mockSigningGroupsApi.Object);

        _mockRequestContextService.Setup(m => m.UserId).Returns(assigneeUserId1);

        var eSignTaskService = new ESignTaskService(
            _mockEnvelopeApiConfig.Object,
            _mockEsignApiClient.Object,
            _mockHttpClient.Object,
            _mockRequestContextService.Object,
            _mockLogger.Object);

        var result = await eSignTaskService.GetTasksAsync(Guid.NewGuid(), new TaskFilter { AssignedUserIds = new[] { assigneeUserId1 }, TaskSort = new TaskSort { ResultCount = 2 } });

        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(TaskType.NeedsToSign, result.First().Type);

        var result2 = await eSignTaskService.GetTasksGroupsAsync(Guid.NewGuid(), includeMembers: false);
        Assert.NotNull(result2);
        Assert.Single(result2);
    }

    [Fact]
    public async Task GetTasksGroupsAsyncWithIncludeMembersTrueReturnsGroupsWithMembers()
    {
        var accountId = Guid.NewGuid();

        var signingGroups = new SigningGroupInformation
        {
            Groups = [
                new SigningGroup
                {
                    SigningGroupId = "group-1",
                    GroupName = "Group 1",
                    Users = [
                        new SigningGroupUser { Email = "<EMAIL>", UserName = "User 1" },
                        new SigningGroupUser { Email = "<EMAIL>", UserName = "User 2" }
                    ]
                },
                new SigningGroup
                {
                    SigningGroupId = "group-2",
                    GroupName = "Group 2",
                    Users = [
                        new SigningGroupUser { Email = "<EMAIL>", UserName = "User 3" }
                    ]
                }
            ]
        };

        _mockSigningGroupsApi
            .Setup(m => m.ListAsync(accountId.ToString(), It.Is<SigningGroupsApi.ListOptions>(o => o.includeUsers == "true")))
            .ReturnsAsync(signingGroups);

        var eSignTaskService = new ESignTaskService(
            _mockEnvelopeApiConfig.Object,
            _mockEsignApiClient.Object,
            _mockHttpClient.Object,
            _mockRequestContextService.Object,
            _mockLogger.Object);

        var result = await eSignTaskService.GetTasksGroupsAsync(accountId, includeMembers: true);

        Assert.NotNull(result);
        Assert.Equal(2, result.Count);

        Assert.Equal("group-1", result[0].Id);
        Assert.Equal("Group 1", result[0].Name);
        Assert.Equal(TaskSource.ESignature, result[0].Source);
        Assert.Equal(2, result[0].Members.Count());

        Assert.Equal("group-2", result[1].Id);
        Assert.Equal("Group 2", result[1].Name);
        Assert.Equal(TaskSource.ESignature, result[1].Source);
        Assert.Single(result[1].Members);

        _mockSigningGroupsApi.Verify(m => m.ListAsync(accountId.ToString(), It.Is<SigningGroupsApi.ListOptions>(o => o.includeUsers == "true")), Times.Once);
    }

    [Fact]
    public async Task GetTasksAsyncWithTaskGroupAssigneeReturnsTasksWithGroupMembers()
    {
        var signingGroupId = "group-1";
        var userId1 = Guid.NewGuid();
        var userId2 = Guid.NewGuid();

        var envelope = new Envelope
        {
            EnvelopeId = Guid.NewGuid().ToString(),
            Sender = new UserInfo { UserId = Guid.NewGuid().ToString() },
            Status = "sent",
            Recipients = new Recipients
            {
                Signers = [
                    new Signer
                    {
                        SigningGroupId = signingGroupId,
                        RoutingOrder = "1",
                        Status = "sent",
                        SigningGroupName = "Task Group",
                        SigningGroupUsers =
                        [
                            new() { UserId = userId1.ToString(), Email = "<EMAIL>", UserName = "User One" },
                            new () { UserId = userId2.ToString(), Email = "<EMAIL>", UserName = "User Two" },
                        ]
                    }
                ]
            }
        };

        _mockEnvelopesApi
            .Setup(m => m.ListStatusChangesAsync(It.IsAny<string>(), It.Is<EnvelopesApi.ListStatusChangesOptions>(opt => opt.folderIds == "awaiting_my_signature" && opt.orderBy == "action_required")))
            .ReturnsAsync(new EnvelopesInformation { Envelopes = [envelope] });

        _mockEnvelopesApi
            .Setup(m => m.ListStatusChangesAsync(It.IsAny<string>(), It.Is<EnvelopesApi.ListStatusChangesOptions>(opt => opt.status == "AuthFailed")))
            .ReturnsAsync(new EnvelopesInformation { Envelopes = [] });

        _mockRequestContextService.Setup(m => m.UserId).Returns(userId1);

        var eSignTaskService = new ESignTaskService(
            _mockEnvelopeApiConfig.Object,
            _mockEsignApiClient.Object,
            _mockHttpClient.Object,
            _mockRequestContextService.Object,
            _mockLogger.Object);

        var result = await eSignTaskService.GetTasksAsync(Guid.NewGuid(), new TaskFilter { TaskSort = new TaskSort { SortColumn = SortColumn.DueDate } });

        Assert.NotNull(result);
        Assert.Single(result);

        var task = result.First();
        Assert.Equal(TaskType.NeedsToSign, task.Type);

        Assert.NotNull(task.TaskGroupAssignee);
        Assert.Equal("Task Group", task.TaskGroupAssignee.Name);
        Assert.Equal(signingGroupId, task.TaskGroupAssignee.Id);
        Assert.Equal(TaskSource.ESignature, task.TaskGroupAssignee.Source);

        Assert.NotNull(task.TaskGroupAssignee.Members);
        Assert.Equal(2, task.TaskGroupAssignee.Members.Count());

        var members = task.TaskGroupAssignee.Members.ToList();

        Assert.Equal(userId1, members[0].Id);
        Assert.Equal("User One", members[0].FullName);
        Assert.Equal("<EMAIL>", members[0].Email);

        Assert.Equal(userId2, members[1].Id);
        Assert.Equal("User Two", members[1].FullName);
        Assert.Equal("<EMAIL>", members[1].Email);
    }

    [Fact]
    public async Task GetTasksAsyncThrowsArgumentNullExceptionWhenTaskFilterIsNull()
    {
        var eSignTaskService = new ESignTaskService(
            _mockEnvelopeApiConfig.Object,
            _mockEsignApiClient.Object,
            _mockHttpClient.Object,
            _mockRequestContextService.Object,
            _mockLogger.Object);

        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            eSignTaskService.GetTasksAsync(Guid.NewGuid(), null));
    }

    [Fact]
    public async Task GetTasksAsyncReturnsEmptyListWhenOnlyUnassignedIsTrue()
    {
        var eSignTaskService = new ESignTaskService(
            _mockEnvelopeApiConfig.Object,
            _mockEsignApiClient.Object,
            _mockHttpClient.Object,
            _mockRequestContextService.Object,
            _mockLogger.Object);

        var result = await eSignTaskService.GetTasksAsync(Guid.NewGuid(), new TaskFilter { OnlyUnassigned = true });

        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetTasksAsyncRetriesOnTransientApiException()
    {
        var callCount = 0;
        _mockEnvelopesApi
            .Setup(m => m.ListStatusChangesAsync(It.IsAny<string>(), It.IsAny<EnvelopesApi.ListStatusChangesOptions>()))
            .Returns(async () =>
            {
                callCount++;
                if (callCount < 3)
                {
                    throw new ApiException(408, "Request Timeout");
                }

                return await Task.FromResult(new EnvelopesInformation { Envelopes = [] });
            });

        var eSignTaskService = new ESignTaskService(
            _mockEnvelopeApiConfig.Object,
            _mockEsignApiClient.Object,
            _mockHttpClient.Object,
            _mockRequestContextService.Object,
            _mockLogger.Object);

        var result = await eSignTaskService.GetTasksAsync(Guid.NewGuid(), new TaskFilter());

        Assert.NotNull(result);
        Assert.Empty(result);
        Assert.True(callCount >= 3); // Should have retried at least twice
    }

    private static Recipients GenerateRecipients(Guid assignedUserId) =>
        new()
        {
            Signers =
            [
                new() { UserId = assignedUserId.ToString() }
            ],
            InPersonSigners = [],
            CarbonCopies = [],
            CertifiedDeliveries = [],
            Editors = [],
            Intermediaries = [],
            Agents = [],
        };
}
