﻿using System;
using System.Net;
using System.Threading.Tasks;

using Xunit;

namespace TaskManagementService.Tests;

[Trait("TestType", "ComponentTest")]
public class HomeControllerComponentTest : IClassFixture<CustomWebApplicationFactory<Program>>
{
    private readonly CustomWebApplicationFactory<Program> _factory;

    public HomeControllerComponentTest(CustomWebApplicationFactory<Program> factory) => _factory = factory;

    [Fact(Skip = "Rewrite")]
    public async Task SimpleTestAsync()
    {
        // Arrange
        using var client = _factory.CreateClient();

        // Act
        var response = await client.GetAsync(new Uri("https://localhost:5001/health"));

        var responseContent = await response.Content.ReadAsStringAsync();

        // Assert
        Assert.NotNull(responseContent);
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }
}
