﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

using MediatR;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using TaskManagementService.Core.Authorization;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

namespace TaskManagementService.Controllers;

[ApiController]
[Authorize(AuthPolicy.JwtToken)]
[Produces("application/json")]
[Route("accounts/{accountId:guid}/users")]
public class UserController(IMediator mediator, IRequestContextService requestContextService) : ControllerBase
{
    [HttpGet("managed-users")]
    public async Task<ActionResult<List<ManagedUser>>> GetManagedUsersAsync(Guid accountId, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(mediator, nameof(mediator));

        var response = await mediator.Send(new GetUserManagedUsersQuery(accountId), cancellationToken);
        return response;
    }

    [HttpGet("current-user")]
    public async Task<ActionResult<ClmUserAccount>> GetCurrentClmUserAsync(CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(mediator, nameof(mediator));

        var response = await mediator.Send(new GetClmUserAccountQuery(requestContextService), cancellationToken);
        if (response is null)
        {
            return NoContent();
        }

        return response;
    }
}
