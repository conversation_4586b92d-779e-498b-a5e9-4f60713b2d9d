// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class FilterValues : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The dateRange property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.DateRangeType? DateRange { get; set; }
        /// <summary>The operator property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Operator { get; set; }
#nullable restore
#else
        public string Operator { get; set; }
#endif
        /// <summary>The range property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.RangeValue? Range { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.RangeValue Range { get; set; }
#endif
        /// <summary>The value property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Value { get; set; }
#nullable restore
#else
        public string Value { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.FilterValues"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.FilterValues CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.FilterValues();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "dateRange", n => { DateRange = n.GetEnumValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.DateRangeType>(); } },
                { "operator", n => { Operator = n.GetStringValue(); } },
                { "range", n => { Range = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.RangeValue>(global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.RangeValue.CreateFromDiscriminatorValue); } },
                { "value", n => { Value = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteEnumValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.DateRangeType>("dateRange", DateRange);
            writer.WriteStringValue("operator", Operator);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.RangeValue>("range", Range);
            writer.WriteStringValue("value", Value);
        }
    }
}
#pragma warning restore CS0618
