﻿using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Authentication;
using Microsoft.Kiota.Http.HttpClientLibrary;

using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models;

namespace TaskManagementService.Infrastructure.Services;

public class ManagedUserService(ILogger<ManagedUserService> logger, IAuthenticationProvider authenticationProvider, HttpClient httpClient, IRequestContextService requestContextService, CLMV2APIClient? clmV2ApiClient = null) : IUserService
{
    public CLMV2APIClient? GetCLMV2APIClient()
    {
        if (clmV2ApiClient != null)
        {
            return clmV2ApiClient;
        }

        if (!requestContextService.IsClmAccount || requestContextService.ClmApiBaseUrl == null)
        {
            return null;
        }

        using var requestAdapter = new HttpClientRequestAdapter(authenticationProvider, httpClient: httpClient);
        requestAdapter.BaseUrl = requestContextService.ClmApiBaseUrl.ToString();
        return new CLMV2APIClient(requestAdapter);
    }

    public async Task<List<ManagedUser>> GetManagedUsersAsync(Guid accountId)
    {
        var clmClient = GetCLMV2APIClient();
        if (clmClient == null)
        {
            return [];
        }

        try
        {
            var managedUsers = await clmClient.V2[accountId.ToString()].Members.Current.Managedusers.Tasks.GetAsync();
            if (managedUsers?.Items?.Count > 0)
            {
                return ConvertUsers(managedUsers.Items).ToList();
            }
        }
        catch (ApiException e)
        {
            logger.LogError(e, "CLM V2 API Exception");
        }

        return [];
    }

    private static List<ManagedUser> ConvertUsers(List<TaskManagedUser> users)
    {
        return users.Select(ConvertUser).ToList();
    }

    private static ManagedUser ConvertUser(TaskManagedUser user) => new ManagedUser
    {
        Id = user.Uid ?? Guid.Empty,
        Name = user.Name ?? string.Empty,
        ManagedUsers = user.Children?.Select(ConvertUser).ToList() ?? [],
        TaskCount = user.Count ?? 0
    };
}
