using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using Moq;

using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Handlers;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

using Xunit;

namespace TaskManagementService.Tests.Handlers;

[Trait("TestType", "UnitTest")]
public class GetUserTaskHistoryQueryHandlerTest
{
    [Fact]
    public async Task HandleClmTaskHistoryAsync()
    {
        var mockLogger = new Mock<ILogger<GetUserTaskHistoryQueryHandler>>();
        var mockClmUserTaskService = new Mock<IClmTaskService>();
        var mockESignTaskService = new Mock<IESignTaskService>();

        var mockAccountId = new Guid("********-0004-0001-000b-************");
        var mockTaskId = new Guid("********-0004-0001-000b-************");
        var startDate = "2024-12-23T00:39:51.397Z";
        var dueDate = "2025-01-23T00:39:51.397Z";
        var mockMessage = "message";
        var mockTitle = "title";

        var mockTaskHistoryAuditEvent = new TaskHistoryAuditEvent() { CreatedDate = startDate, AssignedUser = mockAccountId.ToString(), DueDate = dueDate, IsCompleted = true, Message = mockMessage, Title = mockTitle };
        var mockTaskHistory = new TaskHistory { TaskId = mockTaskId.ToString(), Source = TaskSource.ClmWorkflow, AuditEvents = [mockTaskHistoryAuditEvent] };

        var mockGetUserTaskHistoryQuery =
            new GetUserTaskHistoryQuery(mockAccountId, TaskSource.ClmWorkflow, mockTaskId);

        mockClmUserTaskService
            .Setup(x => x.GetClmTaskHistoryAsync(mockAccountId, mockTaskId))
            .Returns(Task.FromResult(mockTaskHistory));

        var getUserTaskHistoryQueryHandler =
            new GetUserTaskHistoryQueryHandler(mockLogger.Object, mockClmUserTaskService.Object, mockESignTaskService.Object);

        var result = await getUserTaskHistoryQueryHandler.Handle(mockGetUserTaskHistoryQuery, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal(mockTaskId.ToString(), result.TaskId);
        Assert.Equal(TaskSource.ClmWorkflow, result.Source);
        Assert.NotNull(result.AuditEvents);
        Assert.Single(result.AuditEvents.ToList());

        var event1 = result.AuditEvents.ToList()[0];
        Assert.Equal(startDate, event1.CreatedDate);
        Assert.Equal(dueDate, event1.DueDate);
        Assert.True(event1.IsCompleted);
        Assert.Equal(mockMessage, event1.Message);
        Assert.Equal(mockTitle, event1.Title);
    }

    [Fact]
    public async Task HandleEsignTaskHistoryAsync()
    {
        var mockLogger = new Mock<ILogger<GetUserTaskHistoryQueryHandler>>();
        var mockClmUserTaskService = new Mock<IClmTaskService>();
        var mockESignTaskService = new Mock<IESignTaskService>();
        var mockAccountId = new Guid("********-0004-0001-000b-************");
        var mockTaskId = new Guid("********-0004-0001-000b-************");
        var mockGetUserTaskHistoryQuery =
            new GetUserTaskHistoryQuery(mockAccountId, TaskSource.ESignature, mockTaskId);
        mockESignTaskService
            .Setup(x => x.GetEnvelopeHistoryAsync(mockAccountId, mockTaskId, null, default))
            .Returns(Task.FromResult(new TaskHistory { TaskId = mockTaskId.ToString() }));
        var getUserTaskHistoryQueryHandler =
            new GetUserTaskHistoryQueryHandler(mockLogger.Object, mockClmUserTaskService.Object, mockESignTaskService.Object);
        var result = await getUserTaskHistoryQueryHandler.Handle(mockGetUserTaskHistoryQuery, CancellationToken.None);
        Assert.NotNull(result);
        Assert.Equal(mockTaskId.ToString(), result.TaskId);
    }

    [Fact]
    public async Task HandleIdvTaskHistoryAsync()
    {
        var mockLogger = new Mock<ILogger<GetUserTaskHistoryQueryHandler>>();
        var mockClmUserTaskService = new Mock<IClmTaskService>();
        var mockESignTaskService = new Mock<IESignTaskService>();

        var mockAccountId = new Guid("********-0004-0001-000b-************");
        var mockTaskId = new Guid("********-0004-0001-000b-************");

        var mockGetUserTaskHistoryQuery =
            new GetUserTaskHistoryQuery(mockAccountId, TaskSource.IdVerification, mockTaskId);

        var getUserTaskHistoryQueryHandler =
            new GetUserTaskHistoryQueryHandler(mockLogger.Object, mockClmUserTaskService.Object, mockESignTaskService.Object);

        var result = await getUserTaskHistoryQueryHandler.Handle(mockGetUserTaskHistoryQuery, CancellationToken.None);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task HandleAssignmentsApiTaskHistoryAsync()
    {
        var mockLogger = new Mock<ILogger<GetUserTaskHistoryQueryHandler>>();
        var mockClmUserTaskService = new Mock<IClmTaskService>();
        var mockESignTaskService = new Mock<IESignTaskService>();

        var mockAccountId = new Guid("********-0004-0001-000b-************");
        var mockTaskId = new Guid("********-0004-0001-000b-************");

        var mockGetUserTaskHistoryQuery =
            new GetUserTaskHistoryQuery(mockAccountId, TaskSource.AssignmentsApi, mockTaskId);

        var getUserTaskHistoryQueryHandler =
            new GetUserTaskHistoryQueryHandler(mockLogger.Object, mockClmUserTaskService.Object, mockESignTaskService.Object);

        var result = await getUserTaskHistoryQueryHandler.Handle(mockGetUserTaskHistoryQuery, CancellationToken.None);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task NullHandle()
    {
        var mockLogger = new Mock<ILogger<GetUserTaskHistoryQueryHandler>>();
        var mockClmUserTaskService = new Mock<IClmTaskService>();
        var mockESignTaskService = new Mock<IESignTaskService>();

        var getUserTaskHistoryQueryHandler =
            new GetUserTaskHistoryQueryHandler(mockLogger.Object, mockClmUserTaskService.Object, mockESignTaskService.Object);

        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            getUserTaskHistoryQueryHandler.Handle(null, CancellationToken.None));
    }
}
