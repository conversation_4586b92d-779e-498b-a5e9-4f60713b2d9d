ARG REPO=docker.docusignhq.com
FROM $REPO/dotnet-runtime-8.0:latest
ARG DOTNET_CONFIGURATION=Release
ARG DOTNET_TARGET=net8.0
ARG DOTNET_RUNTIME=linux-x64

RUN apt-get update && \
    apt-get install curl -y && \
    rm -rf /var/lib/apt/lists/*

COPY --chown=app bin/${DOTNET_CONFIGURATION}/${DOTNET_TARGET}/${DOTNET_RUNTIME}/publish/ /app
COPY --chown=app run-periodic-tests.sh /app

# make sure run-periodic-tests.sh is executable
RUN chmod +x /app/run-periodic-tests.sh

USER app

# Pass build information as env variables
ARG BUILD_NUMBER
ARG GIT_SHA
ARG GIT_BRANCH
ENV ServiceVersion__BuildNumber=$BUILD_NUMBER ServiceVersion__GitSha1=$GIT_SHA ServiceVersion__GitBranch=$GIT_BRANCH

WORKDIR /app
ENTRYPOINT [ "/app/run-periodic-tests.sh" ]
