// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class Activity : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The AssignedUser property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? AssignedUser { get; set; }
#nullable restore
#else
        public string AssignedUser { get; set; }
#endif
        /// <summary>The CommentText property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CommentText { get; set; }
#nullable restore
#else
        public string CommentText { get; set; }
#endif
        /// <summary>The CreatedDate property</summary>
        public DateTimeOffset? CreatedDate { get; set; }
        /// <summary>The Description property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Description { get; set; }
#nullable restore
#else
        public string Description { get; set; }
#endif
        /// <summary>The DueDate property</summary>
        public DateTimeOffset? DueDate { get; set; }
        /// <summary>The Email property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Email { get; set; }
#nullable restore
#else
        public string Email { get; set; }
#endif
        /// <summary>The ExternalActivityUrl property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? ExternalActivityUrl { get; set; }
#nullable restore
#else
        public string ExternalActivityUrl { get; set; }
#endif
        /// <summary>The Initials property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Initials { get; set; }
#nullable restore
#else
        public string Initials { get; set; }
#endif
        /// <summary>The IsCompleted property</summary>
        public bool? IsCompleted { get; set; }
        /// <summary>The LastUpdateDate property</summary>
        public DateTimeOffset? LastUpdateDate { get; set; }
        /// <summary>The Title property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Title { get; set; }
#nullable restore
#else
        public string Title { get; set; }
#endif
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Activity"/> and sets the default values.
        /// </summary>
        public Activity()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Activity"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Activity CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Activity();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "AssignedUser", n => { AssignedUser = n.GetStringValue(); } },
                { "CommentText", n => { CommentText = n.GetStringValue(); } },
                { "CreatedDate", n => { CreatedDate = n.GetDateTimeOffsetValue(); } },
                { "Description", n => { Description = n.GetStringValue(); } },
                { "DueDate", n => { DueDate = n.GetDateTimeOffsetValue(); } },
                { "Email", n => { Email = n.GetStringValue(); } },
                { "ExternalActivityUrl", n => { ExternalActivityUrl = n.GetStringValue(); } },
                { "Initials", n => { Initials = n.GetStringValue(); } },
                { "IsCompleted", n => { IsCompleted = n.GetBoolValue(); } },
                { "LastUpdateDate", n => { LastUpdateDate = n.GetDateTimeOffsetValue(); } },
                { "Title", n => { Title = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("AssignedUser", AssignedUser);
            writer.WriteStringValue("CommentText", CommentText);
            writer.WriteDateTimeOffsetValue("CreatedDate", CreatedDate);
            writer.WriteStringValue("Description", Description);
            writer.WriteDateTimeOffsetValue("DueDate", DueDate);
            writer.WriteStringValue("Email", Email);
            writer.WriteStringValue("ExternalActivityUrl", ExternalActivityUrl);
            writer.WriteStringValue("Initials", Initials);
            writer.WriteBoolValue("IsCompleted", IsCompleted);
            writer.WriteDateTimeOffsetValue("LastUpdateDate", LastUpdateDate);
            writer.WriteStringValue("Title", Title);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
