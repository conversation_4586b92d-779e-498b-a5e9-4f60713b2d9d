﻿#pragma warning disable SA1402
#pragma warning disable SA1134
#pragma warning disable CS8618
#pragma warning disable CA2227
#pragma warning disable CA1002

using System.Text.Json.Nodes;
using System.Text.Json.Serialization;

// ReSharper disable UnusedMember.Global
namespace TaskManagementService.Infrastructure.ServiceIntegrations.UnifiedRepositoryApi.Models;

public class AgreementsApiDto
{
    [JsonPropertyName("agreements")] public List<AgreementApiDto> Agreements { get; set; }
    [JsonPropertyName("ctoken")] public string? CToken { get; set; }
}

public class AgreementApiDto
{
    [JsonPropertyName("id")] public Guid Id { get; set; }
    [JsonPropertyName("model")] public string Model { get; set; }
    [JsonPropertyName("source")] public string Source { get; set; }
    [JsonPropertyName("version")] public string Version { get; set; }
    [JsonPropertyName("type")] public string Type { get; set; }
    [JsonPropertyName("createdAt")] public DateTimeOffset CreatedAt { get; set; }
    [JsonPropertyName("modifiedAt")] public DateTimeOffset ModifiedAt { get; set; }
    [JsonPropertyName("etag")] public long Etag { get; set; }
    [JsonPropertyName("data")] public AgreementDataApiDto Data { get; set; }
}

public class AgreementDataApiDto
{
    [JsonPropertyName("parties")] public List<PartyApiDto> Parties { get; set; }
    [JsonPropertyName("agreementDocumentData")] public JsonNode AgreementDocumentData { get; set; }
    [JsonPropertyName("name")] public string Name { get; set; }
    [JsonPropertyName("sourceId")] public Guid SourceId { get; set; }
    [JsonPropertyName("languages")] public List<string> Languages { get; set; }
    [JsonPropertyName("sourceName")] public string SourceName { get; set; }
    [JsonPropertyName("totalValue")] public ValueApiDto TotalValue { get; set; }
    [JsonPropertyName("effectiveDate")] public DateTimeOffset EffectiveDate { get; set; }
    [JsonPropertyName("agreementStatus")] public string AgreementStatus { get; set; }
    [JsonPropertyName("sourceAccountId")] public Guid SourceAccountId { get; set; }
    [JsonPropertyName("extractionStatus")] public string ExtractionStatus { get; set; }
    [JsonPropertyName("documentStorageId")] public Guid DocumentStorageId { get; set; }
    [JsonPropertyName("sourceIngestionId")] public string SourceIngestionId { get; set; }
    [JsonPropertyName("extractionReviews")] public JsonNode ExtractionReviews { get; set; }
    [JsonPropertyName("pendingExtractionReviewCount")] public long PendingExtractionReviewCount { get; set; }
    [JsonPropertyName("id")] public Guid Id { get; set; }
}

public class PartyApiDto
{
    [JsonPropertyName("party")] public PartyDetailsApiDto PartyDetails { get; set; }
    [JsonPropertyName("displayName")] public string DisplayName { get; set; }
    [JsonPropertyName("extractionReview")] public ExtractionReviewApiDto ExtractionReview { get; set; }
}

public class ExtractionReviewApiDto
{
    [JsonPropertyName("extractionCount")] public long ExtractionCount { get; set; }
    [JsonPropertyName("source")] public string Source { get; set; }
}

public class PartyDetailsApiDto
{
    [JsonPropertyName("id")] public Guid Id { get; set; }
}

public class TotalValueApiDto
{
    [JsonPropertyName("value")] public ValueApiDto Value { get; set; }
}

public class ValueApiDto
{
    [JsonPropertyName("doubleValue")] public double DoubleValue { get; set; }
    [JsonPropertyName("currencyCode")] public string CurrencyCode { get; set; }
}

public class AgreementSearchRequestDto
{
    [JsonPropertyName("pageSize")] public int Limit { get; set; } = 100;
    [JsonPropertyName("continuationToken")] public string? CToken { get; set; }
    [JsonPropertyName("filters")] public List<AgreementFilterDto> Filters { get; set; }
    [JsonPropertyName("sort")] public List<AgreementSortDto> Sort { get; set; }
}

public class AgreementFieldDto
{
    [JsonPropertyName("field")] public string Field { get; set; }
    [JsonPropertyName("fieldFullyQualifiedName")] public string FieldFullyQualifiedName { get; set; }
    [JsonPropertyName("parentType")] public string ParentType { get; set; }
    [JsonPropertyName("type")] public string Type { get; set; }
}

public class AgreementFilterDto : AgreementFieldDto
{
    [JsonPropertyName("operator")] public string Operator { get; set; }
    [JsonPropertyName("value")] public List<string> Value { get; set; }
}

public class AgreementSortDto : AgreementFieldDto
{
    [JsonPropertyName("direction")] public string Direction { get; set; }
}
