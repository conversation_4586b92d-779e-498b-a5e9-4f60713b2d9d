using System;
using System.Diagnostics;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

using DocuSign.AccountServer.TokenExchangeClient;
using DocuSign.OneConfig.Extensions;

using OpenTelemetry.Trace;

using Polly;
using Polly.Retry;

using TaskManagementService.Core.Config;

namespace TaskManagementService.Infrastructure.ServiceIntegrations.AccountServer;

public class AccountServerTokenExchangeClient : IAccountServerTokenExchangeClient
{
    private readonly ITokenExchangeClient _tokenExchangeClient;
    private readonly ILogger<AccountServerTokenExchangeClient> _logger;
    private readonly AsyncRetryPolicy<string?> _retryPolicy;

    public AccountServerTokenExchangeClient(
        IAccountServerConfig accountServerConfig,
        ILogger<AccountServerTokenExchangeClient> logger,
        IOptions<TokenExchangeClientSecretsManager> rsaSecretsManager,
        ILogger<HttpClientRetryHandler> httpRetryHandlerLogger)
    {
        ArgumentNullException.ThrowIfNull(accountServerConfig, nameof(accountServerConfig));
        ArgumentNullException.ThrowIfNull(rsaSecretsManager, nameof(rsaSecretsManager));
        ArgumentNullException.ThrowIfNull(httpRetryHandlerLogger, nameof(httpRetryHandlerLogger));

        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _tokenExchangeClient = new TokenExchangeClient(
            new AccountServerConfig(accountServerConfig),
            rsaSecretsManager,
            httpRetryHandlerLogger)
        {
            ExceptionHandler = (ex) =>
            {
                Log.TokenFailure(_logger, ex);
                Activity.Current?.RecordException(ex);
            },
            EventHandler = (eventName, data) => { Log.TokenEvent(_logger, eventName, JsonSerializer.Serialize(data), null); }
        };

        _retryPolicy = GetRetryPolicy(accountServerConfig);
    }

    internal AccountServerTokenExchangeClient(
       IWatchedSingletonConfig<IAccountServerConfig> accountServerConfig,
       ILogger<AccountServerTokenExchangeClient> logger,
       ITokenExchangeClient tokenExchangeClient)
    {
        _logger = logger;
        _tokenExchangeClient = tokenExchangeClient;
        _retryPolicy = GetRetryPolicy(accountServerConfig.Value);
    }

    public async Task<string?> GetAppTokenAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Calling account server token client to retrieve application token");
        var token = await _retryPolicy.ExecuteAsync(_ => _tokenExchangeClient.GetApplicationTokenAsync(), cancellationToken);
        return token;
    }

    private AsyncRetryPolicy<string?> GetRetryPolicy(IAccountServerConfig config)
    {
        var retryWaitPeriod = TimeSpan.FromMilliseconds(config.RetryWaitPeriodMs);

        return Policy
            .HandleResult<string?>(result => result is null)
            .WaitAndRetryAsync(
                config.RetryCount,
                retryAttempt => retryWaitPeriod * retryAttempt,
                onRetry: (response, duration, retryCount, context) => Log.RetryGetToken(_logger, retryCount, null));
    }

    private static class Log
    {
        public static readonly Action<ILogger, Exception?> TokenFailure =
            LoggerMessage.Define(LogLevel.Error, 0, "Failed to acquire application token.");

        public static readonly Action<ILogger, EventName, string, Exception?> TokenEvent =
            LoggerMessage.Define<EventName, string>(LogLevel.Information, 1, "Application token event occurred {EventName}, {SerializedData}.");

        public static readonly Action<ILogger, int, Exception?> RetryGetToken =
            LoggerMessage.Define<int>(LogLevel.Warning, 2, "Retrying getting application token. Retry: {RetryCount}.");
    }
}
