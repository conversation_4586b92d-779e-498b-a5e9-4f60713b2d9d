﻿{
  "AccountId": "********-1234-1234-1234-************",
  "UserId": "dfbb2a1b-e54c-4239-948d-c499923764f5",
  "AccountName": "Test Account",
  "ShardId": "shard-id",
  "Authorization": "Bearer token",
  "TraceParent": "trace-parent",
  "TraceToken": "",
  "RequestPath": "/accounts/********-1234-1234-1234-************/",
  "Claims": [
    {
      "Issuer": "LOCAL AUTHORITY",
      "OriginalIssuer": "LOCAL AUTHORITY",
      "Properties": {},
      "Subject": {
        "AuthenticationType": "TestAuthType",
        "IsAuthenticated": true,
        "Actor": null,
        "BootstrapContext": null,
        "Claims": [
          {
            "Issuer": "LOCAL AUTHORITY",
            "OriginalIssuer": "LOCAL AUTHORITY",
            "Properties": {},
            "Type": "http://schemas.microsoft.com/identity/claims/scope",
            "Value": "spring_read",
            "ValueType": "http://www.w3.org/2001/XMLSchema#string"
          }
        ],
        "Label": null,
        "Name": null,
        "NameClaimType": "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name",
        "RoleClaimType": "http://schemas.microsoft.com/ws/2008/06/identity/claims/role"
      },
      "Type": "UserId",
      "Value": "dfbb2a1b-e54c-4239-948d-c499923764f5",
      "ValueType": "http://www.w3.org/2001/XMLSchema#string"
    },
    {
      "Issuer": "LOCAL AUTHORITY",
      "OriginalIssuer": "LOCAL AUTHORITY",
      "Properties": {},
      "Subject": {
        "AuthenticationType": "TestAuthType",
        "IsAuthenticated": true,
        "Actor": null,
        "BootstrapContext": null,
        "Claims": [
          {
            "Issuer": "LOCAL AUTHORITY",
            "OriginalIssuer": "LOCAL AUTHORITY",
            "Properties": {},
            "Type": "UserId",
            "Value": "dfbb2a1b-e54c-4239-948d-c499923764f5",
            "ValueType": "http://www.w3.org/2001/XMLSchema#string"
          }
        ],
        "Label": null,
        "Name": null,
        "NameClaimType": "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name",
        "RoleClaimType": "http://schemas.microsoft.com/ws/2008/06/identity/claims/role"
      },
      "Type": "http://schemas.microsoft.com/identity/claims/scope",
      "Value": "spring_read",
      "ValueType": "http://www.w3.org/2001/XMLSchema#string"
    }
  ],
  "Issuer": null,
  "IsClmAccount": true,
  "ESignApiBaseUrl": "https://esign.com/restapi",
  "ClmApiBaseUrl": "https://clm.com"
}
