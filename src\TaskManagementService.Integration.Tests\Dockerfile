ARG REPO=docker.docusignhq.com
FROM $REPO/dotnet/sdk:8.0
ARG DOTNET_CONFIGURATION=Release
ARG DOTNET_TARGET=net8.0
ARG DOTNET_RUNTIME=linux-x64

WORKDIR /app
COPY bin/${DOTNET_CONFIGURATION}/${DOTNET_TARGET}/${DOTNET_RUNTIME}/publish/ /app
COPY run-analysis.sh /app

# Make sure run-analysis.sh is executable
RUN chmod +x /app/run-analysis.sh

# Pass build information as env variables
ARG BUILD_NUMBER
ENV BUILD_VER=$BUILD_NUMBER

ENTRYPOINT [ "/bin/bash", "/app/run-analysis.sh" ]
