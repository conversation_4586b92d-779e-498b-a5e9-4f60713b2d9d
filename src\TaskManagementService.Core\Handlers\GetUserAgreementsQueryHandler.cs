﻿using MediatR;

using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

namespace TaskManagementService.Core.Handlers;

public class GetUserAgreementsQueryHandler : IRequestHandler<GetUserAgreementsQuery, Dictionary<Guid, UserTaskAgreement>>
{
    private readonly ILogger<GetUserAgreementsQueryHandler> _logger;
    private readonly IUnifiedRepositoryService _unifiedRepositoryService;

    public GetUserAgreementsQueryHandler(ILogger<GetUserAgreementsQueryHandler> logger, IUnifiedRepositoryService unifiedRepositoryService)
    {
        _logger = logger;
        _unifiedRepositoryService = unifiedRepositoryService;
    }

    public async Task<Dictionary<Guid, UserTaskAgreement>> Handle(GetUserAgreementsQuery request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));

        var getAgreementRequest = new GetAgreementsRequest(request.AgreementIds, request.Filters, request.Sort, true, false);
        var agreements = await _unifiedRepositoryService.GetAgreementsAsync(getAgreementRequest, cancellationToken);
        return agreements;
    }
}
