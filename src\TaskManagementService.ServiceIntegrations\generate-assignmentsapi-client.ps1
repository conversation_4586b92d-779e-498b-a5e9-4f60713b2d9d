# restore the Kiota tool
dotnet tool restore

# list the installed dotnet tools
dotnet tool list --local

# Define the URI of the Swagger file
$swaggerUri = 'https://apiqana11.springcm.com/assignments/swagger/v1/swagger.yaml'

# Informative message about downloading the Swagger file
Write-Host "Downloading Swagger file from the specified URL: $swaggerUri"

# Using Invoke-WebRequest to fetch the file and save it to the Contracts sub-folder
Invoke-WebRequest -Uri $swaggerUri -OutFile '.\Contracts\assignmentsapi.yml'

# Informative message about the location of the downloaded file
Write-Host '✅ Swagger file downloaded successfully and saved the file to ".\Contracts\assignmentsapi.json".'

# Generate the client using the Kiota tool
Write-Host '⏳ Generating the client using the Kiota tool...'
dotnet kiota generate `
    --language csharp `
    --class-name AssignmentsApiClient `
    --namespace-name TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi `
    --serializer Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory `
    --deserializer Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory `
    --structured-mime-types application/json `
    --openapi ./contracts/assignmentsapi.yml `
    --output ./Clients/AssignmentsApi `
    --clean-output

dotnet kiota info -d ".\contracts\assignmentsapi.yml" -l CSharp

Write-Host '✅ Generated the Assignments API client using the Kiota tool.'
