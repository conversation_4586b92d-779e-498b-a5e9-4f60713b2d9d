﻿using MediatR;

using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

namespace TaskManagementService.Core.Handlers;

public class GetUserTasksGroupsQueryHandler : IRequestHandler<GetUserTasksGroupsQuery, List<TaskGroup>>
{
    private readonly ILogger<GetUserTasksGroupsQueryHandler> _logger;
    private readonly IEnumerable<ITaskService> _taskServices;

    public GetUserTasksGroupsQueryHandler(ILogger<GetUserTasksGroupsQueryHandler> logger, IEnumerable<ITaskService> taskServices)
    {
        _logger = logger;
        _taskServices = taskServices;
    }

    public async Task<List<TaskGroup>> Handle(GetUserTasksGroupsQuery request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));

        var fetchTasksGroups = new List<Task<List<TaskGroup>>>();
        foreach (var taskService in _taskServices)
        {
            fetchTasksGroups.Add(taskService.GetTasksGroupsAsync(request.AccountId, request.IncludeMembers));
        }

        await Task.WhenAll(fetchTasksGroups);

        var userTasks = new List<TaskGroup>();
        foreach (var task in fetchTasksGroups)
        {
            userTasks.AddRange(await task);
        }

        return [.. userTasks];
    }
}
