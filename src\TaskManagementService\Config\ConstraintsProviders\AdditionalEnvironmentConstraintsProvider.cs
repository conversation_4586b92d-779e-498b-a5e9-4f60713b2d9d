using System;
using System.Diagnostics.CodeAnalysis;

using DocuSign.OneConfig.Extensions;

namespace TaskManagementService.Config.ConstraintsProviders;

[ExcludeFromCodeCoverage]
public class AdditionalEnvironmentConstraintsProvider : ISingletonConstraintsProvider
{
    public void AddSingletonConstraints(IConstraintCollection constraintCollection)
    {
        ArgumentNullException.ThrowIfNull(constraintCollection, nameof(constraintCollection));

        AddConstraint(constraintCollection, "ASPNETCORE_ENVIRONMENT", "netenv");
        AddConstraint(constraintCollection, "MSF_SHARD_IDS_PRIMARY", "shard");
        AddConstraint(constraintCollection, "CLM_ENVIRONMENT", "clmenv");
    }

    private static void AddConstraint(IConstraintCollection constraintCollection, string envVariableName, string constraintKey, string defaultValue = null)
    {
        var envVariableValue = Environment.GetEnvironmentVariable(envVariableName);

        var constraintValue = string.IsNullOrWhiteSpace(envVariableValue)
            ? defaultValue
            : envVariableValue;

        if (string.IsNullOrWhiteSpace(constraintValue))
        {
            return;
        }

#pragma warning disable CA1308
        if (!constraintCollection.TrySetConstraint(constraintKey, constraintValue.ToLowerInvariant()))
#pragma warning restore CA1308
        {
            throw new InvalidOperationException($"Constraint '{constraintKey}' has already been added.");
        }
    }
}
