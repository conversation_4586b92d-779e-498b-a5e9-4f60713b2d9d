// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
using TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Assignees.Item;
namespace TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Assignees
{
    /// <summary>
    /// Builds and executes requests for operations under \api\v1\{accountId}\Assignments\assignees
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class AssigneesRequestBuilder : BaseRequestBuilder
    {
        /// <summary>Gets an item from the TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.api.v1.item.Assignments.assignees.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Assignees.Item.WithAssigneeItemRequestBuilder"/></returns>
        public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Assignees.Item.WithAssigneeItemRequestBuilder this[Guid position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                urlTplParams.Add("assigneeId", position);
                return new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Assignees.Item.WithAssigneeItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>Gets an item from the TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.api.v1.item.Assignments.assignees.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Assignees.Item.WithAssigneeItemRequestBuilder"/></returns>
        [Obsolete("This indexer is deprecated and will be removed in the next major version. Use the one with the typed parameter instead.")]
        public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Assignees.Item.WithAssigneeItemRequestBuilder this[string position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                if (!string.IsNullOrWhiteSpace(position)) urlTplParams.Add("assigneeId", position);
                return new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Assignees.Item.WithAssigneeItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Assignees.AssigneesRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public AssigneesRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/v1/{accountId}/Assignments/assignees", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Assignees.AssigneesRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public AssigneesRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/v1/{accountId}/Assignments/assignees", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
