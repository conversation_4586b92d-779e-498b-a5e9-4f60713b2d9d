﻿using MediatR;

using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Queries;

namespace TaskManagementService.Core.Handlers;

public class ResendESignEnvelopeCommandHandler : IRequestHandler<ResendESignEnvelopeCommand, Unit>
{
    private readonly ILogger<ResendESignEnvelopeCommandHandler> _logger;
    private readonly IESignTaskService _eSignTaskServices;

    public ResendESignEnvelopeCommandHandler(ILogger<ResendESignEnvelopeCommandHandler> logger, IESignTaskService eSignTaskServices)
    {
        _logger = logger;
        _eSignTaskServices = eSignTaskServices;
    }

    public async Task<Unit> Handle(ResendESignEnvelopeCommand request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));

        _logger.LogInformation("Attempting to resend envelope: {TaskId}", request.TaskId);
        await _eSignTaskServices.ResendEnvelopeAsync(request.AccountId, request.TaskId);
        _logger.LogInformation("After resend attempt for envelope: {TaskId}", request.TaskId);

        return Unit.Value;
    }
}
