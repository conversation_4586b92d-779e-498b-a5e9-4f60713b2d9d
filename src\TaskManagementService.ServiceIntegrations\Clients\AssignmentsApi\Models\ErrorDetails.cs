// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class ErrorDetails : ApiException, IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The developerMessage property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? DeveloperMessage { get; set; }
#nullable restore
#else
        public string DeveloperMessage { get; set; }
#endif
        /// <summary>The errorCode property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? ErrorCode { get; set; }
#nullable restore
#else
        public string ErrorCode { get; set; }
#endif
        /// <summary>The errors property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails_errors? Errors { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails_errors Errors { get; set; }
#endif
        /// <summary>The primary error message.</summary>
        public override string Message { get => base.Message; }
        /// <summary>The userMessage property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? UserMessage { get; set; }
#nullable restore
#else
        public string UserMessage { get; set; }
#endif
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails"/> and sets the default values.
        /// </summary>
        public ErrorDetails()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "developerMessage", n => { DeveloperMessage = n.GetStringValue(); } },
                { "errorCode", n => { ErrorCode = n.GetStringValue(); } },
                { "errors", n => { Errors = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails_errors>(global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails_errors.CreateFromDiscriminatorValue); } },
                { "userMessage", n => { UserMessage = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("developerMessage", DeveloperMessage);
            writer.WriteStringValue("errorCode", ErrorCode);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails_errors>("errors", Errors);
            writer.WriteStringValue("userMessage", UserMessage);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
