// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class StageAddedHistoryEvent : global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.HistoryEventBase, IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The actingUserId property</summary>
        public Guid? ActingUserId { get; set; }
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The initialAssignees property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeDetails>? InitialAssignees { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeDetails> InitialAssignees { get; set; }
#endif
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.StageAddedHistoryEvent"/> and sets the default values.
        /// </summary>
        public StageAddedHistoryEvent() : base()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.StageAddedHistoryEvent"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.StageAddedHistoryEvent CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.StageAddedHistoryEvent();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public override IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>(base.GetFieldDeserializers())
            {
                { "actingUserId", n => { ActingUserId = n.GetGuidValue(); } },
                { "initialAssignees", n => { InitialAssignees = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeDetails>(global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeDetails.CreateFromDiscriminatorValue)?.AsList(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public override void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            base.Serialize(writer);
            writer.WriteGuidValue("actingUserId", ActingUserId);
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeDetails>("initialAssignees", InitialAssignees);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
