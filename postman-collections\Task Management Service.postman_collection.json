{"info": {"_postman_id": "28b2314f-8206-4a71-b402-0cd611c10c44", "name": "Task Management Service", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "********"}, "item": [{"name": "{{base_url}}/accounts/{{ds_account_id}}/tasks", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n \"TaskGroupIds\": [\"baf79089-16d5-ef11-b5b5-e0c2640b8870\"], \r\n \"OnlyUnassigned\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/accounts/{{ds_account_id}}/tasks"}, "response": []}, {"name": "{{base_url}}/accounts/{{ds_account_id}}/tasks/count", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/accounts/{{ds_account_id}}/tasks/count"}, "response": []}, {"name": "{{base_url}}/accounts/{{ds_account_id}}/tasks/{{source}}/{{task_id}}/history", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/accounts/{{ds_account_id}}/tasks/{{source}}/{{task_id}}/history"}, "response": []}, {"name": "{{base_url}}/accounts/{{ds_account_id}}/tasks/esignature/{taskId}/void", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "\"Because\"", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/accounts/{{ds_account_id}}/tasks/esignature/{{task_id}}/void"}, "response": []}, {"name": "{{base_url}}/accounts/{{ds_account_id}}/tasks/esignature/{taskId}/resend", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/accounts/{{ds_account_id}}/tasks/esignature/{{task_id}}/resend"}, "response": []}, {"name": "{{base_url}}/accounts/{{ds_account_id}}/tasks/groups", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": "{{base_url}}/accounts/{{ds_account_id}}/tasks/groups"}, "response": []}, {"name": "{{base_url}}/accounts/{{ds_account_id}}/tasks/clm/{{taskId}}/unassign", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": "{{base_url}}/accounts/{{ds_account_id}}/tasks/clm/{{task_id}}/unassign"}, "response": []}, {"name": "{{base_url}}/accounts/{{ds_account_id}}/tasks/clm/{{taskId}}/assign/{{asigneeId}}", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "bearer {{access_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": "{{base_url}}/accounts/{{ds_account_id}}/tasks/clm/{{task_id}}/assign/{{assigneeId}}"}, "response": []}]}