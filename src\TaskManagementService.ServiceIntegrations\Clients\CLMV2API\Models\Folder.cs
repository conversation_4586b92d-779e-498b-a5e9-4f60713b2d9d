// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class Folder : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The AccessLevel property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel? AccessLevel { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel AccessLevel { get; set; }
#endif
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The AttributeGroups property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder_AttributeGroups? AttributeGroups { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder_AttributeGroups AttributeGroups { get; set; }
#endif
        /// <summary>The BrowseDocumentsUrl property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? BrowseDocumentsUrl { get; set; }
#nullable restore
#else
        public string BrowseDocumentsUrl { get; set; }
#endif
        /// <summary>The CreatedBy property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CreatedBy { get; set; }
#nullable restore
#else
        public string CreatedBy { get; set; }
#endif
        /// <summary>The CreatedDate property</summary>
        public DateTimeOffset? CreatedDate { get; set; }
        /// <summary>The CreateDocumentHref property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CreateDocumentHref { get; private set; }
#nullable restore
#else
        public string CreateDocumentHref { get; private set; }
#endif
        /// <summary>The Description property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Description { get; set; }
#nullable restore
#else
        public string Description { get; set; }
#endif
        /// <summary>The Documents property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument? Documents { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument Documents { get; set; }
#endif
        /// <summary>The EosInfo property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo? EosInfo { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo EosInfo { get; set; }
#endif
        /// <summary>The EosParentInfo property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo? EosParentInfo { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo EosParentInfo { get; set; }
#endif
        /// <summary>The Folders property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionFolder? Folders { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionFolder Folders { get; set; }
#endif
        /// <summary>The Href property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Href { get; set; }
#nullable restore
#else
        public string Href { get; set; }
#endif
        /// <summary>The Name property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Name { get; set; }
#nullable restore
#else
        public string Name { get; set; }
#endif
        /// <summary>The ParentFolder property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder? ParentFolder { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder ParentFolder { get; set; }
#endif
        /// <summary>The Path property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Path { get; set; }
#nullable restore
#else
        public string Path { get; set; }
#endif
        /// <summary>The PropagateAttributeGroupsToChildren property</summary>
        public bool? PropagateAttributeGroupsToChildren { get; set; }
        /// <summary>The Security property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Securities? Security { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Securities Security { get; set; }
#endif
        /// <summary>The ShareLinks property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionShareLink? ShareLinks { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionShareLink ShareLinks { get; set; }
#endif
        /// <summary>The UpdatedBy property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? UpdatedBy { get; set; }
#nullable restore
#else
        public string UpdatedBy { get; set; }
#endif
        /// <summary>The UpdatedDate property</summary>
        public DateTimeOffset? UpdatedDate { get; set; }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder"/> and sets the default values.
        /// </summary>
        public Folder()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "AccessLevel", n => { AccessLevel = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel.CreateFromDiscriminatorValue); } },
                { "AttributeGroups", n => { AttributeGroups = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder_AttributeGroups>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder_AttributeGroups.CreateFromDiscriminatorValue); } },
                { "BrowseDocumentsUrl", n => { BrowseDocumentsUrl = n.GetStringValue(); } },
                { "CreateDocumentHref", n => { CreateDocumentHref = n.GetStringValue(); } },
                { "CreatedBy", n => { CreatedBy = n.GetStringValue(); } },
                { "CreatedDate", n => { CreatedDate = n.GetDateTimeOffsetValue(); } },
                { "Description", n => { Description = n.GetStringValue(); } },
                { "Documents", n => { Documents = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument.CreateFromDiscriminatorValue); } },
                { "EosInfo", n => { EosInfo = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo.CreateFromDiscriminatorValue); } },
                { "EosParentInfo", n => { EosParentInfo = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo.CreateFromDiscriminatorValue); } },
                { "Folders", n => { Folders = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionFolder>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionFolder.CreateFromDiscriminatorValue); } },
                { "Href", n => { Href = n.GetStringValue(); } },
                { "Name", n => { Name = n.GetStringValue(); } },
                { "ParentFolder", n => { ParentFolder = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder.CreateFromDiscriminatorValue); } },
                { "Path", n => { Path = n.GetStringValue(); } },
                { "PropagateAttributeGroupsToChildren", n => { PropagateAttributeGroupsToChildren = n.GetBoolValue(); } },
                { "Security", n => { Security = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Securities>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Securities.CreateFromDiscriminatorValue); } },
                { "ShareLinks", n => { ShareLinks = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionShareLink>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionShareLink.CreateFromDiscriminatorValue); } },
                { "UpdatedBy", n => { UpdatedBy = n.GetStringValue(); } },
                { "UpdatedDate", n => { UpdatedDate = n.GetDateTimeOffsetValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel>("AccessLevel", AccessLevel);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder_AttributeGroups>("AttributeGroups", AttributeGroups);
            writer.WriteStringValue("BrowseDocumentsUrl", BrowseDocumentsUrl);
            writer.WriteStringValue("CreatedBy", CreatedBy);
            writer.WriteDateTimeOffsetValue("CreatedDate", CreatedDate);
            writer.WriteStringValue("Description", Description);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument>("Documents", Documents);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo>("EosInfo", EosInfo);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.EosInfo>("EosParentInfo", EosParentInfo);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionFolder>("Folders", Folders);
            writer.WriteStringValue("Href", Href);
            writer.WriteStringValue("Name", Name);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder>("ParentFolder", ParentFolder);
            writer.WriteStringValue("Path", Path);
            writer.WriteBoolValue("PropagateAttributeGroupsToChildren", PropagateAttributeGroupsToChildren);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Securities>("Security", Security);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionShareLink>("ShareLinks", ShareLinks);
            writer.WriteStringValue("UpdatedBy", UpdatedBy);
            writer.WriteDateTimeOffsetValue("UpdatedDate", UpdatedDate);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
