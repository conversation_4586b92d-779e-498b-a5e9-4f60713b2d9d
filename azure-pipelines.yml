trigger:
  branches:
    include:
      - main
      - release/*
pr:
  branches:
    include:
      - main
      - release/*

resources:
  repositories:
    - repository: templates
      type: git
      name: Microservices/msf-ado-templates
      ref: develop

parameters:
  - name: deployExperiment
    displayName: Deploy Experiment
    type: boolean
    default: "false"
    values:
      - "true"
      - "false"

  - name: experimentRouteName
    displayName: Experiment Route Name - defaults to branch/pr# if unspecified
    type: string
    default: " "

  - name: productionTargetRings
    type: object
    default:
      - us
      - eu
      - ca
      - au
      - jp1

variables:
  # Use in Directory.Build.props. Manually adjust the version number as needed. Patch is auto-incremented.
  VersionMajor: "1"
  VersionMinor: "0"
  CheckoutDepth: 0

extends:
  template: templates/msf-pipeline.yml@templates
  parameters:
    buildStages:
      - template: templates/stages/dotnet-build.yml@templates
        parameters:
          containerImage: ado-dotnet8.0-build:latest
          buildTarget: net8.0
          runFossa: true
      - template: templates/stages/dotnet-test.yml@templates
        parameters:
          containerImage: ado-dotnet8.0-build:latest
          codeql: false # Taking this out for now since it takes ~30 minutes to run. We can add back in later as part of separate pipeline.
          collectCoverage: true
          testSteps: # You need to add coverage numbers appropriate to the level of coverage you have for now. Please see this wiki: https://jira.corp.docusign.com/confu/pages/viewpage.action?spaceKey=MHCP&title=Run+your+MicroService+in+ADO+using+testStep+object
            - testType: UnitTest
              coverageLineThreshold: 75
              coverageBranchThreshold: 54
              coverageMethodThreshold: 80
              filesToExcludeForCoverage: "[*]TaskManagementService.Program*,[*]TaskManagementService.Startup*,[*]TaskManagementService.Extensions*,[*]TaskManagementService.ServiceIntegrations*,[*]TaskManagementService.Exceptions*,[*]TaskManagementService.Config*,[*]TaskManagementService.Optimizely*,[*]TaskManagementService.Infrastructure.ServiceIntegrations.UnifiedRepositoryApi.Models*"
    dockerImages:
      - name: task-management-service
        path: src/TaskManagementService
      - name: task-management-service-integration-tests
        path: src/TaskManagementService.Integration.Tests
      - name: "task-management-service-periodic-tests"
        path: "src/TaskManagementService.Periodic.Tests"
      - name: "task-management-service-performance-tests"
        path: "src/TaskManagementService.Perf.Tests"
    # https://jira.corp.docusign.com/confu/pages/viewpage.action?spaceKey=MHCP&title=Services+DeployYaml+Options
    servicesParameters:
      deployExperiment: ${{ parameters.deployExperiment }}
      experimentRouteName: ${{ parameters.experimentRouteName }}
      deployPRs: true
      helmTimeoutSecs: 1800
      autofillSecrets: true
      autofillSecretsEnvVars: true
    deployYaml:
      development:
        system-compute:
          geos:
            - us
        system-storage:
          localities:
            - us
      integration:
        service-compute:
          services:
            task-management-service:
              # WLI Enabled service account to use for CSI Driver authentication
              workloadServiceAccountName: "task-management-service"
              chartYamls:
                - config/integration/app-values-eastus-a.yaml
                - config/integration/app-values-westus3-a.yaml
              prDeploys:
                int-test:
                  - config/integration/app-values-experiment-integration-tests.yaml
                prf-test:
                  - config/shared/app-values-performance-tests.yaml
      stage:
        service-compute:
          services:
            task-management-service:
              # WLI Enabled service account to use for CSI Driver authentication
              workloadServiceAccountName: "task-management-service"
              chartYamls:
                - config/stage/app-values-eastus-a.yaml
                - config/stage/app-values-westus3-a.yaml
      demo:
        service-compute:
          services:
            task-management-service:
              # WLI Enabled service account to use for CSI Driver authentication
              workloadServiceAccountName: "task-management-service"
      production:
        service-compute:
          helmTimeoutSecs: 1800
          staggerDeployByRegion: true
          # staggerDeployByRegionRequireAck: true
          geos: ${{ parameters.productionTargetRings }}
          services:
            task-management-service:
              # WLI Enabled service account to use for CSI Driver authentication
              workloadServiceAccountName: "task-management-service"
