// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class Member : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The Email property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Email { get; set; }
#nullable restore
#else
        public string Email { get; set; }
#endif
        /// <summary>The FirstName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? FirstName { get; set; }
#nullable restore
#else
        public string FirstName { get; set; }
#endif
        /// <summary>The Groups property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup? Groups { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup Groups { get; set; }
#endif
        /// <summary>The Href property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Href { get; set; }
#nullable restore
#else
        public string Href { get; set; }
#endif
        /// <summary>The LastName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? LastName { get; set; }
#nullable restore
#else
        public string LastName { get; set; }
#endif
        /// <summary>The ManagedBy property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member? ManagedBy { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member ManagedBy { get; set; }
#endif
        /// <summary>The MiddleName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? MiddleName { get; set; }
#nullable restore
#else
        public string MiddleName { get; set; }
#endif
        /// <summary>The PermissionSets property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionPermissionSet? PermissionSets { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionPermissionSet PermissionSets { get; set; }
#endif
        /// <summary>The Persona property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Group? Persona { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Group Persona { get; set; }
#endif
        /// <summary>The RecentDocuments property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument? RecentDocuments { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument RecentDocuments { get; set; }
#endif
        /// <summary>The Role property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Role { get; set; }
#nullable restore
#else
        public string Role { get; set; }
#endif
        /// <summary>The Suffix property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Suffix { get; set; }
#nullable restore
#else
        public string Suffix { get; set; }
#endif
        /// <summary>The UserName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? UserName { get; set; }
#nullable restore
#else
        public string UserName { get; set; }
#endif
        /// <summary>The WatchedDocuments property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument? WatchedDocuments { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument WatchedDocuments { get; set; }
#endif
        /// <summary>The WatchedDocumentsProcessTrackingActivities property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentProcessTrackingActivity? WatchedDocumentsProcessTrackingActivities { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentProcessTrackingActivity WatchedDocumentsProcessTrackingActivities { get; set; }
#endif
        /// <summary>The WorkflowQueues property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkflowQueue? WorkflowQueues { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkflowQueue WorkflowQueues { get; set; }
#endif
        /// <summary>The WorkItems property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkItem? WorkItems { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkItem WorkItems { get; set; }
#endif
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member"/> and sets the default values.
        /// </summary>
        public Member()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "Email", n => { Email = n.GetStringValue(); } },
                { "FirstName", n => { FirstName = n.GetStringValue(); } },
                { "Groups", n => { Groups = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup.CreateFromDiscriminatorValue); } },
                { "Href", n => { Href = n.GetStringValue(); } },
                { "LastName", n => { LastName = n.GetStringValue(); } },
                { "ManagedBy", n => { ManagedBy = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member.CreateFromDiscriminatorValue); } },
                { "MiddleName", n => { MiddleName = n.GetStringValue(); } },
                { "PermissionSets", n => { PermissionSets = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionPermissionSet>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionPermissionSet.CreateFromDiscriminatorValue); } },
                { "Persona", n => { Persona = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Group>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Group.CreateFromDiscriminatorValue); } },
                { "RecentDocuments", n => { RecentDocuments = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument.CreateFromDiscriminatorValue); } },
                { "Role", n => { Role = n.GetStringValue(); } },
                { "Suffix", n => { Suffix = n.GetStringValue(); } },
                { "UserName", n => { UserName = n.GetStringValue(); } },
                { "WatchedDocuments", n => { WatchedDocuments = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument.CreateFromDiscriminatorValue); } },
                { "WatchedDocumentsProcessTrackingActivities", n => { WatchedDocumentsProcessTrackingActivities = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentProcessTrackingActivity>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentProcessTrackingActivity.CreateFromDiscriminatorValue); } },
                { "WorkItems", n => { WorkItems = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkItem>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkItem.CreateFromDiscriminatorValue); } },
                { "WorkflowQueues", n => { WorkflowQueues = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkflowQueue>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkflowQueue.CreateFromDiscriminatorValue); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("Email", Email);
            writer.WriteStringValue("FirstName", FirstName);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup>("Groups", Groups);
            writer.WriteStringValue("Href", Href);
            writer.WriteStringValue("LastName", LastName);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member>("ManagedBy", ManagedBy);
            writer.WriteStringValue("MiddleName", MiddleName);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionPermissionSet>("PermissionSets", PermissionSets);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Group>("Persona", Persona);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument>("RecentDocuments", RecentDocuments);
            writer.WriteStringValue("Role", Role);
            writer.WriteStringValue("Suffix", Suffix);
            writer.WriteStringValue("UserName", UserName);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocument>("WatchedDocuments", WatchedDocuments);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionDocumentProcessTrackingActivity>("WatchedDocumentsProcessTrackingActivities", WatchedDocumentsProcessTrackingActivities);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkflowQueue>("WorkflowQueues", WorkflowQueues);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionWorkItem>("WorkItems", WorkItems);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
