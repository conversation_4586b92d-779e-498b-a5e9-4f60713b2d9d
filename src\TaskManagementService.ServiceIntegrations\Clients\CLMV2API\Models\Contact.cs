// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class Contact : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The Address1 property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Address1 { get; set; }
#nullable restore
#else
        public string Address1 { get; set; }
#endif
        /// <summary>The Address2 property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Address2 { get; set; }
#nullable restore
#else
        public string Address2 { get; set; }
#endif
        /// <summary>The Address3 property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Address3 { get; set; }
#nullable restore
#else
        public string Address3 { get; set; }
#endif
        /// <summary>The City property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? City { get; set; }
#nullable restore
#else
        public string City { get; set; }
#endif
        /// <summary>The Company property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Company { get; set; }
#nullable restore
#else
        public string Company { get; set; }
#endif
        /// <summary>The Country property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Country { get; set; }
#nullable restore
#else
        public string Country { get; set; }
#endif
        /// <summary>The Department property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Department { get; set; }
#nullable restore
#else
        public string Department { get; set; }
#endif
        /// <summary>The Email property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Email { get; set; }
#nullable restore
#else
        public string Email { get; set; }
#endif
        /// <summary>The FaxNumber property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? FaxNumber { get; set; }
#nullable restore
#else
        public string FaxNumber { get; set; }
#endif
        /// <summary>The FirstName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? FirstName { get; set; }
#nullable restore
#else
        public string FirstName { get; set; }
#endif
        /// <summary>The Groups property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup? Groups { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup Groups { get; set; }
#endif
        /// <summary>The Href property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Href { get; set; }
#nullable restore
#else
        public string Href { get; set; }
#endif
        /// <summary>The LastName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? LastName { get; set; }
#nullable restore
#else
        public string LastName { get; set; }
#endif
        /// <summary>The PhoneNumber property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? PhoneNumber { get; set; }
#nullable restore
#else
        public string PhoneNumber { get; set; }
#endif
        /// <summary>The Shared property</summary>
        public bool? Shared { get; set; }
        /// <summary>The State property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? State { get; set; }
#nullable restore
#else
        public string State { get; set; }
#endif
        /// <summary>The Title property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Title { get; set; }
#nullable restore
#else
        public string Title { get; set; }
#endif
        /// <summary>The ZipCode property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? ZipCode { get; set; }
#nullable restore
#else
        public string ZipCode { get; set; }
#endif
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Contact"/> and sets the default values.
        /// </summary>
        public Contact()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Contact"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Contact CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Contact();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "Address1", n => { Address1 = n.GetStringValue(); } },
                { "Address2", n => { Address2 = n.GetStringValue(); } },
                { "Address3", n => { Address3 = n.GetStringValue(); } },
                { "City", n => { City = n.GetStringValue(); } },
                { "Company", n => { Company = n.GetStringValue(); } },
                { "Country", n => { Country = n.GetStringValue(); } },
                { "Department", n => { Department = n.GetStringValue(); } },
                { "Email", n => { Email = n.GetStringValue(); } },
                { "FaxNumber", n => { FaxNumber = n.GetStringValue(); } },
                { "FirstName", n => { FirstName = n.GetStringValue(); } },
                { "Groups", n => { Groups = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup.CreateFromDiscriminatorValue); } },
                { "Href", n => { Href = n.GetStringValue(); } },
                { "LastName", n => { LastName = n.GetStringValue(); } },
                { "PhoneNumber", n => { PhoneNumber = n.GetStringValue(); } },
                { "Shared", n => { Shared = n.GetBoolValue(); } },
                { "State", n => { State = n.GetStringValue(); } },
                { "Title", n => { Title = n.GetStringValue(); } },
                { "ZipCode", n => { ZipCode = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("Address1", Address1);
            writer.WriteStringValue("Address2", Address2);
            writer.WriteStringValue("Address3", Address3);
            writer.WriteStringValue("City", City);
            writer.WriteStringValue("Company", Company);
            writer.WriteStringValue("Country", Country);
            writer.WriteStringValue("Department", Department);
            writer.WriteStringValue("Email", Email);
            writer.WriteStringValue("FaxNumber", FaxNumber);
            writer.WriteStringValue("FirstName", FirstName);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup>("Groups", Groups);
            writer.WriteStringValue("Href", Href);
            writer.WriteStringValue("LastName", LastName);
            writer.WriteStringValue("PhoneNumber", PhoneNumber);
            writer.WriteBoolValue("Shared", Shared);
            writer.WriteStringValue("State", State);
            writer.WriteStringValue("Title", Title);
            writer.WriteStringValue("ZipCode", ZipCode);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
