using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;

using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.Hosting;

using TaskManagementService.Controllers;

namespace TaskManagementService.Extensions;

/// <summary>
/// Custom controller feature provider that includes specific controllers only in Development and Integration environments.
/// RedisCacheController and RateLimitController are excluded from all other environments (Demo, Stage, Production, etc.).
/// </summary>
[ExcludeFromCodeCoverage]
public class EnvironmentControllerFeatureProvider : ControllerFeatureProvider
{
    private readonly IHostEnvironment _environment;
    private readonly HashSet<Type> _excludedControllers;

    public EnvironmentControllerFeatureProvider(IHostEnvironment environment)
    {
        _environment = environment ?? throw new ArgumentNullException(nameof(environment));
        _excludedControllers = GetExcludedControllersForEnvironment();
    }

    protected override bool IsController(TypeInfo typeInfo)
    {
        ArgumentNullException.ThrowIfNull(typeInfo, nameof(typeInfo));

        var isController = base.IsController(typeInfo);

        if (!isController)
        {
            return false;
        }

        // Exclude specific controllers based on environment
        return !_excludedControllers.Contains(typeInfo.AsType());
    }

    private HashSet<Type> GetExcludedControllersForEnvironment()
    {
        var excludedControllers = new HashSet<Type>();

        // Only include RedisCacheController and RateLimitController in Development, Integration and Stage environments
        // Exclude them from all other environments (Demo, Production, etc.)
        if (!IsLowerEnvironments())
        {
            excludedControllers.Add(typeof(RedisCacheController));
            excludedControllers.Add(typeof(RateLimitController));
        }

        return excludedControllers;
    }

    private bool IsLowerEnvironments()
    {
        var environmentName = _environment.EnvironmentName;
        return string.Equals(environmentName, "Development", StringComparison.OrdinalIgnoreCase) ||
               string.Equals(environmentName, "Integration", StringComparison.OrdinalIgnoreCase) ||
               string.Equals(environmentName, "Stage", StringComparison.OrdinalIgnoreCase);
    }
}
