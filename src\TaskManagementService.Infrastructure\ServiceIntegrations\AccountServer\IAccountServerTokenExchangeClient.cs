namespace TaskManagementService.Infrastructure.ServiceIntegrations.AccountServer;

public interface IAccountServerTokenExchangeClient
{
    /// <summary>
    /// This method gets you the Application token.
    /// </summary>
    /// <param name="cancellationToken">This is a used to signal that an operation should be canceled gracefully</param>
    /// <returns>Token in string format</returns>
    public Task<string?> GetAppTokenAsync(CancellationToken cancellationToken);
}
