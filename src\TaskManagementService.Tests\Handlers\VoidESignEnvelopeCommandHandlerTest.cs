using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using MediatR;

using Microsoft.Extensions.Logging;

using Moq;

using NSubstitute;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Handlers;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models;

using Xunit;

using TaskGroupMember = TaskManagementService.Core.Models.TaskGroupMember;

namespace TaskManagementService.Tests.Handlers;

[Trait("TestType", "UnitTest")]
public class VoidESignEnvelopeCommandHandlerTest
{
    [Fact]
    public async Task Handle()
    {
        var mockLogger = new Mock<ILogger<VoidESignEnvelopeCommandHandler>>();
        var mockEsignTaskService = new Mock<IESignTaskService>();

        var mockAccountId = new Guid("*************-0001-000b-************");
        var mockTaskId = "*************-0001-000b-************";
        var mockVoidedReason = "void reason";

        var mockVoidESignEnvelopeCommand = new VoidESignEnvelopeCommand(mockAccountId, mockTaskId, mockVoidedReason);

        var voidESignEnvelopeCommandHandler =
            new VoidESignEnvelopeCommandHandler(mockLogger.Object, mockEsignTaskService.Object);

        await voidESignEnvelopeCommandHandler.Handle(mockVoidESignEnvelopeCommand, CancellationToken.None);

        mockEsignTaskService.Verify(x => x.VoidEnvelopeAsync(mockAccountId, mockTaskId, mockVoidedReason), Times.Once);
    }

    [Fact]
    public async Task NullHandle()
    {
        var mockLogger = new Mock<ILogger<VoidESignEnvelopeCommandHandler>>();
        var mockEsignTaskService = new Mock<IESignTaskService>();

        var voidESignEnvelopeCommandHandler =
            new VoidESignEnvelopeCommandHandler(mockLogger.Object, mockEsignTaskService.Object);

        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            voidESignEnvelopeCommandHandler.Handle(null, CancellationToken.None));
    }
}
