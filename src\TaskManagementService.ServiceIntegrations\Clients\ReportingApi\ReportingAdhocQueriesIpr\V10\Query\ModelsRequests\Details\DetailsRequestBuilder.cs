// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using System;
using TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details
{
    /// <summary>
    /// Builds and executes requests for operations under \reporting-adhoc-queries-ipr\v1.0\query\models\details
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class DetailsRequestBuilder : BaseRequestBuilder
    {
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details.DetailsRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public DetailsRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/reporting-adhoc-queries-ipr/v1.0/query/models/details{?accountId*,modelId*}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details.DetailsRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public DetailsRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/reporting-adhoc-queries-ipr/v1.0/query/models/details{?accountId*,modelId*}", rawUrl)
        {
        }
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.AdHocModelDetails"/></returns>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
        /// <exception cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.ProblemDetails">When receiving a 400 status code</exception>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.AdHocModelDetails?> GetAsync(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details.DetailsRequestBuilder.DetailsRequestBuilderGetQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.AdHocModelDetails> GetAsync(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details.DetailsRequestBuilder.DetailsRequestBuilderGetQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            var requestInfo = ToGetRequestInformation(requestConfiguration);
            var errorMapping = new Dictionary<string, ParsableFactory<IParsable>>
            {
                { "400", global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.ProblemDetails.CreateFromDiscriminatorValue },
            };
            return await RequestAdapter.SendAsync<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.AdHocModelDetails>(requestInfo, global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.AdHocModelDetails.CreateFromDiscriminatorValue, errorMapping, cancellationToken).ConfigureAwait(false);
        }
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details.DetailsRequestBuilder.DetailsRequestBuilderGetQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details.DetailsRequestBuilder.DetailsRequestBuilderGetQueryParameters>> requestConfiguration = default)
        {
#endif
            var requestInfo = new RequestInformation(Method.GET, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json");
            return requestInfo;
        }
        /// <summary>
        /// Returns a request builder with the provided arbitrary URL. Using this method means any other path or query parameters are ignored.
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details.DetailsRequestBuilder"/></returns>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details.DetailsRequestBuilder WithUrl(string rawUrl)
        {
            return new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details.DetailsRequestBuilder(rawUrl, RequestAdapter);
        }
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        #pragma warning disable CS1591
        public partial class DetailsRequestBuilderGetQueryParameters 
        #pragma warning restore CS1591
        {
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            [QueryParameter("accountId")]
            public string? AccountId { get; set; }
#nullable restore
#else
            [QueryParameter("accountId")]
            public string AccountId { get; set; }
#endif
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            [QueryParameter("modelId")]
            public string? ModelId { get; set; }
#nullable restore
#else
            [QueryParameter("modelId")]
            public string ModelId { get; set; }
#endif
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class DetailsRequestBuilderGetRequestConfiguration : RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details.DetailsRequestBuilder.DetailsRequestBuilderGetQueryParameters>
        {
        }
    }
}
#pragma warning restore CS0618
