﻿using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Text.Json;

using DocuSign.eSign.Api;
using DocuSign.eSign.Client;
using DocuSign.eSign.Model;

using Flurl;

using Polly.Contrib.WaitAndRetry;
using Polly.Retry;
using Polly;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Exceptions;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Infrastructure.Interfaces;
using TaskManagementService.Infrastructure.ServiceIntegrations.EnvelopeApi;
using TaskManagementService.Infrastructure.Services.Models;

using EnvelopeAuditEventResponse = TaskManagementService.Infrastructure.Services.Models.EnvelopeAuditEventResponse;

namespace TaskManagementService.Infrastructure.Services;

public sealed class ESignTaskService(
    IEnvelopeApiConfig config,
    IEsignApiClient eSignApiClientFactory,
    HttpClient httpClient,
    IRequestContextService requestContextService,
    ILogger<EnvelopeApiService> logger) : ITaskService, IESignTaskService
{
    private IEnvelopesApi? _envelopesApi;
    private ISigningGroupsApi? _signingGroupsApi;

    public const string AuthorizationHeaderName = "Authorization";
    private static readonly JsonSerializerOptions SerializerOptions = new() { PropertyNameCaseInsensitive = true };

    private static readonly IEnumerable<TimeSpan> RetryTimeSpans = Backoff.LinearBackoff(TimeSpan.FromMilliseconds(100), 3, fastFirst: true);
    private readonly AsyncRetryPolicy _asyncRetryPolicy = Policy
        .Handle<ApiException>()
        .WaitAndRetryAsync(
            RetryTimeSpans,
            (e, currentSpan, currentAttempt, currentContext) =>
                logger.LogWarning("Retrying {OperationKey}. Current attempt: {CurrentAttempt}. Correlation Id: {CorrelationId}.", currentContext.OperationKey, currentAttempt, currentContext.CorrelationId));

    private async Task<IEnvelopesApi> CreateDocuSignEnvelopesClientAsync()
    {
        if (_envelopesApi != null)
        {
            return _envelopesApi;
        }

        var eSignBaseUrl = requestContextService.ESignApiBaseUrl.ToString();
        var apiClient = new DocuSignClient(eSignBaseUrl);
        _envelopesApi = await eSignApiClientFactory.GetEsignEnvelopeApiClientAsync(apiClient);
        return _envelopesApi;
    }

    private async Task<ISigningGroupsApi> CreateDocuSignSigningGroupsClientAsync()
    {
        if (_signingGroupsApi != null)
        {
            return _signingGroupsApi;
        }

        var eSignBaseUrl = requestContextService.ESignApiBaseUrl.ToString();
        var apiClient = new DocuSignClient(eSignBaseUrl);
        _signingGroupsApi = await eSignApiClientFactory.GetEsignSigningGroupsApiClientAsync(apiClient);
        return _signingGroupsApi;
    }

    public async Task<List<UserTask>> GetTasksAsync(Guid accountId, TaskFilter taskFilter)
    {
        ArgumentNullException.ThrowIfNull(taskFilter, nameof(taskFilter));

        // eSign tasks are always assigned
        if (taskFilter.OnlyUnassigned)
        {
            return [];
        }

        var docuSignClient = await CreateDocuSignEnvelopesClientAsync();

        var envelopes = (await _asyncRetryPolicy.ExecuteAsync(() => docuSignClient.ListStatusChangesAsync(accountId.ToString(), ConvertToListStatusChangesOptions(taskFilter, TaskSource.ESignature))))?.Envelopes ?? [];
        var tasks = envelopes.Select(envelope => CreateUserTaskFromEnvelope(envelope, requestContextService.UserId.ToString()));
        return tasks.Where(task => task != null && taskFilter.ExtraESignTaskFilter(task)).ToList()!;
    }

    public async Task<EnvelopesInformation> GetIdvTasksAsync(IEnvelopesApi docuSignClient, Guid accountId, TaskFilter? taskFilter, TaskSort? taskSort)
    {
        ArgumentNullException.ThrowIfNull(docuSignClient, nameof(docuSignClient));

        taskFilter ??= new TaskFilter();

        var idvTaskResults = await _asyncRetryPolicy.ExecuteAsync(() => docuSignClient.ListStatusChangesAsync(
            accountId.ToString(),
            ConvertToListStatusChangesOptions(taskFilter, TaskSource.IdVerification)));
        return idvTaskResults;
    }

    public async Task<long> GetIdvTasksCountAsync(Guid accountId)
    {
        var docuSignClient = await CreateDocuSignEnvelopesClientAsync();

        var idvTasks = await GetIdvTasksAsync(docuSignClient, accountId, null, null);

        var completedTasks = idvTasks.Envelopes?
            .Where(env => env != null)
            .Select(envelope => CreateUserTaskFromEnvelope(envelope, requestContextService.UserId.ToString()))
            .Where(task => task != null) ?? [];

        return completedTasks.Count();
    }

    public async Task<TaskHistory> GetEnvelopeHistoryAsync(Guid accountId, Guid envelopeId, string? locale = null, CancellationToken cancellationToken = default)
    {
        var eSignBaseUrl = requestContextService.ESignApiBaseUrl.ToString();
        var requestUrl = eSignBaseUrl
            .AppendPathSegment($"v2.1/accounts/{accountId}/envelopes/{envelopeId}/audit_events");

        if (locale != null)
        {
            requestUrl = requestUrl.AppendQueryParam("locale", locale);
        }

        using var httpRequest = new HttpRequestMessage(HttpMethod.Get, requestUrl.ToUri());
        _ = httpRequest.Headers.TryAddWithoutValidation(AuthorizationHeaderName, requestContextService.Authorization);

        var response = await httpClient.SendAsync(httpRequest, cancellationToken);
        var content = await response.Content.ReadAsStringAsync(cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            var error = JsonSerializer.Deserialize<ESignatureErrorResponse>(content, SerializerOptions);

            logger.LogError("Failed to get task history from eSign API. Status code: {StatusCode}, Error Code: {ErrorCode}  Error Message: {Message}", response.StatusCode, error?.ErrorCode, error?.Message);

            return error?.ErrorCode switch
            {
                "INVALID_REQUEST_PARAMETER" => throw new ValidationException(error?.Message),
                _ => throw new TaskManagementServiceException(ApplicationError.CreateUnspecifiedError(error?.Message)),
            };
        }

        var auditEventResponse = JsonSerializer.Deserialize<EnvelopeAuditEventResponse>(content, SerializerOptions);
        if (auditEventResponse?.AuditEvents == null || auditEventResponse.AuditEvents.Count == 0)
        {
            logger.LogError("Failed to get task history from eSign API. Null response returned");
            return new TaskHistory();
        }

        var taskHistory = new TaskHistory
        {
            TaskId = envelopeId.ToString(),
            Source = TaskSource.ESignature,
            AuditEvents = auditEventResponse.AuditEvents.Select(envelopeAuditEvent => new TaskHistoryAuditEvent
            {
                CreatedDate = envelopeAuditEvent.EventFields.FirstOrDefault(e => e.Name == "logTime")?.Value ?? string.Empty,
                Message = envelopeAuditEvent.EventFields.FirstOrDefault(e => e.Name == "Message")?.Value ?? string.Empty
            })
        };

        return taskHistory;
    }

    public async Task VoidEnvelopeAsync(Guid accountId, string envelopeId, string voidedReason)
    {
        var docuSignClient = await CreateDocuSignEnvelopesClientAsync();
        var envelope = await docuSignClient.GetEnvelopeAsync(accountId.ToString(), envelopeId);
        EnsureUserIsSenderOfTheEnvelope(envelope);

        await docuSignClient.UpdateAsync(accountId.ToString(), envelopeId, new Envelope { Status = "voided", VoidedReason = voidedReason });
    }

    public async Task ResendEnvelopeAsync(Guid accountId, string envelopeId)
    {
        var docuSignClient = await CreateDocuSignEnvelopesClientAsync();
        var envelope = await docuSignClient.GetEnvelopeAsync(accountId.ToString(), envelopeId);
        EnsureUserIsSenderOfTheEnvelope(envelope);

        await docuSignClient.UpdateAsync(accountId.ToString(), envelopeId, new Envelope { Status = "sent" }, new EnvelopesApi.UpdateOptions { resendEnvelope = "true" });
    }

    public async Task<List<TaskGroup>> GetTasksGroupsAsync(Guid accountId, bool includeMembers)
    {
        var docuSignClient = await CreateDocuSignSigningGroupsClientAsync();

        List<TaskGroup> taskGroups;
        try
        {
            var result = await docuSignClient.ListAsync(accountId.ToString(), new SigningGroupsApi.ListOptions { includeUsers = includeMembers ? "true" : "false" });

            if (result?.Groups == null || result.Groups.Count == 0)
            {
                return [];
            }

            taskGroups = result.Groups.Select(group => new TaskGroup
            {
                Id = group.SigningGroupId,
                Name = group.GroupName,
                Source = TaskSource.ESignature,
                Members = includeMembers ? group.Users.Select(u => new TaskGroupMember { Email = u.Email, FullName = u.UserName }) : Array.Empty<TaskGroupMember>()
            }).ToList();
        }
        catch (ApiException e)
        {
            if (e.ErrorCode == 400)
            {
                return [];
            }

            throw;
        }

        return taskGroups;
    }

    private EnvelopesApi.ListStatusChangesOptions ConvertToListStatusChangesOptions(TaskFilter taskFilter, TaskSource taskSource)
    {
        var options = new EnvelopesApi.ListStatusChangesOptions
        {
            include = "recipients",
        };

        switch (taskSource)
        {
            case TaskSource.ESignature:
                options.folderIds = "awaiting_my_signature";
                break;
            case TaskSource.IdVerification:
                options.status = "AuthFailed";
                break;
            case TaskSource.AssignmentsApi:
            case TaskSource.ClmWorkflow:
            default:
                throw new NotSupportedException($"Unsupported task source: {taskSource}");
        }

        if (taskFilter.AssignedDateTo.HasValue)
        {
            options.toDate = taskFilter.AssignedDateTo.Value.ToString("o", CultureInfo.InvariantCulture);
        }

        if (taskFilter.AssignedDateFrom.HasValue)
        {
            options.fromDate = taskFilter.AssignedDateFrom.Value.ToString("o", CultureInfo.InvariantCulture);
        }
        else
        {
            options.fromDate = DateTime.UtcNow.AddMonths(config.DefaultStartDateOffsetInMonths).ToString("o", CultureInfo.InvariantCulture);
        }

        if (!string.IsNullOrEmpty(taskFilter.SearchText))
        {
            options.searchText = taskFilter.SearchText;
        }

        if (taskFilter != null && taskFilter.TaskSort != null)
        {
            switch (taskFilter.TaskSort.SortColumn)
            {
                case SortColumn.AssignedDate:
                    options.orderBy = "sent";
                    break;
                case SortColumn.DueDate:
                    options.orderBy = "action_required";
                    break;
                default:
                    break;
            }

            if (taskFilter.TaskSort.ResultCount != null)
            {
                options.count = taskFilter.TaskSort.ResultCount.Value.ToString(CultureInfo.CurrentCulture);
            }
        }

        return options;
    }

    private static UserTask? CreateUserTaskFromEnvelope(Envelope envelope, string userId)
    {
        ArgumentNullException.ThrowIfNull(envelope, nameof(envelope));

        var assignee = CreateAssignees(envelope, userId)
            .OrderBy(x => x.Order)
            .FirstOrDefault();

        if (assignee == null)
        {
            return null;
        }

        var type = TaskType.NeedsToSign;
        if (envelope.Status == "authfailed")
        {
            type = TaskType.VerificationFailed;
        }
        else
        {
            switch (assignee.RecipientType)
            {
                case CollaboratorType.Assignee:
                    type = TaskType.NeedsToSign;
                    break;
                case CollaboratorType.Viewer:
                    type = TaskType.NeedsToView;
                    break;
                case CollaboratorType.InPersonSigner:
                    type = TaskType.InPersonSigner;
                    break;
                case CollaboratorType.SpecifyRecipients:
                    type = TaskType.SpecifyRecipients;
                    break;
                case CollaboratorType.UpdateRecipients:
                    type = TaskType.UpdateRecipients;
                    break;
                case CollaboratorType.Editor:
                    type = TaskType.AllowToEdit;
                    break;
                case CollaboratorType.ReceivesACopy:
                    type = TaskType.ReceivesACopy;
                    break;
                case CollaboratorType.Intermediary:
                case CollaboratorType.Assignor:
                case CollaboratorType.SignWithWitness:
                case CollaboratorType.SignWithNotary:
                default:
                    break;
            }
        }

        var group = assignee as TaskSigningGroup;

        return new UserTask
        {
            Id = Guid.Parse(envelope.EnvelopeId),
            Source = TaskSource.ESignature,
            Type = type,
            Assignor = CreateAssignor(envelope),
            CreatedDate = envelope.CreatedDateTime,
            DueDate = envelope.ExpireDateTime,
            AssignedDate = envelope.SentDateTime,
            AgreementIds = [Guid.Parse(envelope.EnvelopeId)],
            Assignees = [assignee],
            NavigationUrl = $"/send/documents/details/{envelope.EnvelopeId}",
            Name = envelope.EmailSubject,
            Title = envelope.EmailSubject,
            Description = envelope.EmailSubject,
            WorkflowName = string.Empty,
            TaskGroupAssignee = group == null
                ? null
                : new TaskGroupWithPermissions
                {
                    Id = group.Id,
                    Name = group.Name,
                    Source = TaskSource.ESignature,
                    Members = group.SigningGroupUsers.Select(u =>
                        Guid.TryParse(u.UserId, out var memberUserId)
                            ? new TaskGroupMember { Id = memberUserId, FullName = u.UserName, Email = u.Email }
                            : new TaskGroupMember { Id = null, FullName = u.UserName, Email = u.Email })
                }
        };
    }

    private static List<TaskCollaborator> CreateAssignees(Envelope envelope, string userId)
    {
        ArgumentNullException.ThrowIfNull(envelope, nameof(envelope));

        if (envelope.Recipients?.Signers == null)
        {
            return [];
        }

        var recipients = new List<TaskCollaborator>();

        // Process each recipient type
        ProcessRecipients(envelope.Recipients.Signers, CollaboratorType.Assignee, userId);
        ProcessRecipients(envelope.Recipients.InPersonSigners, CollaboratorType.InPersonSigner, userId, includeSigningGroup: false);
        ProcessRecipients(envelope.Recipients.CertifiedDeliveries, CollaboratorType.Viewer, userId);
        ProcessRecipients(envelope.Recipients.Editors, CollaboratorType.Editor, userId);
        ProcessRecipients(envelope.Recipients.CarbonCopies, CollaboratorType.ReceivesACopy, userId);
        ProcessRecipients(envelope.Recipients.Intermediaries, CollaboratorType.UpdateRecipients, userId);
        ProcessRecipients(envelope.Recipients.Agents, CollaboratorType.SpecifyRecipients, userId);

        return recipients;

        void ProcessRecipients<T>(IEnumerable<T?>? recipientsList, CollaboratorType type, string tokenUserId, bool includeSigningGroup = true)
            where T : class
        {
            if (recipientsList == null)
            {
                return;
            }

            foreach (var recipient in recipientsList)
            {
                if (recipient == null)
                {
                    continue;
                }

                dynamic r = recipient; // Using dynamic to handle different recipient types
                if (r.Status == "completed")
                {
                    continue;
                }

                var order = 1;
                if (!string.IsNullOrEmpty(r.RoutingOrder) && !int.TryParse(r.RoutingOrder, out order))
                {
                    order = 1;
                }

                if (includeSigningGroup && !string.IsNullOrEmpty(r.SigningGroupId))
                {
                    List<UserInfo> signingGroupUsers = Enumerable.ToList<UserInfo>(r.SigningGroupUsers);
                    if (signingGroupUsers.All(user => user.UserId != tokenUserId))
                    {
                        continue;
                    }

                    recipients.Add(new TaskSigningGroup(r.SigningGroupId, r.SigningGroupName, type, r.SigningGroupUsers == null ? new ReadOnlyCollection<UserInfo>(Array.Empty<UserInfo>()) : new ReadOnlyCollection<UserInfo>(r.SigningGroupUsers), order));
                }
                else if (Guid.TryParse(r.UserId, out Guid parsedUserId))
                {
                    if (parsedUserId.ToString() != tokenUserId)
                    {
                        continue;
                    }

                    var displayName = string.IsNullOrEmpty(r.Name) ? r.RoleName : r.Name;
                    recipients.Add(new TaskUser(parsedUserId, displayName, r.Email, type, order));
                }
            }
        }
    }

    private static TaskUser? CreateAssignor(Envelope envelope)
    {
        ArgumentNullException.ThrowIfNull(envelope, nameof(envelope));

        if (envelope.Sender == null || !Guid.TryParse(envelope.Sender.UserId, out var userId))
        {
            return null;
        }

        return new TaskUser(userId, envelope.Sender.UserName, envelope.Sender.Email, CollaboratorType.Assignor);
    }

    private void EnsureUserIsSenderOfTheEnvelope(Envelope envelope)
    {
        ArgumentNullException.ThrowIfNull(envelope, nameof(envelope));

        if (envelope.Sender?.UserId != requestContextService.UserId.ToString())
        {
            throw new ValidationException("This user is not the sender of the envelope. Only the sender of the envelope may perform the requested operation.");
        }
    }
}
