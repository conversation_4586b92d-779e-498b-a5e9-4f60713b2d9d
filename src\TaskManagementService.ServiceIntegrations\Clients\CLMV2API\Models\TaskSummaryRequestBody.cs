// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class TaskSummaryRequestBody : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The AssignedUserUids property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<Guid?>? AssignedUserUids { get; set; }
#nullable restore
#else
        public List<Guid?> AssignedUserUids { get; set; }
#endif
        /// <summary>The DueDateRange property</summary>
        public int? DueDateRange { get; set; }
        /// <summary>The OnlyUnassigned property</summary>
        public bool? OnlyUnassigned { get; set; }
        /// <summary>The PageSize property</summary>
        public int? PageSize { get; set; }
        /// <summary>The SortColumn property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? SortColumn { get; set; }
#nullable restore
#else
        public string SortColumn { get; set; }
#endif
        /// <summary>The StartIndex property</summary>
        public int? StartIndex { get; set; }
        /// <summary>The TaskGroupIds property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<Guid?>? TaskGroupIds { get; set; }
#nullable restore
#else
        public List<Guid?> TaskGroupIds { get; set; }
#endif
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskSummaryRequestBody"/> and sets the default values.
        /// </summary>
        public TaskSummaryRequestBody()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskSummaryRequestBody"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskSummaryRequestBody CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskSummaryRequestBody();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "AssignedUserUids", n => { AssignedUserUids = n.GetCollectionOfPrimitiveValues<Guid?>()?.AsList(); } },
                { "DueDateRange", n => { DueDateRange = n.GetIntValue(); } },
                { "OnlyUnassigned", n => { OnlyUnassigned = n.GetBoolValue(); } },
                { "PageSize", n => { PageSize = n.GetIntValue(); } },
                { "SortColumn", n => { SortColumn = n.GetStringValue(); } },
                { "StartIndex", n => { StartIndex = n.GetIntValue(); } },
                { "TaskGroupIds", n => { TaskGroupIds = n.GetCollectionOfPrimitiveValues<Guid?>()?.AsList(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteCollectionOfPrimitiveValues<Guid?>("AssignedUserUids", AssignedUserUids);
            writer.WriteIntValue("DueDateRange", DueDateRange);
            writer.WriteBoolValue("OnlyUnassigned", OnlyUnassigned);
            writer.WriteIntValue("PageSize", PageSize);
            writer.WriteStringValue("SortColumn", SortColumn);
            writer.WriteIntValue("StartIndex", StartIndex);
            writer.WriteCollectionOfPrimitiveValues<Guid?>("TaskGroupIds", TaskGroupIds);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
