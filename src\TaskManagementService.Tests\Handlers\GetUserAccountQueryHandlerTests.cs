﻿using System;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

using Flurl.Http.Testing;

using Microsoft.Extensions.Logging;
using Microsoft.Kiota.Abstractions.Authentication;

using Moq;
using Moq.Contrib.HttpClient;

using Snapshooter.Xunit;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Queries;
using TaskManagementService.Middleware;
using TaskManagementService.Services;

using Xunit;
#pragma warning disable SA1512
#pragma warning disable SA1005

#pragma warning disable CA2000

namespace TaskManagementService.Tests.Handlers;

[Trait("TestType", "UnitTest")]
public sealed class GetUserAccountQueryHandlerTests : IDisposable
{
    private readonly GetUserAccountQueryHandler _sut;

    private static readonly Guid AccountId = new("6977bd8d-e196-4802-9d62-bb68bddd9fb8");
    private static readonly Guid UserId = new("1811b96b-177c-4832-9eff-c3253d42de88");

    private readonly HttpTest _httpTest;
    private readonly Mock<IHttpClientFactory> _httpClientFactoryMock;
    private readonly Mock<IClmApiConfig> _clmApiConfigMock;

    public GetUserAccountQueryHandlerTests()
    {
        _httpTest = new HttpTest();

        var handlerMock = new Mock<HttpMessageHandler>(MockBehavior.Strict);
        handlerMock
            .SetupRequest(HttpMethod.Get, "https://api.example.com/oauth/userinfo")
            .ReturnsJsonResponse(new GetUserAccountQueryHandler.DocusignUser
            {
                Sub = UserId,
                Accounts =
                [
                    new GetUserAccountQueryHandler.DocusignAccount
                    {
                        AccountId = AccountId,
                        AccountName = "Test Account",
                        BaseUri = new Uri("https://api.foo.bar"),
                    },
                ],
            });

        handlerMock
            .SetupRequest(HttpMethod.Get, $"https://auth.clm.com/v2/{AccountId}/account")
            .ReturnsJsonResponse(new GetUserAccountQueryHandler.DocusignClmAccount
            {
                ApiBaseUrl = new Uri("https://api.clm.com"),
            });

        handlerMock
            .SetupRequest(HttpMethod.Get, $"https://api.clm.com/v2/{AccountId}/members/current")
            .Returns(async (HttpRequestMessage request, CancellationToken _) =>
            {
                await Task.Yield();
                return new HttpResponseMessage
                {
                    Content = new StringContent("{\"Href\":\"https://api.clm.com/1811b96b-177c-4832-9eff-c3253d42de88\",\"Role\":\"CLM User\",\"UserName\":\"Taco\"}", new MediaTypeHeaderValue("application/json")),
                    StatusCode = HttpStatusCode.OK,
                };
            });

        _httpClientFactoryMock = new Mock<IHttpClientFactory>();
        _httpClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(handlerMock.CreateClient());
        _clmApiConfigMock = new Mock<IClmApiConfig>();
        _clmApiConfigMock.Setup(x => x.AuthUrl).Returns("https://auth.clm.com");

        _sut = new GetUserAccountQueryHandler(
            new Mock<ILogger<GetUserAccountQueryHandler>>().Object,
            new NoOpHybridCache(),
            _httpClientFactoryMock.Object,
            _clmApiConfigMock.Object,
            new AnonymousAuthenticationProvider());
    }

    [Fact]
    public async Task GetUserAccountQueryReturnsUserAccount()
    {
        var response = await _sut.Handle(new GetUserAccountQuery(CreateRequestContextService()));

        _httpClientFactoryMock.Verify(x => x.CreateClient("UserAccount"), Times.Once);
        _clmApiConfigMock.Verify(x => x.AuthUrl, Times.Once);
        Snapshot.Match(response);
    }

    [Fact]
    public async Task GetUserAccountQueryReturnsUserAccountForNonClmAccounts()
    {
        var response = await _sut.Handle(new GetUserAccountQuery(CreateRequestContextService(isClmAccount: false)));

        _httpClientFactoryMock.Verify(x => x.CreateClient("UserAccount"), Times.Once);
        _clmApiConfigMock.Verify(x => x.AuthUrl, Times.Never);
        Snapshot.Match(response);
    }

    [Fact]
    public async Task GetClmUserAccountQueryReturnsNoUserAccountForNonClmAccounts()
    {
        var request = CreateRequestContextService(isClmAccount: false);
        request.ClmApiBaseUrl = null;
        var response = await _sut.Handle(new GetClmUserAccountQuery(request));
        _httpClientFactoryMock.Verify(x => x.CreateClient("UserAccount"), Times.Never);
        Assert.Null(response);
    }

    [Fact]
    public async Task GetClmUserAccountQueryReturnsUserAccountForNonClmAccounts()
    {
        var response = await _sut.Handle(new GetClmUserAccountQuery(CreateRequestContextService(isClmAccount: false)));
        _httpClientFactoryMock.Verify(x => x.CreateClient("UserAccount"), Times.Never);
        Assert.Null(response);
    }

    [Fact]
    public async Task GetClmUserAccountQueryReturnsUserAccountForClmAccounts()
    {
        var response = await _sut.Handle(new GetClmUserAccountQuery(CreateRequestContextService(isClmAccount: true)));
        _httpClientFactoryMock.Verify(x => x.CreateClient("UserAccount"), Times.Once);
        Assert.NotNull(response);
    }

    public void Dispose()
    {
        _httpTest?.Dispose();
    }

    private static RequestContextService CreateRequestContextService(bool isClmAccount = true)
    {
        return new RequestContextService
        {
            AccountId = AccountId,
            UserId = UserId,
            Issuer = new Uri("https://api.example.com"),
            IsClmAccount = isClmAccount,
            ClmApiBaseUrl = isClmAccount ? new Uri("https://api.clm.com") : null,
        };
    }
}
