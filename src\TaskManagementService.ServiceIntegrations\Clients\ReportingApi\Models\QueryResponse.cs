// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class QueryResponse : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The aggregate property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Aggregate { get; set; }
#nullable restore
#else
        public string Aggregate { get; set; }
#endif
        /// <summary>The calculatedEndDate property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CalculatedEndDate { get; set; }
#nullable restore
#else
        public string CalculatedEndDate { get; set; }
#endif
        /// <summary>The calculatedStartDate property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CalculatedStartDate { get; set; }
#nullable restore
#else
        public string CalculatedStartDate { get; set; }
#endif
        /// <summary>The groupBy property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? GroupBy { get; set; }
#nullable restore
#else
        public string GroupBy { get; set; }
#endif
        /// <summary>The requestId property</summary>
        public Guid? RequestId { get; set; }
        /// <summary>The resultCount property</summary>
        public int? ResultCount { get; set; }
        /// <summary>The results property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.QueryResponse_results>? Results { get; private set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.QueryResponse_results> Results { get; private set; }
#endif
        /// <summary>The timeBucket property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.TimeBucket? TimeBucket { get; set; }
        /// <summary>The totalAggregateCount property</summary>
        public int? TotalAggregateCount { get; set; }
        /// <summary>The totalRowCount property</summary>
        public int? TotalRowCount { get; set; }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.QueryResponse"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.QueryResponse CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.QueryResponse();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "aggregate", n => { Aggregate = n.GetStringValue(); } },
                { "calculatedEndDate", n => { CalculatedEndDate = n.GetStringValue(); } },
                { "calculatedStartDate", n => { CalculatedStartDate = n.GetStringValue(); } },
                { "groupBy", n => { GroupBy = n.GetStringValue(); } },
                { "requestId", n => { RequestId = n.GetGuidValue(); } },
                { "resultCount", n => { ResultCount = n.GetIntValue(); } },
                { "results", n => { Results = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.QueryResponse_results>(global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.QueryResponse_results.CreateFromDiscriminatorValue)?.AsList(); } },
                { "timeBucket", n => { TimeBucket = n.GetEnumValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.TimeBucket>(); } },
                { "totalAggregateCount", n => { TotalAggregateCount = n.GetIntValue(); } },
                { "totalRowCount", n => { TotalRowCount = n.GetIntValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("aggregate", Aggregate);
            writer.WriteStringValue("calculatedEndDate", CalculatedEndDate);
            writer.WriteStringValue("calculatedStartDate", CalculatedStartDate);
            writer.WriteStringValue("groupBy", GroupBy);
            writer.WriteGuidValue("requestId", RequestId);
            writer.WriteIntValue("resultCount", ResultCount);
            writer.WriteEnumValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.TimeBucket>("timeBucket", TimeBucket);
            writer.WriteIntValue("totalAggregateCount", TotalAggregateCount);
            writer.WriteIntValue("totalRowCount", TotalRowCount);
        }
    }
}
#pragma warning restore CS0618
