﻿using Microsoft.AspNetCore.Http;

namespace TaskManagementService.Core.Exceptions;

public static class ApplicationError
{
    public static ErrorDetails CreateFailedToRetrieveUserError(Guid userId, Guid accountId) => new()
    {
        ErrorCode = "FAILED_TO_RETRIEVE_USER",
        HttpStatusCode = StatusCodes.Status500InternalServerError,
        UserMessage = "An error occurred while retrieving user data",
        DeveloperMessage = $"Failed to retrieve user account information. UserId: {userId}, AccountId: {accountId}"
    };

    public static ErrorDetails CreateUnspecifiedError(string? message) => new()
    {
        ErrorCode = "UNSPECIFIED_ERROR",
        HttpStatusCode = StatusCodes.Status500InternalServerError,
        UserMessage = "An error occurred while processing request",
        DeveloperMessage = message
    };
}
