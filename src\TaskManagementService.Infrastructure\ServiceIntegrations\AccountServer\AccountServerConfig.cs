using System;
using System.Diagnostics;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

using DocuSign.AccountServer.TokenExchangeClient;
using DocuSign.OneConfig.Extensions;

using OpenTelemetry.Trace;

using Polly;
using Polly.Retry;

using TaskManagementService.Core.Config;

namespace TaskManagementService.Infrastructure.ServiceIntegrations.AccountServer;

internal sealed class AccountServerConfig : DefaultConfig
{
    public AccountServerConfig(IAccountServerConfig config)
    {
        ArgumentNullException.ThrowIfNull(config, nameof(config));

        AuthTokenUrl = config.AuthTokenUrl;
        HttpClientRetryHandlerMaxTries = config.HttpClientRetryHandlerMaxTries;
        HttpClientRetryHandlerSleepIntervalMs = config.HttpClientRetryHandlerSleepIntervalMs;
        ClientId = config.ClientId;
        JwtScope = config.JwtScope;
        IsLive = config.IsLive;
    }
}
