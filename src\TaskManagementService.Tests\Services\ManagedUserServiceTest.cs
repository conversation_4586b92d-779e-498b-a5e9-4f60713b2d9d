﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Authentication;
using Microsoft.Kiota.Abstractions.Serialization;

using Moq;

using NSubstitute;
using NSubstitute.ExceptionExtensions;

using TaskManagementService.Core.Interfaces;
using TaskManagementService.Infrastructure.Services;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models;

using Xunit;

namespace TaskManagementService.Tests.Services;

[Trait("TestType", "UnitTest")]
public class ManagedUserServiceTest
{
    private readonly Mock<ILogger<ManagedUserService>> _mockedILogger = new();
    private readonly Mock<IAuthenticationProvider> _mockedAuthenticationProvider = new();
    private readonly Mock<HttpClient> _mockedHttpClient = new();
    private readonly Mock<IRequestContextService> _mockedRequestContextService = new();

    public ManagedUserServiceTest()
    {
        _mockedRequestContextService.Setup(x => x.ClmApiBaseUrl).Returns(new Uri("https://test.com"));
        _mockedRequestContextService.Setup(x => x.IsClmAccount).Returns(true);
    }

    [Fact]
    public void GetCLMV2APIClient()
    {
        var managedUserService = new ManagedUserService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object);
        var result = managedUserService.GetCLMV2APIClient();
        Assert.NotNull(result);
        Assert.True(result.GetType() == typeof(CLMV2APIClient));
    }

    [Fact]
    public async Task GetManagedUsersAsync()
    {
        var managedUsers = new ApiCollectionTaskManagedUser()
        {
            AdditionalData = new Dictionary<string, object>(),
            First = "First value",
            Href = "https://test.com/test-test-test-1234",
            Items = [
                new TaskManagedUser
                {
                    Name = "User 1",
                    Uid = Guid.NewGuid(),
                    Children = [
                        new TaskManagedUser()
                        {
                            Name = "Managed User 1",
                            Uid = Guid.NewGuid()
                        },
                        new TaskManagedUser()
                        {
                            Name = "Managed User 2",
                            Uid = Guid.NewGuid(),
                            Count = 1
                        }
                    ],
                    Count = 2
                },
                new TaskManagedUser
                {
                    Name = "User 2",
                    Uid = Guid.NewGuid(),
                    Children = [
                        new TaskManagedUser()
                        {
                            Name = "Managed User 3",
                            Uid = Guid.NewGuid(),
                            Count = 4
                        }
                    ],
                    Count = 7
                }
            ]
        };

        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);

        adapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<ApiCollectionTaskManagedUser>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
            .ReturnsForAnyArgs(managedUsers);

        var managedUserService = new ManagedUserService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        var result = await managedUserService.GetManagedUsersAsync(Guid.NewGuid());

        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.True(result[0].Name == "User 1");
        Assert.True(result[1].Name == "User 2");
        Assert.True(result[0].ManagedUsers.Count() == 2);
        Assert.True(result[1].ManagedUsers.Count() == 1);
        Assert.True(result[1].ManagedUsers.First().TaskCount == 4);
        Assert.True(result[0].TaskCount == 2);
        Assert.True(result[1].TaskCount == 7);
    }

    [Fact]
    public async Task GetManagedUsersAsyncException()
    {
        var adapter = Substitute.For<IRequestAdapter>();
        var clmV2ApiClient = new CLMV2APIClient(adapter);

        var apiException = new ApiException("Test Exception");

        adapter.SendAsync(
                Arg.Any<RequestInformation>(),
                Arg.Any<ParsableFactory<ApiCollectionTaskManagedUser>>(),
                Arg.Any<Dictionary<string, ParsableFactory<IParsable>>>(),
                Arg.Any<CancellationToken>())
            .ThrowsForAnyArgs(apiException);

        var managedUserService = new ManagedUserService(_mockedILogger.Object, _mockedAuthenticationProvider.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object, clmV2ApiClient);
        var result = await managedUserService.GetManagedUsersAsync(Guid.NewGuid());

        Assert.Empty(result);
    }
}
