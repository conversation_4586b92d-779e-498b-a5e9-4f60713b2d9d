using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using Mapster;

using MediatR;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using TaskManagementService.Core.Authorization;
using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

namespace TaskManagementService.Controllers;

[ApiController]
[Authorize(AuthPolicy.JwtToken)]
[Authorize(AuthPolicy.SignatureTasks)]
[Produces("application/json")]
[Route("accounts/{accountId:guid}/tasks")]
public class TasksController(IMediator mediator) : ControllerBase
{
    [HttpGet("{source}/{taskId:guid}/history")]
    public async Task<ActionResult<TaskHistory>> TaskHistoryAsync(Guid accountId, TaskSource source, Guid taskId, string locale = null)
    {
        ArgumentNullException.ThrowIfNull(mediator, nameof(mediator));

        var taskHistory = await mediator.Send(new GetUserTaskHistoryQuery(accountId, source, taskId, locale));

        return taskHistory?.AuditEvents != null && taskHistory.AuditEvents.Any()
            ? taskHistory
            : NotFound();
    }

    [HttpGet("count")]
    public async Task<ActionResult<TasksCount>> GetTasksCountAsync(Guid accountId, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(mediator, nameof(mediator));
        var response = await mediator.Send(new GetUserTasksCountQuery(), cancellationToken);
        return response.TasksCount;
    }

    [HttpPost]
    public async Task<PaginatedList<UserTask>> GetTasksAsync(Guid accountId, [FromBody] TaskFilter taskFilter, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(mediator, nameof(mediator));

        var tasks = await mediator.Send(new GetUserTasksQuery(accountId, taskFilter), cancellationToken);
        return tasks;
    }

    [HttpPut("esignature/{taskId}/void")]
    public Task VoidESignTaskAsync(Guid accountId, string taskId, [FromBody] ESignatureTaskVoidBody body)
    {
        ArgumentNullException.ThrowIfNull(mediator, nameof(mediator));
        ArgumentNullException.ThrowIfNull(body, nameof(body));
        return mediator.Send(new VoidESignEnvelopeCommand(accountId, taskId, body.VoidedReason));
    }

    [HttpPut("esignature/{taskId}/resend")]
    public Task ResendESignTaskAsync(Guid accountId, string taskId)
    {
        ArgumentNullException.ThrowIfNull(mediator, nameof(mediator));

        return mediator.Send(new ResendESignEnvelopeCommand(accountId, taskId));
    }

    [HttpGet("groups")]
    public async Task<List<TaskGroup>> TaskGroupsAsync(Guid accountId, bool includeMembers = false)
    {
        ArgumentNullException.ThrowIfNull(mediator, nameof(mediator));

        var tasks = await mediator.Send(new GetUserTasksGroupsQuery(accountId, includeMembers));
        return tasks.Adapt<List<TaskGroup>>();
    }

    [HttpPost("clm/{taskId}/unassign")]
    public async Task<List<string>> UnassignTaskAsync(Guid accountId, Guid taskId)
    {
        ArgumentNullException.ThrowIfNull(mediator, nameof(mediator));

        var listResponse = await mediator.Send(new UnassignTaskCommand(accountId, taskId));
        return listResponse;
    }

    [HttpPost("clm/{taskId}/assign/{assigneeId}")]
    public async Task<List<string>> AssignTaskAsync(Guid accountId, Guid taskId, string assigneeId)
    {
        ArgumentNullException.ThrowIfNull(mediator, nameof(mediator));

        var listResponse = await mediator.Send(new AssignTaskCommand(accountId, taskId, assigneeId));
        return listResponse;
    }
}
