{"descriptionHash": "B735FB1421DCAEAE8DB59F19762D7D6ECBD2F4827898F5FA7D9E441548747DA6D4E3BFE9D907694EB0A6EFF9380BCDF3677C3B7769B4D93161417B6A3FB717CB", "descriptionLocation": "../../contracts/assignmentsapi.yml", "lockFileVersion": "1.0.0", "kiotaVersion": "1.20.0", "clientClassName": "AssignmentsApiClient", "typeAccessModifier": "Public", "clientNamespaceName": "TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi", "language": "CSharp", "usesBackingStore": false, "excludeBackwardCompatible": false, "includeAdditionalData": true, "disableSSLValidation": false, "serializers": ["Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory"], "deserializers": ["Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory"], "structuredMimeTypes": ["application/json"], "includePatterns": [], "excludePatterns": [], "disabledValidationRules": []}