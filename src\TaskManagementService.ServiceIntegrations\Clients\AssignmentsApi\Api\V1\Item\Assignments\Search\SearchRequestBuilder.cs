// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using System;
using TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models;
namespace TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Search
{
    /// <summary>
    /// Builds and executes requests for operations under \api\v1\{accountId}\Assignments\search
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class SearchRequestBuilder : BaseRequestBuilder
    {
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Search.SearchRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public SearchRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/v1/{accountId}/Assignments/search{?count*,pagingCursor*}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Search.SearchRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public SearchRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/v1/{accountId}/Assignments/search{?count*,pagingCursor*}", rawUrl)
        {
        }
        /// <summary>
        /// Find assignments by search.
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponseListResponse"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
        /// <exception cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails">When receiving a 400 status code</exception>
        /// <exception cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails">When receiving a 401 status code</exception>
        /// <exception cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails">When receiving a 422 status code</exception>
        /// <exception cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails">When receiving a 500 status code</exception>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponseListResponse?> PostAsync(global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentsSearchRequest body, Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Search.SearchRequestBuilder.SearchRequestBuilderPostQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponseListResponse> PostAsync(global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentsSearchRequest body, Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Search.SearchRequestBuilder.SearchRequestBuilderPostQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = ToPostRequestInformation(body, requestConfiguration);
            var errorMapping = new Dictionary<string, ParsableFactory<IParsable>>
            {
                { "400", global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails.CreateFromDiscriminatorValue },
                { "401", global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails.CreateFromDiscriminatorValue },
                { "422", global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails.CreateFromDiscriminatorValue },
                { "500", global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails.CreateFromDiscriminatorValue },
            };
            return await RequestAdapter.SendAsync<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponseListResponse>(requestInfo, global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponseListResponse.CreateFromDiscriminatorValue, errorMapping, cancellationToken).ConfigureAwait(false);
        }
        /// <summary>
        /// Find assignments by search.
        /// </summary>
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToPostRequestInformation(global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentsSearchRequest body, Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Search.SearchRequestBuilder.SearchRequestBuilderPostQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToPostRequestInformation(global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentsSearchRequest body, Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Search.SearchRequestBuilder.SearchRequestBuilderPostQueryParameters>> requestConfiguration = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = new RequestInformation(Method.POST, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json");
            requestInfo.SetContentFromParsable(RequestAdapter, "application/json", body);
            return requestInfo;
        }
        /// <summary>
        /// Returns a request builder with the provided arbitrary URL. Using this method means any other path or query parameters are ignored.
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Search.SearchRequestBuilder"/></returns>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Search.SearchRequestBuilder WithUrl(string rawUrl)
        {
            return new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Search.SearchRequestBuilder(rawUrl, RequestAdapter);
        }
        /// <summary>
        /// Find assignments by search.
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class SearchRequestBuilderPostQueryParameters 
        {
            [QueryParameter("count")]
            public int? Count { get; set; }
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            [QueryParameter("pagingCursor")]
            public string? PagingCursor { get; set; }
#nullable restore
#else
            [QueryParameter("pagingCursor")]
            public string PagingCursor { get; set; }
#endif
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class SearchRequestBuilderPostRequestConfiguration : RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Search.SearchRequestBuilder.SearchRequestBuilderPostQueryParameters>
        {
        }
    }
}
#pragma warning restore CS0618
