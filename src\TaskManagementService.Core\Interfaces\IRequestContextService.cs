﻿using System.Security.Claims;

namespace TaskManagementService.Core.Interfaces;

public interface IRequestContextService
{
    public Guid AccountId { get; }
    public Guid UserId { get; }
    public string AccountName { get; }
    public string ShardId { get; set; }
    public string Authorization { get; }
    public string TraceParent { get; }
    public string TraceToken { get; }
    public string RequestPath { get; }
    public IReadOnlyList<Claim> Claims { get; }
    public Uri Issuer { get; }
    public bool IsClmAccount { get; }
    public Uri ESignApiBaseUrl { get; }
    public Uri? ClmApiBaseUrl { get; }
}
