﻿using System.Globalization;
using System.Net;
using System.Text;
using System.Text.Json;

using Flurl;

using Mapster;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Exceptions;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Infrastructure.ServiceIntegrations.UnifiedRepositoryApi.Models;

namespace TaskManagementService.Infrastructure.ServiceIntegrations.UnifiedRepositoryApi;

public class UnifiedRepositoryService : IUnifiedRepositoryService
{
    public const string ShardIdHeaderName = "DocuSign-Shard-Id";
    public const string AuthorizationHeaderName = "Authorization";
    public const int DefaultPageSize = 100;

    private readonly IUnifiedRepositoryApiConfig _unifiedRepositoryApiConfig;
    private readonly HttpClient _httpClient;
    private readonly IRequestContextService _requestContextService;

    public UnifiedRepositoryService(IUnifiedRepositoryApiConfig unifiedRepositoryApiConfig, HttpClient httpClient, IRequestContextService requestContextService)
    {
        _unifiedRepositoryApiConfig = unifiedRepositoryApiConfig;
        _httpClient = httpClient;
        _requestContextService = requestContextService;
    }

    public async Task<Dictionary<Guid, UserTaskAgreement>> GetAgreementsAsync(GetAgreementsRequest request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(request);
        ArgumentNullException.ThrowIfNull(request.AgreementIds);

        if (request.AgreementIds.Count == 0)
        {
            return [];
        }

        var agreements = new Dictionary<Guid, UserTaskAgreement>();

        var requestUrl = string.Format(CultureInfo.InvariantCulture, _unifiedRepositoryApiConfig.BaseUrl, _requestContextService.ShardId)
            .AppendPathSegments($"/v1/accounts/{_requestContextService.AccountId}/agreements/query")
            .AppendQueryParam("includeAiMetadata", request.ShouldFetchAiMetadata ? "Basic" : "None")
            .AppendQueryParam("includeEnvelopeData", request.ShouldFetchEnvelopeData ? "true" : "false");

        var agreementsSearchRequest = new AgreementSearchRequestDto
        {
            Limit = DefaultPageSize,
            CToken = null,
            Filters = request.Filters.Adapt<List<AgreementFilterDto>>(),
            Sort = request.Sort.Adapt<List<AgreementSortDto>>(),
        };

        // This is important. We can only apply other additional filters on top of the agreement ids.
        agreementsSearchRequest.Filters.Add(new AgreementFilterDto
        {
            // CLM document id and document storage id are the same.
            Field = "data.documentStorageId",
            Operator = "In",
            Value = request.AgreementIds.Select(id => id.ToString()).ToList(),
        });

        string? ctoken = null;
        do
        {
            if (ctoken != null)
            {
                agreementsSearchRequest.CToken = ctoken;
            }

            using var httpRequest = new HttpRequestMessage(HttpMethod.Post, requestUrl.ToUri());
            httpRequest.Headers.TryAddWithoutValidation(AuthorizationHeaderName, _requestContextService.Authorization);
            httpRequest.Headers.TryAddWithoutValidation(ShardIdHeaderName, _requestContextService.ShardId);
            httpRequest.Content = new StringContent(JsonSerializer.Serialize(agreementsSearchRequest), Encoding.UTF8, "application/json");

            var response = await _httpClient.SendAsync(httpRequest, cancellationToken);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var apiAgreements = JsonSerializer.Deserialize<AgreementsApiDto>(content) ?? new AgreementsApiDto();
                var mappedAgreements = apiAgreements.Agreements.Adapt<List<UserTaskAgreement>>();
                foreach (var mappedAgreement in mappedAgreements)
                {
                    if (mappedAgreement.Data == null)
                    {
                        continue;
                    }

                    agreements.TryAdd(mappedAgreement.Data.DocumentStorageId, mappedAgreement);
                }

                ctoken = apiAgreements.CToken;
            }
            else
            {
                if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    return [];
                }

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var message = $"Failed to fetch agreements list. Status code: {response.StatusCode}. Content: {content}";
                throw new TaskManagementServiceException(ApplicationError.CreateUnspecifiedError(message));
            }
        }
        while (!string.IsNullOrWhiteSpace(ctoken));

        return agreements;
    }
}
