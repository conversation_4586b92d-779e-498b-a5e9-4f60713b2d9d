#!/bin/bash

# Read Input Argument
serviceUrl=$1
testStage=$2
wafEnabled=$3

#Run Periodic Tests
if [ -z "$testStage" ]; then
    echo "Running Periodic Tests for serviceUrl: $serviceUrl wafEnabled: $wafEnabled"
    if [ "$wafEnabled" != "true" ]; then
        (instance=$serviceUrl dotnet TaskManagementService.Periodic.Tests.dll -notrait Category=Waf)
    else
        (instance=$serviceUrl dotnet TaskManagementService.Periodic.Tests.dll)
    fi
else
    echo "Running Periodic Tests for serviceUrl: $serviceUrl testStage: $testStage wafEnabled: $wafEnabled"
    if [ "$wafEnabled" != "true" ]; then
        (instance=$serviceUrl dotnet TaskManagementService.Periodic.Tests.dll -trait Category=$testStage -notrait Category=Waf)
    else
        (instance=$serviceUrl dotnet TaskManagementService.Periodic.Tests.dll -trait Category=$testStage)
    fi
fi

periodicExitCode=$?                                   # Save the exit code

echo "periodicExitCode:$periodicExitCode"
exit $periodicExitCode # Return the exit code from the periodic tests
