using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using Moq;

using TaskManagementService.Core.Handlers;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

using Xunit;

namespace TaskManagementService.Tests.Handlers;

[Trait("TestType", "UnitTest")]
public class GetUserAgreementsQueryHandlerTest
{
    [Fact]
    public void Handle()
    {
        var mockLogger = new Mock<ILogger<GetUserAgreementsQueryHandler>>();
        var mockUnifiedRepositoryService = new Mock<IUnifiedRepositoryService>();

        var mockAccountId = new Guid("********-0004-0001-000b-************");
        var mockAgreementId = new Guid("*************-0001-000b-************");

        var mockGetUserAgreementsQuery =
            new GetUserAgreementsQuery(mockAccountId, new HashSet<Guid> { mockAgreementId }, [], []);

        mockUnifiedRepositoryService
            .Setup(x => x.GetAgreementsAsync(It.IsAny<GetAgreementsRequest>(), CancellationToken.None))
            .Returns(Task.FromResult(new Dictionary<Guid, UserTaskAgreement>()));

        var getUserAgreementsQueryHandler =
            new GetUserAgreementsQueryHandler(mockLogger.Object, mockUnifiedRepositoryService.Object);

        var result = getUserAgreementsQueryHandler.Handle(mockGetUserAgreementsQuery, CancellationToken.None);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task HandleWithAgreementAsync()
    {
        var mockLogger = new Mock<ILogger<GetUserAgreementsQueryHandler>>();
        var mockUnifiedRepositoryService = new Mock<IUnifiedRepositoryService>();

        var mockAccountId = new Guid("********-0004-0001-000b-************");
        var mockAgreementId = new Guid("*************-0001-000b-************");
        var mockIngestionId = "*************-0001-000b-************";
        var mockAgreementStat = "*************-0001-000b-************";
        var mockName = "name";
        var mockSourceName = "name2";
        var mockExtractionReviewSource = "source";
        var mockCurrencyCode = "$";
        var mockPartyDetailsId = new Guid("********-0004-0001-000b-************");

        var startDateTimeOffset = new DateTimeOffset(new DateTime(2025, 1, 1));
        var modifiedDateTimeOffset = new DateTimeOffset(new DateTime(2025, 1, 2));
        var mockValue = new Value { CurrencyCode = mockCurrencyCode, DoubleValue = 1000 };

        var mockPartyExtractionReview = new ExtractionReview() { ExtractionCount = 1, Source = mockExtractionReviewSource };
        var mockPartyDetails = new PartyDetails() { Id = mockPartyDetailsId };
        var mockParty = new Party() { DisplayName = mockName, ExtractionReview = mockPartyExtractionReview, PartyDetails = mockPartyDetails };

        var mockAgreementData = new AgreementData()
        {
            SourceId = mockAgreementId,
            SourceAccountId = mockAccountId,
            SourceIngestionId = mockIngestionId,
            Id = mockAgreementId,
            AgreementStatus = mockAgreementStat,
            Parties = [mockParty],
            PendingExtractionReviewCount = 1,
            Name = mockName,
            SourceName = mockSourceName,
            TotalValue = mockValue
        };

        var mockAgreement = new UserTaskAgreement() { CreatedAt = startDateTimeOffset, Id = mockAgreementId, ModifiedAt = modifiedDateTimeOffset, Etag = 1, Data = mockAgreementData };

        var mockGetUserAgreementsQuery =
            new GetUserAgreementsQuery(mockAccountId, new HashSet<Guid> { mockAgreementId }, [], []);

        mockUnifiedRepositoryService
            .Setup(x => x.GetAgreementsAsync(It.IsAny<GetAgreementsRequest>(), CancellationToken.None))
            .Returns(Task.FromResult(new Dictionary<Guid, UserTaskAgreement>() { { mockAgreementId, mockAgreement } }));

        var getUserAgreementsQueryHandler =
            new GetUserAgreementsQueryHandler(mockLogger.Object, mockUnifiedRepositoryService.Object);

        var result = await getUserAgreementsQueryHandler.Handle(mockGetUserAgreementsQuery, CancellationToken.None);

        Assert.NotNull(result);
        var getAgreement = result.TryGetValue(mockAgreementId, out var agreement);
        Assert.True(getAgreement);
        Assert.Equal(startDateTimeOffset, agreement.CreatedAt);
        Assert.Equal(modifiedDateTimeOffset, agreement.ModifiedAt);
        Assert.Equal(1, agreement.Etag);
        Assert.Equal(mockAgreementId, agreement.Id);

        var agreementData = agreement.Data;
        Assert.NotNull(agreementData);
        Assert.Equal(mockAgreementId, agreementData.Id);
        Assert.Equal(mockAgreementId, agreementData.SourceId);
        Assert.Equal(mockAccountId, agreementData.SourceAccountId);
        Assert.Equal(mockIngestionId, agreementData.SourceIngestionId);
        Assert.Equal(mockAgreementStat, agreementData.AgreementStatus);
        Assert.Equal(mockName, agreementData.Name);
        Assert.Equal(mockSourceName, agreementData.SourceName);

        Assert.NotNull(agreementData.Parties);
        Assert.Single(agreementData.Parties);
        var party = agreementData.Parties[0];
        Assert.Equal(mockName, party.DisplayName);

        Assert.NotNull(party.ExtractionReview);
        var extractionReview = party.ExtractionReview;
        Assert.Equal(mockExtractionReviewSource, extractionReview.Source);
        Assert.Equal(1, extractionReview.ExtractionCount);

        Assert.NotNull(party.PartyDetails);
        var partyDetails = party.PartyDetails;
        Assert.Equal(mockPartyDetailsId, partyDetails.Id);
    }

    [Fact]
    public async Task NullHandle()
    {
        var mockLogger = new Mock<ILogger<GetUserAgreementsQueryHandler>>();
        var mockUnifiedRepositoryService = new Mock<IUnifiedRepositoryService>();

        var getUserAgreementsQueryHandler =
            new GetUserAgreementsQueryHandler(mockLogger.Object, mockUnifiedRepositoryService.Object);

        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            getUserAgreementsQueryHandler.Handle(null, CancellationToken.None));
    }
}
