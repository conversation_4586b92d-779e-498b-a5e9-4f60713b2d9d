<Project>
  <PropertyGroup>
    <CodeAnalysisRuleSet Condition=" '$(IsLegacyCodeAnalysis)' == 'true' ">$(CodeAnalysisConfigRuleSetFilePath)</CodeAnalysisRuleSet>
    <CodeAnalysisRuleSet Condition=" '$(CodeAnalysisRuleSet)' == '' ">$(CodeAnalysisConfigRuleSetFilePath)</CodeAnalysisRuleSet>
    <EnableStyleCopAnalyzers Condition=" '$(EnableStyleCopAnalyzers)' == '' ">true</EnableStyleCopAnalyzers>
  </PropertyGroup>

  <Choose>
    <When Condition=" '$(EnableStyleCopAnalyzers)' == 'true' ">
      <ItemGroup>
        <!-- Adds StyleCop analyzers to enforce consistent styling in addition to roslyn analyzers (EnforceCodeStyleInBuild)  -->
        <PackageReference Include="StyleCop.Analyzers">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
      </ItemGroup>
    </When>
  </Choose>

  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.Threading.Analyzers" PrivateAssets="all" IncludeAssets="runtime; build; native; contentfiles; analyzers; buildtransitive" />
    <PackageReference Include="Microsoft.VisualStudio.SlnGen" PrivateAssets="all" IncludeAssets="runtime; build; native; contentfiles; analyzers; buildtransitive" />
    <PackageReference Include="Microsoft.SourceLink.GitHub" PrivateAssets="all"/>
    <PackageReference Include="AsyncFixer"  PrivateAssets="all" IncludeAssets="runtime; build; native; contentfiles; analyzers; buildtransitive" />
  </ItemGroup>

  <!-- stylecop configuration -->
  <ItemGroup>
    <AdditionalFiles Include="$(CodeAnalysisConfigFilePath)" Link="stylecop.json" Visible="false" />
  </ItemGroup>
</Project>
