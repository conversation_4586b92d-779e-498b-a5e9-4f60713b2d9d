// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class Securities : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The Groups property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityGroup>? Groups { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityGroup> Groups { get; set; }
#endif
        /// <summary>The Roles property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityString>? Roles { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityString> Roles { get; set; }
#endif
        /// <summary>The Users property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityUser>? Users { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityUser> Users { get; set; }
#endif
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Securities"/> and sets the default values.
        /// </summary>
        public Securities()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Securities"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Securities CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Securities();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "Groups", n => { Groups = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityGroup>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityGroup.CreateFromDiscriminatorValue)?.AsList(); } },
                { "Roles", n => { Roles = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityString>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityString.CreateFromDiscriminatorValue)?.AsList(); } },
                { "Users", n => { Users = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityUser>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityUser.CreateFromDiscriminatorValue)?.AsList(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityGroup>("Groups", Groups);
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityString>("Roles", Roles);
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.SecurityUser>("Users", Users);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
