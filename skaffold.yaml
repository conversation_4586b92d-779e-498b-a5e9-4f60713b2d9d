# https://github.docusignhq.com/pages/Microservices/msf-docs/developer-productivity/msf-truedev-environment/
apiVersion: skaffold/v3
kind: Config
metadata:
    name: task-management-service
requires:
    - path: skaffold-redis.yaml
build:
    platforms:
        - linux/amd64
    local:
        push: true
    tagPolicy:
        customTemplate:
            template: "{{.DATE}}_{{.SHA}}"
            components:
                - name: DATE
                  dateTime:
                      format: "2006-01-02_15-04-05"
                      timezone: UTC
                - name: SHA
                  gitCommit:
                      variant: AbbrevCommitSha
    artifacts:
        - image: msfcontainerregistrytruedevglobal.azurecr.io/task-management-service
          context: src/TaskManagementService/
          platforms:
              - linux/amd64
          custom:
              buildCommand: dotnet r skaffold:build
              dependencies:
                  dockerfile:
                      path: Dockerfile
                      buildArgs:
                          DOTNET_CONFIGURATION: "Release"
                          DEBUGGER: "false"
deploy:
    kubeContext: msf-development-westus3-a
    helm:
        releases:
            - name: task-management-service-{{ coalesce (index . "USERNAME") (index . "USER") | replace "." "-" | lower }}
              namespace: task-management
              remoteChart: ds-microservice-shared-chart
              repo: https://helm.docusignhq.com
              upgradeOnChange: true
              wait: true
              setValueTemplates:
                  image.tag: "{{.IMAGE_TAG}}"
                  image.repository: msfcontainerregistrytruedevglobal.azurecr.io
                  annotations.username: '{{ coalesce (index . "USERNAME") (index . "USER") | lower }}'
                  virtualService.spec.http[0].match[0].uri.prefix: /task-management-service-{{ coalesce (index . "USERNAME") (index . "USER") | replace "." "-" | lower }}/v1.0/health
                  virtualService.spec.http[1].match[0].uri.prefix: /task-management-service-{{ coalesce (index . "USERNAME") (index . "USER") | replace "." "-" | lower }}/v1.0/swagger
                  virtualService.spec.http[2].match[0].uri.prefix: /task-management-service-{{ coalesce (index . "USERNAME") (index . "USER") | replace "." "-" | lower }}/v1.0/version
                  env._local:
                      - name: "USERNAME"
                        value: '{{ coalesce (index . "USERNAME") (index . "USER") | lower }}'
              valuesFiles:
                  - ./config/shared/app-values.yaml
                  - ./config/local/app-values.yaml
