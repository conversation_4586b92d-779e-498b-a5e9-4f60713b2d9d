﻿using Microsoft.AspNetCore.Http;

namespace TaskManagementService.Core.Exceptions;

public class TaskManagementServiceException : Exception
{
    public TaskManagementServiceException()
    {
    }

    public TaskManagementServiceException(string message)
        : base(message)
    {
        ErrorDetails = new ErrorDetails
        {
            HttpStatusCode = StatusCodes.Status500InternalServerError,
            DeveloperMessage = message
        };
    }

    public TaskManagementServiceException(string message, Exception innerException)
        : base(message, innerException)
    {
        ErrorDetails = new ErrorDetails
        {
            HttpStatusCode = StatusCodes.Status500InternalServerError,
            DeveloperMessage = message
        };
    }

    public TaskManagementServiceException(ErrorDetails errorDetails)
        : base(errorDetails?.DeveloperMessage)
    {
        ErrorDetails = errorDetails!;
    }

    public ErrorDetails ErrorDetails { get; set; } = new();
}
