﻿using Microsoft.Kiota.Abstractions.Authentication;

using TaskManagementService.Infrastructure.ServiceIntegrations.AccountServer;

namespace TaskManagementService.Infrastructure.Implementations;

internal sealed class ApplicationTokenProvider(IAccountServerTokenExchangeClient accountServerTokenExchangeClient) : IAccessTokenProvider
{
    public async Task<string> GetAuthorizationTokenAsync(Uri uri, Dictionary<string, object>? additionalAuthenticationContext = null, CancellationToken cancellationToken = default)
    {
        var token = await accountServerTokenExchangeClient.GetAppTokenAsync(default) ?? throw new InvalidOperationException("Failed to get application token");
        return token;
    }

    public AllowedHostsValidator AllowedHostsValidator { get; } = new AllowedHostsValidator(Array.Empty<string>());
}
