// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using System;
using TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models;
using TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests
{
    /// <summary>
    /// Builds and executes requests for operations under \reporting-adhoc-queries-ipr\v1.0\query\models
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class ModelsRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The details property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details.DetailsRequestBuilder Details
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.Details.DetailsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.ModelsRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public ModelsRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/reporting-adhoc-queries-ipr/v1.0/query/models{?accountId*,modelType*}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.ModelsRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public ModelsRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/reporting-adhoc-queries-ipr/v1.0/query/models{?accountId*,modelType*}", rawUrl)
        {
        }
        /// <returns>A List&lt;global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.AdHocModel&gt;</returns>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
        /// <exception cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.ProblemDetails">When receiving a 400 status code</exception>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.AdHocModel>?> GetAsync(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.ModelsRequestBuilder.ModelsRequestBuilderGetQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.AdHocModel>> GetAsync(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.ModelsRequestBuilder.ModelsRequestBuilderGetQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            var requestInfo = ToGetRequestInformation(requestConfiguration);
            var errorMapping = new Dictionary<string, ParsableFactory<IParsable>>
            {
                { "400", global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.ProblemDetails.CreateFromDiscriminatorValue },
            };
            var collectionResult = await RequestAdapter.SendCollectionAsync<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.AdHocModel>(requestInfo, global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.AdHocModel.CreateFromDiscriminatorValue, errorMapping, cancellationToken).ConfigureAwait(false);
            return collectionResult?.AsList();
        }
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.ModelsRequestBuilder.ModelsRequestBuilderGetQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.ModelsRequestBuilder.ModelsRequestBuilderGetQueryParameters>> requestConfiguration = default)
        {
#endif
            var requestInfo = new RequestInformation(Method.GET, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json");
            return requestInfo;
        }
        /// <summary>
        /// Returns a request builder with the provided arbitrary URL. Using this method means any other path or query parameters are ignored.
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.ModelsRequestBuilder"/></returns>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.ModelsRequestBuilder WithUrl(string rawUrl)
        {
            return new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.ModelsRequestBuilder(rawUrl, RequestAdapter);
        }
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        #pragma warning disable CS1591
        public partial class ModelsRequestBuilderGetQueryParameters 
        #pragma warning restore CS1591
        {
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            [QueryParameter("accountId")]
            public string? AccountId { get; set; }
#nullable restore
#else
            [QueryParameter("accountId")]
            public string AccountId { get; set; }
#endif
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            [QueryParameter("modelType")]
            public string? ModelType { get; set; }
#nullable restore
#else
            [QueryParameter("modelType")]
            public string ModelType { get; set; }
#endif
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class ModelsRequestBuilderGetRequestConfiguration : RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.ModelsRequests.ModelsRequestBuilder.ModelsRequestBuilderGetQueryParameters>
        {
        }
    }
}
#pragma warning restore CS0618
