using System.Text.Json;

using TaskManagementService.Periodic.Tests.Utils;

using xRetry;

using Xunit;
using Xunit.Abstractions;

namespace TaskManagementService.Periodic.Tests;

public class SmokeApiTests(ITestOutputHelper testOutputHelper) : PeriodicTestBase
{
    private static readonly JsonSerializerOptions JsonSerializerOptions = new() { WriteIndented = true };

    [RetryFact(3, 2000)]
    [Trait("Priority", "1")]
    [Trait("Category", "Periodic")]
    public async Task CheckLivenessTestAsync()
    {
        testOutputHelper.WriteLine("=============================================");
        testOutputHelper.WriteLine("Running Task Management Service Liveness Test");
        testOutputHelper.WriteLine("=============================================");

        var response = await AnalyzeSampleDotnetRequests.GetServiceLivenessAsync(HealthCheckEndpoint);
        testOutputHelper.WriteLine($"Service Liveness Response: {response}");

        var content = await response.Content.ReadAsStringAsync();
        using var jsonDocument = JsonDocument.Parse(content);
        var formattedJson = JsonSerializer.Serialize(jsonDocument.RootElement, JsonSerializerOptions);
        testOutputHelper.WriteLine($"Service Liveness Content: {formattedJson}");

        Assert.True(response.IsSuccessStatusCode, "Service should be healthy.");
    }
}
