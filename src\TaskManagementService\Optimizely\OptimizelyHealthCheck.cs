using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

using DocuSign.OneConfig.Extensions.Msf;

using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

using TaskManagementService.Config;

namespace TaskManagementService.Optimizely;

/// <summary>
/// Health check for Optimizely integration to verify the service configuration is valid.
/// </summary>
public sealed class OptimizelyHealthCheck : IHealthCheck
{
    private readonly OneConfigOptimizelyOptions _optimizelyOptions;
    private readonly OptimizelyHealthCheckOptions _healthCheckOptions;
    private readonly ILogger<OptimizelyHealthCheck> _logger;
    private readonly HttpClient _httpClient;

    public OptimizelyHealthCheck(
        IOptions<OneConfigOptimizelyOptions> optimizelyOptions,
        IOptions<HealthCheckSettings> healthCheckSettings,
        ILogger<OptimizelyHealthCheck> logger,
        HttpClient httpClient)
    {
        _optimizelyOptions = optimizelyOptions?.Value ?? throw new ArgumentNullException(nameof(optimizelyOptions));
        _healthCheckOptions = healthCheckSettings?.Value?.Optimizely ?? throw new ArgumentNullException(nameof(healthCheckSettings));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
    }

    /// <summary>
    /// Performs the health check by verifying Optimizely configuration is valid and datafiles are accessible.
    /// </summary>
    /// <param name="context">The health check context.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>The health check result.</returns>
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Starting Optimizely configuration and datafile health check");

            if (!_optimizelyOptions.EnableGlobalProject && (_optimizelyOptions.Projects?.Count ?? 0) == 0)
            {
                _logger.LogInformation("Optimizely is disabled, marking as healthy");
                return HealthCheckResult.Healthy("Optimizely is disabled");
            }

            var (healthyProjects, totalProjects, validationIssues) = await ValidateAllProjectsAsync(cancellationToken);

            if (_healthCheckOptions.LogOnlyMode)
            {
                return HandleLogOnlyMode(healthyProjects, totalProjects, validationIssues);
            }

            return HandleStandardMode(healthyProjects, totalProjects, validationIssues);
        }
#pragma warning disable CA1031 // Do not catch general exception types
        catch (Exception ex)
#pragma warning restore CA1031 // Do not catch general exception types
        {
            _logger.LogError(ex, "Optimizely health check failed with an unexpected exception.");
            return _healthCheckOptions.LogOnlyMode
                ? HealthCheckResult.Healthy("Optimizely health check failed with an exception (log-only mode).")
                : HealthCheckResult.Unhealthy("Optimizely health check failed with an exception.", ex);
        }
    }

    private async Task<(int healthyProjects, int totalProjects, List<string> validationIssues)> ValidateAllProjectsAsync(CancellationToken cancellationToken)
    {
        var healthyProjects = 0;
        var totalProjects = 0;
        var validationIssues = new List<string>();

        if (_optimizelyOptions.EnableGlobalProject)
        {
            totalProjects++;
            var (isHealthy, errorMessage) = await ValidateProjectAsync("GlobalProject", _optimizelyOptions.GlobalProjectSdkKey, cancellationToken);
            if (isHealthy)
            {
                healthyProjects++;
            }
            else
            {
                validationIssues.Add(errorMessage);
            }
        }

        if (_optimizelyOptions.Projects?.Count > 0)
        {
            foreach (var project in _optimizelyOptions.Projects)
            {
                totalProjects++;
                var (isHealthy, errorMessage) = await ValidateProjectAsync(project.Key, project.Value.SdkKey, cancellationToken);
                if (isHealthy)
                {
                    healthyProjects++;
                }
                else
                {
                    validationIssues.Add(errorMessage);
                }
            }
        }

        return (healthyProjects, totalProjects, validationIssues);
    }

    private async Task<(bool IsHealthy, string ErrorMessage)> ValidateProjectAsync(string projectName, string sdkKey, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(sdkKey))
        {
            var errorMessage = $"Project {projectName} is configured but missing SDK key";
            _logger.LogWarning("Optimizely health check validation issue: {ErrorMessage}", errorMessage);
            return (false, errorMessage);
        }

        var datafileResult = await ValidateDatafileAsync(projectName, sdkKey, cancellationToken);
        if (!datafileResult.IsValid)
        {
            var errorMessage = $"Project {projectName} datafile validation failed: {datafileResult.ErrorMessage}";
            _logger.LogWarning("Optimizely health check validation issue: {ErrorMessage}", errorMessage);
            return (false, errorMessage);
        }

        _logger.LogDebug("Project {ProjectName} has valid SDK key and datafile", projectName);
        return (true, string.Empty);
    }

    private HealthCheckResult HandleLogOnlyMode(int healthyProjects, int totalProjects, List<string> validationIssues)
    {
        if (validationIssues.Count > 0)
        {
            var issuesMessage = string.Join("; ", validationIssues);
            _logger.LogWarning(
                "Optimizely validation completed with {IssueCount} issues (log-only mode): {Issues}",
                validationIssues.Count,
                issuesMessage);
        }

        var message = totalProjects == 0
            ? "Optimizely is enabled but no projects configured (log-only mode)"
            : $"Optimizely validation completed (log-only mode). {healthyProjects}/{totalProjects} projects valid. Issues: {validationIssues.Count}";

        _logger.LogInformation("Optimizely health check completed in log-only mode: {Message}", message);
        return HealthCheckResult.Healthy(message);
    }

    private HealthCheckResult HandleStandardMode(int healthyProjects, int totalProjects, List<string> validationIssues)
    {
        if (totalProjects == 0)
        {
            const string errorMessage = "Optimizely appears to be enabled but no projects are configured";
            _logger.LogWarning("Optimizely health check failed: {ErrorMessage}", errorMessage);
            return HealthCheckResult.Unhealthy(errorMessage);
        }

        if (validationIssues.Count > 0)
        {
            var issuesMessage = string.Join("; ", validationIssues);
            _logger.LogWarning(
                "Optimizely validation completed with {IssueCount} issues: {Issues}",
                validationIssues.Count,
                issuesMessage);

            if (healthyProjects > 0)
            {
                var degradedMessage = $"Optimizely partially healthy. {healthyProjects}/{totalProjects} projects valid. Issues: {validationIssues.Count}";
                return HealthCheckResult.Degraded(degradedMessage);
            }

            var unhealthyMessage = $"Optimizely validation failed. 0/{totalProjects} projects valid. Issues: {issuesMessage}";
            return HealthCheckResult.Unhealthy(unhealthyMessage);
        }

        var successMessage = $"Optimizely configuration and datafiles are valid. {healthyProjects}/{totalProjects} projects configured correctly";
        _logger.LogDebug("Optimizely health check completed successfully: {Message}", successMessage);
        return HealthCheckResult.Healthy(successMessage);
    }

    private async Task<DatafileValidationResult> ValidateDatafileAsync(string projectName, string sdkKey, CancellationToken cancellationToken)
    {
        try
        {
            var datafileUrl = new Uri($"https://cdn.optimizely.com/datafiles/{sdkKey}.json");
            _logger.LogInformation("Validating datafile for project {ProjectName} at URL: {DatafileUrl}", projectName, GetMaskedDatafileUrl(sdkKey));

            using var response = await _httpClient.GetAsync(datafileUrl, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync(cancellationToken);

                if (string.IsNullOrWhiteSpace(content) || !content.TrimStart().StartsWith('{'))
                {
                    const string errorMessage = "Datafile is empty or not valid JSON";
                    _logger.LogWarning("Project {ProjectName} datafile validation failed: {ErrorMessage}", projectName, errorMessage);
                    return new DatafileValidationResult(false, errorMessage);
                }

                _logger.LogDebug("Project {ProjectName} datafile is accessible and appears valid", projectName);
                return new DatafileValidationResult(true, null);
            }

            var httpErrorMessage = $"HTTP {(int)response.StatusCode} {response.ReasonPhrase}";
            _logger.LogWarning("Project {ProjectName} datafile validation failed with status: {ErrorMessage}", projectName, httpErrorMessage);
            return new DatafileValidationResult(false, httpErrorMessage);
        }
        catch (HttpRequestException ex)
        {
            var errorMessage = $"Network error: {ex.Message}";
            _logger.LogWarning(ex, "Project {ProjectName} datafile validation failed with network error", projectName);
            return new DatafileValidationResult(false, errorMessage);
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            const string errorMessage = "Request timeout";
            _logger.LogWarning(ex, "Project {ProjectName} datafile validation failed due to timeout", projectName);
            return new DatafileValidationResult(false, errorMessage);
        }
#pragma warning disable CA1031 // Do not catch general exception types
        catch (Exception ex)
#pragma warning restore CA1031 // Do not catch general exception types
        {
            var errorMessage = $"Unexpected error: {ex.Message}";
            _logger.LogError(ex, "Project {ProjectName} datafile validation failed with unexpected error", projectName);
            return new DatafileValidationResult(false, errorMessage);
        }
    }

    private static string GetMaskedDatafileUrl(string sdkKey)
    {
        if (string.IsNullOrEmpty(sdkKey) || sdkKey.Length < 8)
        {
            return "(invalid or short sdk key)";
        }

        return $"https://cdn.optimizely.com/datafiles/{sdkKey.Substring(0, 2)}...{sdkKey.Substring(sdkKey.Length - 2)}.json";
    }

    /// <summary>
    /// Result of datafile validation.
    /// </summary>
    /// <param name="IsValid">Whether the datafile validation was successful.</param>
    /// <param name="ErrorMessage">Error message if validation failed.</param>
    private readonly record struct DatafileValidationResult(bool IsValid, string ErrorMessage);
}
