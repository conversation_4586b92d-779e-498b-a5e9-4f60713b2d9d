using System.Net;

namespace TaskManagementService.Periodic.Tests;

public abstract class PeriodicTestBase
{
    protected static readonly Uri Endpoint =
#if DEBUG
    new(Environment.GetEnvironmentVariable("instance") ?? "https://services.dev.docusign.net");
#else
    new(Environment.GetEnvironmentVariable("instance") ?? "http://services.local:80");
#endif

    protected static readonly Uri HealthCheckEndpoint = new(Endpoint, "task-management-service/v1.0/health/");

    protected static async Task<bool> CheckAvailabilityAsync(Uri availabilityEndpoint)
    {
        using var client = new HttpClient();
        using var availability = new HttpRequestMessage(HttpMethod.Get, availabilityEndpoint);
        var availabilityResponse = await client.SendAsync(availability);
        return availabilityResponse.StatusCode == HttpStatusCode.OK;
    }
}
