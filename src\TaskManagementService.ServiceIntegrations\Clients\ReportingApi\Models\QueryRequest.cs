// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class QueryRequest : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The accountId property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? AccountId { get; set; }
#nullable restore
#else
        public string AccountId { get; set; }
#endif
        /// <summary>The aggregation property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Aggregation? Aggregation { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Aggregation Aggregation { get; set; }
#endif
        /// <summary>The count property</summary>
        public int? Count { get; set; }
        /// <summary>The endDate property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? EndDate { get; set; }
#nullable restore
#else
        public string EndDate { get; set; }
#endif
        /// <summary>The filters property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Filter? Filters { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Filter Filters { get; set; }
#endif
        /// <summary>The getTotalCount property</summary>
        public bool? GetTotalCount { get; set; }
        /// <summary>The getTotalCountOnly property</summary>
        public bool? GetTotalCountOnly { get; set; }
        /// <summary>The id property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Id { get; set; }
#nullable restore
#else
        public string Id { get; set; }
#endif
        /// <summary>The offset property</summary>
        public int? Offset { get; set; }
        /// <summary>The projections property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Projection>? Projections { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Projection> Projections { get; set; }
#endif
        /// <summary>The requestId property</summary>
        public Guid? RequestId { get; set; }
        /// <summary>The startDate property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? StartDate { get; set; }
#nullable restore
#else
        public string StartDate { get; set; }
#endif
        /// <summary>The type property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Type { get; set; }
#nullable restore
#else
        public string Type { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.QueryRequest"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.QueryRequest CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.QueryRequest();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "accountId", n => { AccountId = n.GetStringValue(); } },
                { "aggregation", n => { Aggregation = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Aggregation>(global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Aggregation.CreateFromDiscriminatorValue); } },
                { "count", n => { Count = n.GetIntValue(); } },
                { "endDate", n => { EndDate = n.GetStringValue(); } },
                { "filters", n => { Filters = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Filter>(global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Filter.CreateFromDiscriminatorValue); } },
                { "getTotalCount", n => { GetTotalCount = n.GetBoolValue(); } },
                { "getTotalCountOnly", n => { GetTotalCountOnly = n.GetBoolValue(); } },
                { "id", n => { Id = n.GetStringValue(); } },
                { "offset", n => { Offset = n.GetIntValue(); } },
                { "projections", n => { Projections = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Projection>(global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Projection.CreateFromDiscriminatorValue)?.AsList(); } },
                { "requestId", n => { RequestId = n.GetGuidValue(); } },
                { "startDate", n => { StartDate = n.GetStringValue(); } },
                { "type", n => { Type = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("accountId", AccountId);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Aggregation>("aggregation", Aggregation);
            writer.WriteIntValue("count", Count);
            writer.WriteStringValue("endDate", EndDate);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Filter>("filters", Filters);
            writer.WriteBoolValue("getTotalCount", GetTotalCount);
            writer.WriteBoolValue("getTotalCountOnly", GetTotalCountOnly);
            writer.WriteStringValue("id", Id);
            writer.WriteIntValue("offset", Offset);
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Projection>("projections", Projections);
            writer.WriteGuidValue("requestId", RequestId);
            writer.WriteStringValue("startDate", StartDate);
            writer.WriteStringValue("type", Type);
        }
    }
}
#pragma warning restore CS0618
