using TaskManagementService.Core.Models;

namespace TaskManagementService.Core.Interfaces;

public interface IClmTaskService
{
    public Task<TaskHistory> GetClmTaskHistoryAsync(Guid accountId, Guid taskId);
    public Task<List<string>> PostUnassignTaskAsync(Guid accountId, Guid taskId);
    public Task<List<string>> PostAssignTaskAsync(Guid accountId, Guid taskId, string assigneeId);
    public Task<List<TasksSourceCount>> GetTasksCountAsync(CancellationToken cancellationToken);
}
