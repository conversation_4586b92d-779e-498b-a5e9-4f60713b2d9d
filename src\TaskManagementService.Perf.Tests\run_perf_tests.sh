#!/usr/bin/env bash

serviceUrl=$1
testStage=$2

export SERVICE_URL=$serviceUrl
export TEST_STAGE=$testStage

echo "Will run the performance analysis against ${serviceUrl}"
echo "Test Stage: ${testStage}"

echo "Running performance analysis..."

jmeter -n -t Test/smoke.jmx -J_host=$1 -J_app=task-management-service -j /dev/stdout

testExitCode=$?

echo "Stopping proxy..."
(curl -sf -XPOST http://127.0.0.1:15020/quitquitquit) # Stop Istio Proxy
echo "Istio Proxy Quit Exitcode: $?"

echo "Performance analysis exit code: ${testExitCode}"
exit $testExitCode
