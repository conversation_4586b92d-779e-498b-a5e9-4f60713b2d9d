ARG SOURCE_REPO=docker.docusignhq.com
FROM $SOURCE_REPO/dotnet-runtime-8.0:latest

RUN apt-get update && \
    apt-get install curl -y && \
    rm -rf /var/lib/apt/lists/*

ARG DOTNET_CONFIGURATION=Release
ARG DOTNET_TARGET=net8.0
ARG DOTNET_RUNTIME=linux-x64
EXPOSE 5000
EXPOSE 5001

WORKDIR /app
COPY --chown=app bin/${DOTNET_CONFIGURATION}/${DOTNET_TARGET}/${DOTNET_RUNTIME}/publish/ /app
USER app

# Pass build information as env variables
ARG BUILD_NUMBER
ARG GIT_SHA
ARG GIT_BRANCH
ENV ServiceVersion__BuildNumber=$BUILD_NUMBER ServiceVersion__GitSha1=$GIT_SHA ServiceVersion__GitBranch=$GIT_BRANCH

ENTRYPOINT ["dotnet", "TaskManagementService.dll"]
