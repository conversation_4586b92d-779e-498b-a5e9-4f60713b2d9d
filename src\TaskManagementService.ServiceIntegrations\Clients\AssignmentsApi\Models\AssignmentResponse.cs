// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class AssignmentResponse : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The assignees property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeResponse>? Assignees { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeResponse> Assignees { get; set; }
#endif
        /// <summary>The assignerId property</summary>
        public Guid? AssignerId { get; set; }
        /// <summary>The assignmentId property</summary>
        public Guid? AssignmentId { get; set; }
        /// <summary>The contextId property</summary>
        public Guid? ContextId { get; set; }
        /// <summary>The createdDate property</summary>
        public DateTimeOffset? CreatedDate { get; set; }
        /// <summary>The description property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Description { get; set; }
#nullable restore
#else
        public string Description { get; set; }
#endif
        /// <summary>The dueDate property</summary>
        public DateTimeOffset? DueDate { get; set; }
        /// <summary>The expirationDate property</summary>
        public DateTimeOffset? ExpirationDate { get; set; }
        /// <summary>The historyItems property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse.AssignmentResponse_historyItems>? HistoryItems { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse.AssignmentResponse_historyItems> HistoryItems { get; set; }
#endif
        /// <summary>The metadata property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse_metadata? Metadata { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse_metadata Metadata { get; set; }
#endif
        /// <summary>The status property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Status { get; set; }
#nullable restore
#else
        public string Status { get; set; }
#endif
        /// <summary>The subContextId property</summary>
        public Guid? SubContextId { get; set; }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "assignees", n => { Assignees = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeResponse>(global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeResponse.CreateFromDiscriminatorValue)?.AsList(); } },
                { "assignerId", n => { AssignerId = n.GetGuidValue(); } },
                { "assignmentId", n => { AssignmentId = n.GetGuidValue(); } },
                { "contextId", n => { ContextId = n.GetGuidValue(); } },
                { "createdDate", n => { CreatedDate = n.GetDateTimeOffsetValue(); } },
                { "description", n => { Description = n.GetStringValue(); } },
                { "dueDate", n => { DueDate = n.GetDateTimeOffsetValue(); } },
                { "expirationDate", n => { ExpirationDate = n.GetDateTimeOffsetValue(); } },
                { "historyItems", n => { HistoryItems = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse.AssignmentResponse_historyItems>(global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse.AssignmentResponse_historyItems.CreateFromDiscriminatorValue)?.AsList(); } },
                { "metadata", n => { Metadata = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse_metadata>(global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse_metadata.CreateFromDiscriminatorValue); } },
                { "status", n => { Status = n.GetStringValue(); } },
                { "subContextId", n => { SubContextId = n.GetGuidValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeResponse>("assignees", Assignees);
            writer.WriteGuidValue("assignerId", AssignerId);
            writer.WriteGuidValue("assignmentId", AssignmentId);
            writer.WriteGuidValue("contextId", ContextId);
            writer.WriteDateTimeOffsetValue("createdDate", CreatedDate);
            writer.WriteStringValue("description", Description);
            writer.WriteDateTimeOffsetValue("dueDate", DueDate);
            writer.WriteDateTimeOffsetValue("expirationDate", ExpirationDate);
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse.AssignmentResponse_historyItems>("historyItems", HistoryItems);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse_metadata>("metadata", Metadata);
            writer.WriteStringValue("status", Status);
            writer.WriteGuidValue("subContextId", SubContextId);
        }
        /// <summary>
        /// Composed type wrapper for classes <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeActionUpdatedHistoryEvent"/>, <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeAddedHistoryEvent"/>, <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeRemovedHistoryEvent"/>, <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeReplacedHistoryEvent"/>, <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCancelledHistoryEvent"/>, <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCompletedHistoryEvent"/>, <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCreatedHistoryEvent"/>, <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentExpiredHistoryEvent"/>, <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentPropertiesUpdatedHistoryEvent"/>, <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.StageAddedHistoryEvent"/>
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class AssignmentResponse_historyItems : IComposedTypeWrapper, IParsable
        {
            /// <summary>Composed type representation for type <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeActionUpdatedHistoryEvent"/></summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeActionUpdatedHistoryEvent? AssigneeActionUpdatedHistoryEvent { get; set; }
#nullable restore
#else
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeActionUpdatedHistoryEvent AssigneeActionUpdatedHistoryEvent { get; set; }
#endif
            /// <summary>Composed type representation for type <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeAddedHistoryEvent"/></summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeAddedHistoryEvent? AssigneeAddedHistoryEvent { get; set; }
#nullable restore
#else
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeAddedHistoryEvent AssigneeAddedHistoryEvent { get; set; }
#endif
            /// <summary>Composed type representation for type <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeRemovedHistoryEvent"/></summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeRemovedHistoryEvent? AssigneeRemovedHistoryEvent { get; set; }
#nullable restore
#else
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeRemovedHistoryEvent AssigneeRemovedHistoryEvent { get; set; }
#endif
            /// <summary>Composed type representation for type <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeReplacedHistoryEvent"/></summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeReplacedHistoryEvent? AssigneeReplacedHistoryEvent { get; set; }
#nullable restore
#else
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeReplacedHistoryEvent AssigneeReplacedHistoryEvent { get; set; }
#endif
            /// <summary>Composed type representation for type <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCancelledHistoryEvent"/></summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCancelledHistoryEvent? AssignmentCancelledHistoryEvent { get; set; }
#nullable restore
#else
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCancelledHistoryEvent AssignmentCancelledHistoryEvent { get; set; }
#endif
            /// <summary>Composed type representation for type <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCompletedHistoryEvent"/></summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCompletedHistoryEvent? AssignmentCompletedHistoryEvent { get; set; }
#nullable restore
#else
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCompletedHistoryEvent AssignmentCompletedHistoryEvent { get; set; }
#endif
            /// <summary>Composed type representation for type <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCreatedHistoryEvent"/></summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCreatedHistoryEvent? AssignmentCreatedHistoryEvent { get; set; }
#nullable restore
#else
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCreatedHistoryEvent AssignmentCreatedHistoryEvent { get; set; }
#endif
            /// <summary>Composed type representation for type <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentExpiredHistoryEvent"/></summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentExpiredHistoryEvent? AssignmentExpiredHistoryEvent { get; set; }
#nullable restore
#else
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentExpiredHistoryEvent AssignmentExpiredHistoryEvent { get; set; }
#endif
            /// <summary>Composed type representation for type <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentPropertiesUpdatedHistoryEvent"/></summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentPropertiesUpdatedHistoryEvent? AssignmentPropertiesUpdatedHistoryEvent { get; set; }
#nullable restore
#else
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentPropertiesUpdatedHistoryEvent AssignmentPropertiesUpdatedHistoryEvent { get; set; }
#endif
            /// <summary>Composed type representation for type <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.StageAddedHistoryEvent"/></summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.StageAddedHistoryEvent? StageAddedHistoryEvent { get; set; }
#nullable restore
#else
            public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.StageAddedHistoryEvent StageAddedHistoryEvent { get; set; }
#endif
            /// <summary>
            /// Creates a new instance of the appropriate class based on discriminator value
            /// </summary>
            /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse.AssignmentResponse_historyItems"/></returns>
            /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
            public static global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse.AssignmentResponse_historyItems CreateFromDiscriminatorValue(IParseNode parseNode)
            {
                _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
                var mappingValue = parseNode.GetChildNode("eventType")?.GetStringValue();
                var result = new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponse.AssignmentResponse_historyItems();
                if("AssigneeActionUpdatedHistoryEvent".Equals(mappingValue, StringComparison.OrdinalIgnoreCase))
                {
                    result.AssigneeActionUpdatedHistoryEvent = new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeActionUpdatedHistoryEvent();
                }
                else if("AssigneeAddedHistoryEvent".Equals(mappingValue, StringComparison.OrdinalIgnoreCase))
                {
                    result.AssigneeAddedHistoryEvent = new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeAddedHistoryEvent();
                }
                else if("AssigneeRemovedHistoryEvent".Equals(mappingValue, StringComparison.OrdinalIgnoreCase))
                {
                    result.AssigneeRemovedHistoryEvent = new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeRemovedHistoryEvent();
                }
                else if("AssigneeReplacedHistoryEvent".Equals(mappingValue, StringComparison.OrdinalIgnoreCase))
                {
                    result.AssigneeReplacedHistoryEvent = new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeReplacedHistoryEvent();
                }
                else if("AssignmentCancelledHistoryEvent".Equals(mappingValue, StringComparison.OrdinalIgnoreCase))
                {
                    result.AssignmentCancelledHistoryEvent = new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCancelledHistoryEvent();
                }
                else if("AssignmentCompletedHistoryEvent".Equals(mappingValue, StringComparison.OrdinalIgnoreCase))
                {
                    result.AssignmentCompletedHistoryEvent = new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCompletedHistoryEvent();
                }
                else if("AssignmentCreatedHistoryEvent".Equals(mappingValue, StringComparison.OrdinalIgnoreCase))
                {
                    result.AssignmentCreatedHistoryEvent = new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCreatedHistoryEvent();
                }
                else if("AssignmentExpiredHistoryEvent".Equals(mappingValue, StringComparison.OrdinalIgnoreCase))
                {
                    result.AssignmentExpiredHistoryEvent = new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentExpiredHistoryEvent();
                }
                else if("AssignmentPropertiesUpdatedHistoryEvent".Equals(mappingValue, StringComparison.OrdinalIgnoreCase))
                {
                    result.AssignmentPropertiesUpdatedHistoryEvent = new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentPropertiesUpdatedHistoryEvent();
                }
                else if("StageAddedHistoryEvent".Equals(mappingValue, StringComparison.OrdinalIgnoreCase))
                {
                    result.StageAddedHistoryEvent = new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.StageAddedHistoryEvent();
                }
                return result;
            }
            /// <summary>
            /// The deserialization information for the current model
            /// </summary>
            /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
            public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
            {
                if(AssigneeActionUpdatedHistoryEvent != null)
                {
                    return AssigneeActionUpdatedHistoryEvent.GetFieldDeserializers();
                }
                else if(AssigneeAddedHistoryEvent != null)
                {
                    return AssigneeAddedHistoryEvent.GetFieldDeserializers();
                }
                else if(AssigneeRemovedHistoryEvent != null)
                {
                    return AssigneeRemovedHistoryEvent.GetFieldDeserializers();
                }
                else if(AssigneeReplacedHistoryEvent != null)
                {
                    return AssigneeReplacedHistoryEvent.GetFieldDeserializers();
                }
                else if(AssignmentCancelledHistoryEvent != null)
                {
                    return AssignmentCancelledHistoryEvent.GetFieldDeserializers();
                }
                else if(AssignmentCompletedHistoryEvent != null)
                {
                    return AssignmentCompletedHistoryEvent.GetFieldDeserializers();
                }
                else if(AssignmentCreatedHistoryEvent != null)
                {
                    return AssignmentCreatedHistoryEvent.GetFieldDeserializers();
                }
                else if(AssignmentExpiredHistoryEvent != null)
                {
                    return AssignmentExpiredHistoryEvent.GetFieldDeserializers();
                }
                else if(AssignmentPropertiesUpdatedHistoryEvent != null)
                {
                    return AssignmentPropertiesUpdatedHistoryEvent.GetFieldDeserializers();
                }
                else if(StageAddedHistoryEvent != null)
                {
                    return StageAddedHistoryEvent.GetFieldDeserializers();
                }
                return new Dictionary<string, Action<IParseNode>>();
            }
            /// <summary>
            /// Serializes information the current object
            /// </summary>
            /// <param name="writer">Serialization writer to use to serialize this model</param>
            public virtual void Serialize(ISerializationWriter writer)
            {
                _ = writer ?? throw new ArgumentNullException(nameof(writer));
                if(AssigneeActionUpdatedHistoryEvent != null)
                {
                    writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeActionUpdatedHistoryEvent>(null, AssigneeActionUpdatedHistoryEvent);
                }
                else if(AssigneeAddedHistoryEvent != null)
                {
                    writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeAddedHistoryEvent>(null, AssigneeAddedHistoryEvent);
                }
                else if(AssigneeRemovedHistoryEvent != null)
                {
                    writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeRemovedHistoryEvent>(null, AssigneeRemovedHistoryEvent);
                }
                else if(AssigneeReplacedHistoryEvent != null)
                {
                    writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeReplacedHistoryEvent>(null, AssigneeReplacedHistoryEvent);
                }
                else if(AssignmentCancelledHistoryEvent != null)
                {
                    writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCancelledHistoryEvent>(null, AssignmentCancelledHistoryEvent);
                }
                else if(AssignmentCompletedHistoryEvent != null)
                {
                    writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCompletedHistoryEvent>(null, AssignmentCompletedHistoryEvent);
                }
                else if(AssignmentCreatedHistoryEvent != null)
                {
                    writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCreatedHistoryEvent>(null, AssignmentCreatedHistoryEvent);
                }
                else if(AssignmentExpiredHistoryEvent != null)
                {
                    writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentExpiredHistoryEvent>(null, AssignmentExpiredHistoryEvent);
                }
                else if(AssignmentPropertiesUpdatedHistoryEvent != null)
                {
                    writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentPropertiesUpdatedHistoryEvent>(null, AssignmentPropertiesUpdatedHistoryEvent);
                }
                else if(StageAddedHistoryEvent != null)
                {
                    writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.StageAddedHistoryEvent>(null, StageAddedHistoryEvent);
                }
            }
        }
    }
}
#pragma warning restore CS0618
