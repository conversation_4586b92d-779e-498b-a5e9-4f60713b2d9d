﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Kiota.Abstractions;

using TaskManagementService.Core.Exceptions;

namespace TaskManagementService.Middleware;

public class ExceptionHandlerMiddleware(RequestDelegate next, ILogger<ExceptionHandlerMiddleware> logger)
{
    public async Task InvokeAsync(HttpContext httpContext)
    {
        try
        {
            await next(httpContext);
        }
        catch (TaskManagementServiceException ex)
        {
            logger.LogError(ex, "An error occurred while processing request. Message {Message}", ex.ErrorDetails?.DeveloperMessage);
            await WriteErrorResponseAsync(httpContext, ex.ErrorDetails);
        }
        catch (ValidationException ex)
        {
            logger.LogError(ex, "Bad Request");

            await WriteErrorResponseAsync(httpContext, new ErrorDetails
            {
                UserMessage = ex.Message,
                HttpStatusCode = StatusCodes.Status400BadRequest,
            });
        }
        catch (NotSupportedException ex)
        {
            logger.LogError(ex, "Bad Request");

            await WriteErrorResponseAsync(httpContext, new ErrorDetails
            {
                UserMessage = ex.Message,
                HttpStatusCode = StatusCodes.Status400BadRequest,
            });
        }
        catch (ApiException ex)
        {
            logger.LogError(ex, "ApiException");

            await WriteErrorResponseAsync(httpContext, new ErrorDetails
            {
                HttpStatusCode = ex.ResponseStatusCode,
                UserMessage = "An error occurred while processing your request.",
            });
        }
#pragma warning disable CA1031 // This is a global exception handler, catching all exceptions is required to prevent unhandled exceptions and to provide a consistent error response.
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Unhandled exception");
            await WriteErrorResponseAsync(httpContext, new ErrorDetails
            {
                UserMessage = "An error occurred while processing your request.",
                HttpStatusCode = StatusCodes.Status500InternalServerError,
            });
#pragma warning restore CA1031
        }
    }

    private static Task WriteErrorResponseAsync(HttpContext httpContext, ErrorDetails errorDetails)
    {
        ArgumentNullException.ThrowIfNull(httpContext);

        httpContext.Response.ContentType = "application/json";
        httpContext.Response.StatusCode = errorDetails.HttpStatusCode ?? StatusCodes.Status500InternalServerError;

        errorDetails.Extensions.Add("ReferenceId", httpContext.TraceIdentifier);
        return httpContext.Response.WriteAsync(JsonSerializer.Serialize(errorDetails));
    }
}
