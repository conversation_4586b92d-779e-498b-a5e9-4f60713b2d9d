﻿#pragma warning disable SA1402
#pragma warning disable SA1134
#pragma warning disable CS8618
#pragma warning disable CA2227
#pragma warning disable CA1002

using System.Text.Json.Serialization;

using TaskManagementService.Infrastructure.Serialization;

// ReSharper disable UnusedMember.Global
namespace TaskManagementService.Infrastructure.ServiceIntegrations.EnvelopeApi.Models;

public partial class ESignTaskReportApiDto
{
    [JsonPropertyName("awaitingMySignatureCount")][JsonConverter(typeof(ParseStringConverter))] public long AwaitingMySignatureCount { get; set; }
    [JsonPropertyName("completedEnvelopeCount")][JsonConverter(typeof(ParseStringConverter))] public long CompletedEnvelopeCount { get; set; }
    [JsonPropertyName("expiringSoonCount")][JsonConverter(typeof(ParseStringConverter))] public long ExpiringSoonCount { get; set; }
    [Json<PERSON>ropertyName("waitingForOthersCount")][Json<PERSON>onverter(typeof(ParseStringConverter))] public long WaitingForOthersCount { get; set; }
}
