using System;

using Moq;

using TaskManagementService.Core.Interfaces;
using TaskManagementService.Optimizely;

using Xunit;

namespace TaskManagementService.Tests.Optimizely;

[Trait("TestType", "UnitTest")]
public class UserContextProviderTests
{
    private readonly Mock<IRequestContextService> _mockRequestContextService;
    private readonly UserContextProvider _sut;

    public UserContextProviderTests()
    {
        _mockRequestContextService = new Mock<IRequestContextService>();
        _sut = new UserContextProvider(_mockRequestContextService.Object);
    }

    [Fact]
    public void ConstructorWithNullRequestContextServiceThrowsArgumentNullException()
    {
        // Act & Assert
        var ex = Assert.Throws<ArgumentNullException>(() => new UserContextProvider(null!));
        Assert.Equal("requestContextService", ex.ParamName);
    }

    [Fact]
    public void UserIdReturnsUserIdFromContext()
    {
        // Arrange
        var userId = Guid.NewGuid();
        _mockRequestContextService.Setup(x => x.UserId).Returns(userId);

        // Act
        var result = _sut.UserId;

        // Assert
        Assert.Equal(userId.ToString(), result);
    }

    [Fact]
    public void AccountIdReturnsAccountIdFromContext()
    {
        // Arrange
        var accountId = Guid.NewGuid();
        _mockRequestContextService.Setup(x => x.AccountId).Returns(accountId);

        // Act
        var result = _sut.AccountId;

        // Assert
        Assert.Equal(accountId.ToString(), result);
    }
}
