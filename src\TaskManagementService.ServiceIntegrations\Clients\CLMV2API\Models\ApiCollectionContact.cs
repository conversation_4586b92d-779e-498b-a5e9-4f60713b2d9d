// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class ApiCollectionContact : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The First property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? First { get; set; }
#nullable restore
#else
        public string First { get; set; }
#endif
        /// <summary>The Href property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Href { get; set; }
#nullable restore
#else
        public string Href { get; set; }
#endif
        /// <summary>The Items property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Contact>? Items { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Contact> Items { get; set; }
#endif
        /// <summary>The Last property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Last { get; set; }
#nullable restore
#else
        public string Last { get; set; }
#endif
        /// <summary>The Limit property</summary>
        public int? Limit { get; set; }
        /// <summary>The Next property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Next { get; set; }
#nullable restore
#else
        public string Next { get; set; }
#endif
        /// <summary>The Offset property</summary>
        public int? Offset { get; set; }
        /// <summary>The Previous property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Previous { get; set; }
#nullable restore
#else
        public string Previous { get; set; }
#endif
        /// <summary>The Total property</summary>
        public int? Total { get; set; }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionContact"/> and sets the default values.
        /// </summary>
        public ApiCollectionContact()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionContact"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionContact CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionContact();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "First", n => { First = n.GetStringValue(); } },
                { "Href", n => { Href = n.GetStringValue(); } },
                { "Items", n => { Items = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Contact>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Contact.CreateFromDiscriminatorValue)?.AsList(); } },
                { "Last", n => { Last = n.GetStringValue(); } },
                { "Limit", n => { Limit = n.GetIntValue(); } },
                { "Next", n => { Next = n.GetStringValue(); } },
                { "Offset", n => { Offset = n.GetIntValue(); } },
                { "Previous", n => { Previous = n.GetStringValue(); } },
                { "Total", n => { Total = n.GetIntValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("First", First);
            writer.WriteStringValue("Href", Href);
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Contact>("Items", Items);
            writer.WriteStringValue("Last", Last);
            writer.WriteIntValue("Limit", Limit);
            writer.WriteStringValue("Next", Next);
            writer.WriteIntValue("Offset", Offset);
            writer.WriteStringValue("Previous", Previous);
            writer.WriteIntValue("Total", Total);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
