﻿using System.Text.Json.Serialization;

namespace TaskManagementService.Core.Enums;

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum TaskType
{
    NeedsToSign,
    InPersonSigner,
    NeedsToView,
    SpecifyRecipients,
    UpdateRecipients,
    AllowToEdit,
    Approve,
    Choice,
    ChooseDocuments,
    ChooseUsers,
    CreateOrUploadDoc,
    EditDocument,
    EditForm,
    FillForm,
    FullPageEditForm,
    FullPageFillForm,
    ReviewData,
    ReviewAndSendForExternalReview,
    ReviewAndSendForSignature,
    ResolveComments,
    Routing,
    ObligationTask,
    VerificationFailed,
    ReviewId,
    ReconcileData,
    ReceivesACopy
}
