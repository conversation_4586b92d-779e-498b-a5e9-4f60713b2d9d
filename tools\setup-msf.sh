#!/bin/bash

SCRIPT_DIR="$(cd -- "$(dirname -- "${BASH_SOURCE[0]}")" &>/dev/null && pwd)"

pushd $SCRIPT_DIR/..

usage() {
    echo "Usage: $0 [-h] [-p <profile-name>] [-n <namespace1>] [-n <namespace2>]... [-d]" 1>&2
    echo 1>&2
    echo -e "\t-d\t\tshow debug output" 1>&2
    echo 1>&2
    exit 1
}

SETUP_ARGS=("-n task-management")
PROFILE_NAME="minikube"
CONTAINER_RUNTIME="docker"

while getopts "hp:n:da" flag; do
    case "${flag}" in
    p) PROFILE_NAME=${OPTARG} ;;
    n) SETUP_ARGS+=("-n " ${OPTARG}) ;;
    d) SETUP_ARGS+=("-d") ;;
    *) usage ;;
    esac
done

echo "📌 Locating msf-dev..."
if ! command -v minikube-start.sh &>/dev/null; then
    MSF_DEV_DIR="$(cd -- "$SCRIPT_DIR/../../msf-dev/minikube/" &>/dev/null && pwd)"

    echo "🚜 Trying $MSF_DEV_DIR..."
    export PATH=$PATH:$MSF_DEV_DIR
    if ! command -v minikube-start.sh &>/dev/null; then
        echo "❌ Unable to find msf-dev/minikube, please add it to PATH."
        exit 1
    fi
fi

echo "📌 Locating msf-dev/dotnet..."
if ! command -v msf-dotnet-setup.sh &>/dev/null; then
    MSF_DOTNET_DIR="$(cd -- "$(dirname -- "$(command -v minikube-start.sh)")/../dotnet" &>/dev/null && pwd)"

    echo "🚜 Trying $MSF_DOTNET_DIR..."
    export PATH=$PATH:$MSF_DOTNET_DIR
    if ! command -v msf-dotnet-setup.sh &>/dev/null; then
        echo "❌ Unable to find msf-dev/dotnet, please add it to PATH."
        exit 1
    fi
fi

minikube-sync-msf.sh

msf-dotnet-setup.sh
if (($? != 0)); then
    exit 1
fi

echo "📌 Installing msf..."
minikube-start.sh -p $PROFILE_NAME -r $CONTAINER_RUNTIME ${START_ARGS[*]}
if (($? != 0)); then
    exit 1
fi

minikube-setup.sh -p $PROFILE_NAME ${SETUP_ARGS[*]}

if (($? != 0)); then
    exit 1
fi

minikube-tunnel.sh -p $PROFILE_NAME

if (($? != 0)); then
    exit 1
fi

. minikube-docker-env.sh -p $PROFILE_NAME

CURRENT_CONTEXT=$(kubectl config current-context)
if [ "$CURRENT_CONTEXT" != "$PROFILE_NAME" ]; then
    echo "📌 use $PROFILE_NAME profile..."
    kubectl config use $PROFILE_NAME
    if (($? != 0)); then
        echo "❌ Unable to select $PROFILE_NAME context."
        exit 1
    fi
fi

echo "📌 Verifying secrets..."
SECRET_NAMES=()
for SECRET_NAME in "${SECRET_NAMES[@]}"; do
    echo "🔒 Checking $SECRET_NAME..."
    kubectl get secret -n task-management $SECRET_NAME &>/dev/null
    if (($? != 0)); then
        echo "❌ Unable find $SECRET_NAME secret, please follow up with the team and try again."
        exit 1
    fi
done

echo "📌 Deploying server..."
skaffold run

if (($? != 0)); then
    echo "❌ skaffold run failed."
    exit 1
fi

# For local development, public API endpoint is https://services.local/{service}/v1.0/health or http://services.local/{service}/v1.0/health
# private API endpoint is https://internal-services.local:444/{service}/v1.0/health or http://internal-services.local:81/{service}/v1.0/health
DEPLOYED_URL=https://services.local/task-management-service/v1.0/health

echo "📌 Opening server info..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    open $DEPLOYED_URL
elif grep -qi microsoft /proc/version; then
    wslview $DEPLOYED_URL
    echo "❗ NOTE: you need to run \".\minikube\minikube-tunnel.ps1 from msf-dev to configure DNS & SSL in host."
fi

echo "✅ Mikikube configured and service deployed successfully."
