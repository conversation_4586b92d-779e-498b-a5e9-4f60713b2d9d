﻿using System.ComponentModel.DataAnnotations;
using System.Globalization;

using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Authentication;
using Microsoft.Kiota.Http.HttpClientLibrary;

using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Infrastructure.Resources;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Workflowqueues;

using TaskGroup = TaskManagementService.Core.Models.TaskGroup;
using TaskGroupMember = TaskManagementService.Core.Models.TaskGroupMember;

namespace TaskManagementService.Infrastructure.Services;

public class ClmTaskService(ILogger<ClmTaskService> logger, IAuthenticationProvider authenticationProvider, HttpClient httpClient, IRequestContextService requestContextService, CLMV2APIClient? clmV2ApiClient = null) : ITaskService, IClmTaskService
{
    public CLMV2APIClient? GetCLMV2APIClient()
    {
        if (clmV2ApiClient != null)
        {
            return clmV2ApiClient;
        }

        if (!requestContextService.IsClmAccount || requestContextService.ClmApiBaseUrl == null)
        {
            return null;
        }

        using var requestAdapter = new HttpClientRequestAdapter(authenticationProvider, httpClient: httpClient);
        requestAdapter.BaseUrl = requestContextService.ClmApiBaseUrl.ToString();
        return new CLMV2APIClient(requestAdapter);
    }

    private static bool IsClmTaskAssigneeFilterNullOrEmpty(TaskFilter? taskFilter)
    {
        if (taskFilter == null)
        {
            return true;
        }
        else
        {
            return taskFilter.GroupAssignees.Count == 0 && taskFilter.AssignedUserIds.Count == 0;
        }
    }

    public async Task<List<UserTask>> GetTasksAsync(Guid accountId, TaskFilter? taskFilter)
    {
        var clmClient = GetCLMV2APIClient();
        if (clmClient == null)
        {
            return [];
        }

        var taskGroupIds = new HashSet<Guid>();
        var taskGroupAssignees = (taskFilter?.GroupAssignees ?? []).ToHashSet();
        var taskGroupMembers = new Dictionary<Guid, string>();
        if (taskFilter == null || IsClmTaskAssigneeFilterNullOrEmpty(taskFilter))
        {
            var taskGroupsList = await GetTasksGroupsAsync(accountId, includeMembers: false);
            foreach (var taskGroup in taskGroupsList)
            {
                if (Guid.TryParse(taskGroup.Id, out var groupId))
                {
                    _ = taskGroupIds.Add(groupId);
                }
            }
        }
        else
        {
            foreach (var taskFilterGroupAssignee in taskFilter.GroupAssignees ?? [])
            {
                if (Guid.TryParse(taskFilterGroupAssignee, out var groupId))
                {
                    _ = taskGroupIds.Add(groupId);
                }
            }
        }

        try
        {
            var assignedClmUserIds = taskFilter?.AssignedUserIds.Count == 0
                ? null
                : taskFilter?.AssignedUserIds.Where(id => id != requestContextService.UserId).Select(id => (Guid?)id).ToList();

            var taskSummaryRequestBody = new TaskSummaryRequestBody
            {
                OnlyUnassigned = taskFilter?.OnlyUnassigned,
                TaskGroupIds = taskGroupIds.Count == 0 ? null : taskGroupIds.Select(x => (Guid?)x).ToList(),
                AssignedUserUids = assignedClmUserIds,
                PageSize = taskFilter?.TaskSort?.ResultCount ?? 500,
                SortColumn = ConvertSortColumnToString(taskFilter?.TaskSort?.SortColumn)
            };

            var workItems = await clmClient.V2[accountId.ToString()].Members.Current.Workitems.Summary.PostAsync(taskSummaryRequestBody);
            if (workItems?.WorkItems?.Count > 0)
            {
                IEnumerable<TaskSummaryWorkItem> workItemList = workItems.WorkItems;
                if (taskGroupAssignees.Count > 0)
                {
                    workItemList = workItemList.Where(x =>
                        x.WorkflowQueueId != null && x.WorkflowQueueId.Value != Guid.Empty &&
                        taskGroupAssignees.Contains(x.WorkflowQueueId.Value.ToString()));
                }

                if (taskFilter?.AssignedUserIds.Count > 0)
                {
                    workItemList = workItemList.Where(x => x.AssigneeId != null && taskFilter.AssignedUserIds.Select(id => (Guid?)id).ToList().Contains(x.AssigneeId));
                }

                var taskGroups = new Dictionary<Guid, TaskSummaryTaskGroup>();
                foreach (var workflowQueue in workItems.WorkflowQueues ?? [])
                {
                    if (workflowQueue is { Id: not null })
                    {
                        _ = taskGroups.TryAdd(workflowQueue.Id.Value, workflowQueue);
                        if (workflowQueue.Members != null && workflowQueue.Members.Count > 0)
                        {
                            foreach (var item in workflowQueue.Members)
                            {
                                if (item.Id != null && item.Id.HasValue && !taskGroupMembers.ContainsKey(item.Id.Value) && item.FullName != null)
                                {
                                    taskGroupMembers.Add(item.Id.Value, item.FullName);
                                }
                            }
                        }
                    }
                }

                return workItemList
                    .Select(wi => GetUserTask(wi, taskGroups, taskGroupMembers))
                    .Where(t => taskFilter?.ExtraClmTaskFilter(t) ?? true).ToList();
            }
        }
        catch (ApiException e)
        {
            logger.LogError(e, "CLM V2 API Exception");
        }

        return [];
    }

    public async Task<List<TaskGroup>> GetTasksGroupsAsync(Guid accountId, bool includeMembers)
    {
        var clmClient = GetCLMV2APIClient();
        if (clmClient == null)
        {
            return [];
        }

        try
        {
            var workGroups = await clmClient.V2[accountId.ToString()].Members.Current.Workflowqueues.GetAsync(req =>
            {
                req.QueryParameters = new WorkflowqueuesRequestBuilder.WorkflowqueuesRequestBuilderGetQueryParameters
                {
                    PageSortParamsLimit = 500,
                };
            });

            List<TaskSummaryTaskGroup>? groupsWithMembers = null;
            if (includeMembers)
            {
                var groupIds = workGroups?.Items?.Select(group =>
                {
                    var idString = group.Href?.Split("/").Last() ?? string.Empty;
                    if (Guid.TryParse(idString, out var id))
                    {
                        return (Guid?)id;
                    }

                    return null;
                }).ToList();
                if (groupIds != null)
                {
                    var postWorkflowMembersRequestBody = new PostWorkflowMembersRequestBody { TaskGroupIds = groupIds };
                    groupsWithMembers = (await clmClient.V2[accountId.ToString()].Members.Current.Workflowqueues.Members.PostAsync(postWorkflowMembersRequestBody))?.Items;
                }
            }

            if (workGroups?.Items?.Count > 0)
            {
                return workGroups.Items.Select(group =>
                {
                    var id = group.Href?.Split("/").Last() ?? string.Empty;
                    var members = groupsWithMembers?.FirstOrDefault(g => g.Id.ToString() == id)?.Members?.Select(m => new TaskGroupMember
                    {
                        Id = m?.Id ?? null,
                        FullName = m?.FullName ?? string.Empty,
                        Email = m?.Email ?? string.Empty
                    });
                    return new TaskGroup
                    {
                        Id = group.Href?.Split("/").Last() ?? string.Empty,
                        Name = group.Name ?? string.Empty,
                        Source = TaskSource.ClmWorkflow,
                        Members = members ?? [],
                    };
                }).ToList();
            }
        }
        catch (ApiException e)
        {
            logger.LogError(e, "CLM V2 API Exception");
        }

        return [];
    }

    public async Task<List<string>> PostUnassignTaskAsync(Guid accountId, Guid taskId)
    {
        var clmClient = GetCLMV2APIClient();
        if (clmClient == null)
        {
            logger.LogError("CLM V2 API Exception, unable to create CLM client");
            throw new ValidationException("CLM V2 API Exception, unable to create CLM client");
        }

        var workItem = new WorkItem
        {
            Assignee = new User
            {
                Href = $"{requestContextService.ClmApiBaseUrl}v2/{accountId}/members/********-0000-0000-0000-************"
            }
        };

        try
        {
            var updatedWorkItem = await clmClient.V2[accountId.ToString()].Workitems[taskId].Unassign.PatchAsync(workItem);

            if (updatedWorkItem?.Assignee?.Href != null)
            {
                if (updatedWorkItem.Assignee.Href != workItem.Assignee.Href)
                {
                    logger.LogError("CLM V2 API Exception, validation failed, new workitem member does not match requested member");
                    throw new ValidationException("Href of returned workitem must match requested href");
                }

                return [];
            }

            logger.LogError("CLM V2 API Exception, validation failed, new workitem member is null");
            throw new ValidationException("Href of returned workitem must not be null");
        }
        catch (ApiException e)
        {
            if (e.ResponseStatusCode == 422)
            {
                logger.LogError(e, "CLM V2 API Exception, could not unassign member from workitem due to failing validation");
            }

            logger.LogError(e, "CLM V2 API Exception, could not unassign member from workitem due to server error");
            throw;
        }
    }

    public async Task<List<string>> PostAssignTaskAsync(Guid accountId, Guid taskId, string assigneeId)
    {
        var clmClient = GetCLMV2APIClient();
        if (clmClient == null)
        {
            logger.LogError("CLM V2 API Exception, unable to create CLM client");
            throw new ValidationException("CLM V2 API Exception, unable to create CLM client");
        }

        var workItem = new WorkItem
        {
            Assignee = new User
            {
                Href = $"{requestContextService.ClmApiBaseUrl}v2/{accountId}/members/{assigneeId}"
            }
        };

        try
        {
            var updatedWorkItem = await clmClient.V2[accountId.ToString()].Workitems[taskId].Assign.PatchAsync(workItem);

            if (updatedWorkItem?.Assignee?.Href != null)
            {
                if (updatedWorkItem.Assignee.Href != workItem.Assignee.Href)
                {
                    logger.LogError("CLM V2 API Exception, validation failed, new workitem member does not match requested member");
                    throw new ValidationException("Href of returned workitem must match requested href");
                }

                return [];
            }

            logger.LogError("CLM V2 API Exception, validation failed, new workitem member is null");
            throw new ValidationException("Href of returned workitem must not be null");
        }
        catch (ApiException e)
        {
            if (e.ResponseStatusCode == 422)
            {
                logger.LogError(e, "CLM V2 API Exception, could not assign member to workitem due to failing validation");
            }

            logger.LogError(e, "CLM V2 API Exception, could not assign member to workitem due to server error");
            throw;
        }
    }

    public async Task<TaskHistory> GetClmTaskHistoryAsync(Guid accountId, Guid taskId)
    {
        var clmClient = GetCLMV2APIClient();
        if (clmClient == null || taskId == Guid.Empty)
        {
            return new TaskHistory();
        }

        try
        {
            var activityHistoryEvents = await clmClient.V2[accountId.ToString()].Workitems[taskId].History.GetAsync();
            if (activityHistoryEvents?.HistoryEvents?.Count > 0)
            {
                var sortedHistoryEvents = activityHistoryEvents.HistoryEvents.OrderByDescending(x => x.LastUpdateDate);
                var historyEvents = sortedHistoryEvents.Select(activityHistoryEvent => new TaskHistoryAuditEvent
                {
                    AssignedUser = activityHistoryEvent.AssignedUser ?? string.Empty,
                    Title = activityHistoryEvent.Title ?? string.Empty,
                    Message = activityHistoryEvent.Description ?? string.Empty,
                    CreatedDate = activityHistoryEvent.CreatedDate != null ? GetIsoString(activityHistoryEvent.CreatedDate) : string.Empty,
                    DueDate = activityHistoryEvent.DueDate != null && activityHistoryEvent.DueDate.Value != DateTimeOffset.MinValue ? GetIsoString(activityHistoryEvent.DueDate) : string.Empty,
                }).ToList();

                return new TaskHistory
                {
                    TaskId = taskId.ToString(),
                    Source = TaskSource.ClmWorkflow,
                    AuditEvents = historyEvents,
                };
            }
        }
        catch (ApiException e)
        {
            logger.LogError(e, "CLM V2 API Exception");
        }

        return new TaskHistory();
    }

    public async Task<List<TasksSourceCount>> GetTasksCountAsync(CancellationToken cancellationToken = default)
    {
        var tasks = await GetTasksAsync(requestContextService.AccountId, null);
        return [new TasksSourceCount(TaskSource.AssignmentsApi, tasks.FindAll(x => x.Source == TaskSource.AssignmentsApi).Count),
            new TasksSourceCount(TaskSource.ClmWorkflow, tasks.FindAll(x => x.Source == TaskSource.ClmWorkflow).Count)];
    }

    private static UserTask GetUserTask(TaskSummaryWorkItem workItem, Dictionary<Guid, TaskSummaryTaskGroup> taskGroups, Dictionary<Guid, string> assigneeInfo)
    {
        TaskSummaryTaskGroup? taskGroup = null;
        if (workItem.WorkflowQueueId.HasValue
           && workItem.WorkflowQueueId.Value != Guid.Empty
           && taskGroups.TryGetValue(workItem.WorkflowQueueId.Value, out var group))
        {
            taskGroup = group;
        }

        var userTask = new UserTask
        {
            Id = workItem.Id ?? Guid.Empty,
            Source = GetTaskSourceFromValue(workItem.Source),
            Title = workItem.Name ?? string.Empty,
            Description = workItem.Information ?? string.Empty,
            Type = GetTaskTypeFromValue(workItem.Type),
            NavigationUrl = GetTaskUrlFromValue(workItem.WorkItemUrl, workItem.Source, workItem.Id),
            Name = workItem.Name ?? string.Empty,
            CreatedDate = GetIsoString(workItem.CreatedDate),
            DueDate = GetIsoString(workItem.DueDate),
            AssignedDate = GetIsoString(workItem.AssignDate),
            Assignees = GetAssignees(workItem, assigneeInfo),
            Assignor = GetAssignor(workItem),
            AgreementIds = GetTaskAgreementIds(workItem.Documents),
            WorkflowName = workItem.WorkflowName ?? string.Empty,
            TaskGroupAssignee = GetUserTaskGroup(taskGroup),
        };

        workItem.Documents?.ForEach(document => userTask.AddAgreement(new UserTaskAgreement
        {
            Id = GetGuidFromUid(document.Uid),
            Source = GetTaskSourceFromValue(workItem.Source).ToString(),
            CreatedAt = document.CreatedDate ?? DateTimeOffset.MinValue,
            Name = document.Name ?? string.Empty,
        }));

        return userTask;
    }

    private static TaskGroupWithPermissions? GetUserTaskGroup(TaskSummaryTaskGroup? taskGroup)
    {
        if (taskGroup == null)
        {
            return null;
        }

        return new TaskGroupWithPermissions
        {
            Id = taskGroup.Id.ToString() ?? string.Empty,
            Name = taskGroup.Name ?? string.Empty,
            Source = TaskSource.ClmWorkflow,
            Members = taskGroup.Members?.Select(x => new TaskGroupMember { Id = x.Id, FullName = x.FullName ?? string.Empty, Email = x.Email ?? string.Empty }) ?? [],
            Permissions = new TaskPermissions
            {
                CanAssign = taskGroup.Permissions?.CanAssign ?? false,
                CanClaim = taskGroup.Permissions?.CanClaim ?? false,
                CanUnclaim = taskGroup.Permissions?.CanUnclaim ?? false,
            }
        };
    }

    private static Guid GetGuidFromUid(string? uid) => !string.IsNullOrEmpty(uid) ? Guid.Parse(uid) : Guid.Empty;

    private static string GetIsoString(DateTimeOffset? date)
    {
        return date?.UtcDateTime.ToString("o", CultureInfo.InvariantCulture) ?? string.Empty;
    }

    private static TaskSource? GetTaskSourceFromValue(string? source)
    {
        return string.IsNullOrEmpty(source) ? null : source switch
        {
            "CLMWorkflow" => TaskSource.ClmWorkflow,
            "Esignature" => TaskSource.ESignature,
            "IdVerification" => TaskSource.IdVerification,
            "AssignmentsAPI" => TaskSource.AssignmentsApi,
            _ => null,
        };
    }

    private static TaskType? GetTaskTypeFromValue(string? type)
    {
        return string.IsNullOrEmpty(type) ? null : type switch
        {
            HumanActivityConstants.ApproveDocumentsActivity => TaskType.Approve,
            HumanActivityConstants.ChoiceActivity => TaskType.Choice,
            HumanActivityConstants.RoutingActivity => TaskType.Routing,
            HumanActivityConstants.ChooseDocumentsActivity => TaskType.ChooseDocuments,
            HumanActivityConstants.ChooseUsersActivity => TaskType.ChooseUsers,
            HumanActivityConstants.CreateDocumentActivity => TaskType.CreateOrUploadDoc,
            HumanActivityConstants.EditDocumentsActivity => TaskType.EditDocument,
            HumanActivityConstants.FillFormActivity => TaskType.FillForm,
            HumanActivityConstants.FullPageFillFormActivity => TaskType.FullPageFillForm,
            HumanActivityConstants.EditFormActivity => TaskType.EditForm,
            HumanActivityConstants.FullPageEditFormActivity => TaskType.FullPageEditForm,
            HumanActivityConstants.ReviewAndSendForExternalReviewActivity => TaskType.ReviewAndSendForExternalReview,
            HumanActivityConstants.ReviewAndSendForSignatureActivity => TaskType.ReviewAndSendForSignature,
            HumanActivityConstants.DataReview => TaskType.ReviewData,
            HumanActivityConstants.DataReconciliationActivity => TaskType.ReconcileData,
            HumanActivityConstants.ReviewData => TaskType.ReviewData,
            HumanActivityConstants.ResolveComments => TaskType.ResolveComments,
            _ => null
        };
    }

    private static string GetTaskUrlFromValue(string? url, string? source, Guid? workItemId)
    {
        var taskSource = GetTaskSourceFromValue(source);
        if (url == null || taskSource == null || workItemId == null)
        {
            return url ?? string.Empty;
        }

        if (taskSource == TaskSource.ClmWorkflow)
        {
            var builder = new UriBuilder(url);

            // Extract first non-empty segment, default to "atlas"
            var segments = builder.Path.Split('/', StringSplitOptions.RemoveEmptyEntries);
            var root = segments.Length > 0 ? segments[0] : "atlas";

            // Normalize path
            builder.Path = $"/{root}/workflow/tasks";

            var query = builder.Query;
            var queryDictionary = System.Web.HttpUtility.ParseQueryString(query);
            queryDictionary.Clear();

            queryDictionary["wfid"] = workItemId.ToString();

            builder.Query = queryDictionary.ToString();

            return builder.Uri.ToString();
        }

        return url;
    }

    private static List<TaskUser> GetAssignees(TaskSummaryWorkItem workItem, Dictionary<Guid, string> assigneeInfo)
    {
        if (!workItem.AssigneeId.HasValue)
        {
            return [];
        }
        else
        {
            var name = string.Empty;
            if (assigneeInfo.TryGetValue(workItem.AssigneeId.Value, out var value))
            {
                name = value;
            }

            return [new TaskUser(workItem.AssigneeId.Value, name, string.Empty, CollaboratorType.Assignee)];
        }
    }

    private static TaskUser? GetAssignor(TaskSummaryWorkItem workItem)
    {
        return !workItem.AssignorId.HasValue
            ? null
            : new TaskUser(workItem.AssignorId.Value, string.Empty, string.Empty, CollaboratorType.Assignor);
    }

    private static List<Guid> GetTaskAgreementIds(List<Document>? documents)
    {
        if (documents == null || documents.Count == 0)
        {
            return [];
        }

        var agreements = new List<Guid>(documents.Count);
        foreach (var document in documents)
        {
            if (Guid.TryParse(document.Uid, out var guid))
            {
                agreements.Add(guid);
            }
        }

        return agreements;
    }

    private static string ConvertSortColumnToString(SortColumn? sortColumn)
    {
        return sortColumn switch
        {
            null => string.Empty,
            SortColumn.AssignedDate => "TaskAssignedDate",
            SortColumn.DueDate => "TaskDueDate",
            _ => string.Empty,
        };
    }
}
