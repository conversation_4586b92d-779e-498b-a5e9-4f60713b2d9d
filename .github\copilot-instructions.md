# Task Management Service - AI Agent Instructions

## Project Overview

Task Management Service is a .NET 8 microservice that **aggregates tasks from multiple sources** and provides a unified API to the task management widget. This is not a CRUD service—it's an aggregation layer that pulls data from various DocuSign services.

## Architecture & Key Concepts

### Clean Architecture Structure

```
src/
├── TaskManagementService/           # API layer (controllers, middleware, startup)
├── TaskManagementService.Core/      # Domain models, interfaces, MediatR handlers
├── TaskManagementService.Infrastructure/ # External service integrations
└── TaskManagementService.ServiceIntegrations/ # Generated API clients from OpenAPI specs
```

### Task Sources Integration

The service aggregates from these sources (see `TaskSource` enum):

-   `ClmWorkflow` - CLM workflow tasks via Assignments API
-   `ESignature` - DocuSign eSignature tasks via Envelope API
-   `IdVerification` - ID verification tasks
-   `AssignmentsApi` - Generic assignment tasks

**Key Pattern**: Each source has its own service implementing count/fetch interfaces:

-   `IClmTaskService` → `ClmTaskService`
-   `IEnvelopeApiService` → `EnvelopeApiService`

### Generated API Clients

**Critical**: API clients in `ServiceIntegrations/` are generated from OpenAPI specs. Never edit these manually:

-   `Clients/ReportingApi/` - Generated from `reportingapi.json`
-   `Clients/CLMV2API/` - Generated from CLM v2 API
-   `Contracts/assignmentsapi.yml` - Source contract for Assignments API

## Development Workflows

### Build & Run Commands

```bash
# Standard build (from root)
dotnet tool restore && dotnet build

# Local development with all integrations
cd src/TaskManagementService && dotnet run

# Docker development with nginx proxy
docker compose up --build
# Access at https://localhost/health

# True Dev (MSF environment integration)
# Touch Dockerfile in src/TaskManagementService to force rebuild
```

### Authentication & Authorization

Service uses DocuSign's ADM (Account Directory Management) auth:

-   JWT tokens with `TokenType` claim = "5" for user tokens
-   Authorization policies: `JwtToken`, `ClmRead`, `ClmWrite`, `Signature`, `SignatureTasks`
-   Controllers require `[Authorize(AuthPolicy.JwtToken)]` and `[Authorize(AuthPolicy.SignatureTasks)]`

### Configuration Patterns

Environment-specific config in `config/{env}/app-values.yaml`:

-   `local/` - Docker compose development
-   `demo/`, `integration/`, `stage/`, `production/` - MSF deployment environments
-   Secrets loaded from `/etc/task-management-service/secrets` in containers

### Testing Strategy

-   `TaskManagementService.Tests/` - Unit tests
-   `TaskManagementService.Integration.Tests/` - Integration tests
-   `TaskManagementService.Periodic.Tests/` - End-to-end periodic tests

## Code Patterns & Conventions

### MediatR Pattern

Controllers delegate to MediatR handlers in Core layer:

```csharp
[HttpGet("count")]
public async Task<ActionResult<TasksCount>> GetTasksCountAsync(Guid accountId, CancellationToken cancellationToken)
{
    var response = await mediator.Send(new GetUserTasksCountQuery(), cancellationToken);
    return response.TasksCount;
}
```

### Request Context Service

`IRequestContextService` provides authenticated user context:

-   `AccountId`, `UserId` - Current user identifiers
-   `Authorization` - Bearer token for downstream calls
-   `ESignApiBaseUrl` - Environment-specific eSign API base URL

### Error Handling & Logging

-   Serilog configured for structured logging
-   Health checks at `/health` endpoint with Optimizely integration
-   OpenTelemetry for tracing and metrics

### Controller Environment Filtering

`EnvironmentControllerFeatureProvider` conditionally includes controllers based on environment—some features only available in specific environments.

## Build Configuration

### .NET 8 Specific

-   SDK version pinned to `8.0.414` in `global.json`
-   `TreatWarningsAsErrors=true` and strict static analysis
-   Latest C# language features enabled
-   Linux x64 runtime targeting for containerization

### Dependencies & Package Management

-   Central package management via `Directory.Packages.props`
-   MSF (Microservices Framework) libraries for common DocuSign patterns
-   Hybrid caching with Redis for performance
-   Mapster for object mapping instead of AutoMapper

## External Service Integrations

### Service Client Pattern

External services use HTTP clients with proper authentication:

```csharp
httpRequest.Headers.TryAddWithoutValidation("Authorization", requestContextService.Authorization);
```

### API Contract Versioning

-   OpenAPI specs in `ServiceIntegrations/Contracts/`
-   Client regeneration required when contracts change
-   Versioned endpoints (e.g., `/api/v1/{accountId}/...`)

## Specialized Copilot Prompts

This project includes specialized prompts in `.github/instructions/` for common workflows:

### 1. Publish Pull Request (`publish-pull-request.md`)

Automatically creates a GitHub pull request with proper Jira integration and generated title/description based on code changes.

**Usage:**

```
@workspace /file:.github/instructions/publish-pull-request.md Please create a pull request for my current branch
```

**Features:**

-   Extracts Jira ticket ID from branch name
-   Analyzes code changes with `git diff`
-   Generates meaningful PR title and description
-   Uses the project's PR template
-   Adds "Copilot CLI" label

### 2. Review Pull Request (`review-pull-request.md`)

Performs comprehensive code review of GitHub pull requests following project standards.

**Usage:**

```
@workspace /file:.github/instructions/review-pull-request.md Please review PR #123
```

or

```
@workspace /file:.github/instructions/review-pull-request.md Please review the PR for my current branch
```

**Features:**

-   Follows project's C# coding standards from CONTRIBUTING.md and .editorconfig
-   Checks formatting, naming conventions, code quality
-   Analyzes functionality, logic, and error handling
-   Reviews architecture, design, and security
-   Posts detailed line-by-line comments via GitHub API

### 3. Self Review (`self-review.md`)

Performs self-review of changes in the current branch against main branch.

**Usage:**

```
@workspace /file:.github/instructions/self-review.md Please perform a self-review of my current changes
```

**Features:**

-   Compares current branch against main
-   Structured markdown output with clear categories
-   Focuses on project standards, naming conventions, code quality
-   Identifies potential bugs, security issues, and architectural concerns
-   Provides constructive feedback with specific suggestions

### Usage Examples

**Create a PR:**

```
@workspace /file:.github/instructions/publish-pull-request.md Create a PR for my feature branch with reviewer john.doe
```

**Review current changes:**

```
@workspace /file:.github/instructions/self-review.md Review my staged changes before I commit
```

**Review specific PR:**

```
@workspace /file:.github/instructions/review-pull-request.md Review PR #456 and post detailed comments
```

### Project Standards

All prompts follow the project's coding standards defined in:

-   `CONTRIBUTING.md` - Development guidelines and best practices
-   `.editorconfig` - Code formatting rules
-   Project-specific C# conventions for Task Management Service

## Common Gotchas

-   **Generated clients**: Never manually edit generated code in `ServiceIntegrations/Clients/`
-   **Environment controllers**: Some controllers only load in specific environments
-   **Task source mapping**: String to enum conversion in `GetTaskSourceFromValue()` method
-   **Docker development**: Touch Dockerfile to force rebuild in True Dev environment
-   **Redis caching**: Local development requires Redis container from docker-compose
