{"swagger": "2.0", "info": {"version": "v1", "title": "WebApi"}, "host": "localhost", "basePath": "/api", "schemes": ["http"], "paths": {"/v201411/accounts/current": {"get": {"tags": ["Accounts"], "operationId": "Accounts_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Account"}}}}, "patch": {"tags": ["Accounts"], "operationId": "Accounts_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "account", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Account"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Account"}}}}}, "/v201411/accounts/current/attributegroups": {"get": {"tags": ["Accounts"], "operationId": "Accounts_GetAttributeGroups", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[AttributeGroup]"}}}}}, "/v201411/accounts/{id}": {"put": {"tags": ["Accounts"], "operationId": "Accounts_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "account", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Account"}}, {"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Account"}}}}}, "/v201411/attributegroups/{id}": {"get": {"tags": ["AttributeGroups"], "operationId": "AttributeGroups_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/AttributeGroup"}}}}}, "/v2/{accountId}/attributegroups/{id}": {"get": {"tags": ["AttributeGroupsV2"], "operationId": "AttributeGroupsV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/AttributeGroup"}}}}}, "/v2/{accountId}/attributegroups": {"get": {"tags": ["AttributeGroupsV2"], "operationId": "AttributeGroupsV2_GetAllAttributeGroups", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[AttributeGroup]"}}}}}, "/v2/{accountId}/bulkTasks": {"get": {"tags": ["BulkTasksV2"], "operationId": "BulkTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[BulkTask]"}}}}}, "/v2/{accountId}/bulkWorkflowTasks/{id}": {"get": {"tags": ["BulkWorkflowTasksV2"], "operationId": "BulkWorkflowTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BulkWorkflowTask"}}}}}, "/v2/{accountId}/bulkWorkflowTasks": {"post": {"tags": ["BulkWorkflowTasksV2"], "operationId": "BulkWorkflowTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "bulkWorkflowTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/BulkWorkflowTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BulkWorkflowTask"}}}}}, "/v2/{accountId}/bulkWorkflowTasks/{taskId}": {"delete": {"tags": ["BulkWorkflowTasksV2"], "operationId": "BulkWorkflowTasksV2_Delete", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskId", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/v201411/changesecuritytasks/{taskid}": {"get": {"tags": ["ChangeSecurityTasks"], "operationId": "ChangeSecurityTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ChangeSecurityTask"}}}}}, "/v201411/changesecuritytasks": {"post": {"tags": ["ChangeSecurityTasks"], "operationId": "ChangeSecurityTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "changeSecurityTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ChangeSecurityTask"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ChangeSecurityTask"}}}}}, "/v2/{accountId}/changesecuritytasks/{taskid}": {"get": {"tags": ["ChangeSecurityTasksV2"], "operationId": "ChangeSecurityTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ChangeSecurityTask"}}}}}, "/v2/{accountId}/changesecuritytasks": {"post": {"tags": ["ChangeSecurityTasksV2"], "operationId": "ChangeSecurityTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "changeSecurityTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ChangeSecurityTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ChangeSecurityTask"}}}}}, "/v201411/clauses/{id}": {"get": {"tags": ["Clauses"], "operationId": "Clauses_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Clause"}}}}, "put": {"tags": ["Clauses"], "operationId": "Clauses_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "clause", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Clause"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Clause"}}}}, "delete": {"tags": ["Clauses"], "operationId": "Clauses_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["Clauses"], "operationId": "<PERSON><PERSON>_<PERSON>", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "clause", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Clause"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Clause"}}}}}, "/v201411/clauses/{id}/options": {"get": {"tags": ["Clauses"], "operationId": "Clauses_GetClauseOptions", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Option]"}}}}}, "/v201411/clauses": {"get": {"tags": ["Clauses"], "operationId": "<PERSON><PERSON>_GetAllClauses", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Clause]"}}}}, "post": {"tags": ["Clauses"], "operationId": "Clauses_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "clause", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Clause"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Clause"}}}}}, "/v2/{accountId}/clauses/{id}": {"get": {"tags": ["ClausesV2"], "operationId": "ClausesV2_GetAsync", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Clause"}}}}, "put": {"tags": ["ClausesV2"], "operationId": "ClausesV2_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "clause", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Clause"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Clause"}}}}, "delete": {"tags": ["ClausesV2"], "operationId": "ClausesV2_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["ClausesV2"], "operationId": "ClausesV2_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "clause", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Clause"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Clause"}}}}}, "/v2/{accountId}/clauses/{id}/options": {"get": {"tags": ["ClausesV2"], "operationId": "ClausesV2_GetClauseOptionsAsync", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Option]"}}}}}, "/v2/{accountId}/clauses": {"get": {"tags": ["ClausesV2"], "operationId": "ClausesV2_GetAllClausesAsync", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Clause]"}}}}, "post": {"tags": ["ClausesV2"], "operationId": "ClausesV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "clause", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Clause"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Clause"}}}}}, "/v201411/contacts/{id}": {"get": {"tags": ["Contacts"], "operationId": "Contacts_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Contact"}}}}, "put": {"tags": ["Contacts"], "operationId": "Contacts_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "contact", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Contact"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Contact"}}}}, "delete": {"tags": ["Contacts"], "operationId": "Contacts_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["Contacts"], "operationId": "Contacts_<PERSON>", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "contact", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Contact"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Contact"}}}}}, "/v201411/contacts": {"get": {"tags": ["Contacts"], "operationId": "Contacts_GetAllContacts", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Contact]"}}}}, "post": {"tags": ["Contacts"], "operationId": "Contacts_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "contact", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Contact"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Contact"}}}}}, "/v201411/contacts/{id}/groups": {"get": {"tags": ["Contacts"], "operationId": "Contacts_GetGroups", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Group]"}}}}}, "/v2/{accountId}/contacts/{id}": {"get": {"tags": ["ContactsV2"], "operationId": "ContactsV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Contact"}}}}, "put": {"tags": ["ContactsV2"], "operationId": "ContactsV2_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "contact", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Contact"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Contact"}}}}, "delete": {"tags": ["ContactsV2"], "operationId": "ContactsV2_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["ContactsV2"], "operationId": "ContactsV2_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "contact", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Contact"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Contact"}}}}}, "/v2/{accountId}/contacts": {"get": {"tags": ["ContactsV2"], "operationId": "ContactsV2_GetAllContacts", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Contact]"}}}}, "post": {"tags": ["ContactsV2"], "operationId": "ContactsV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "contact", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Contact"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Contact"}}}}}, "/v2/{accountId}/contacts/{id}/groups": {"get": {"tags": ["ContactsV2"], "operationId": "ContactsV2_GetGroups", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Group]"}}}}}, "/v201411/copytasks": {"post": {"tags": ["CopyTasks"], "operationId": "CopyTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "copyTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CopyTask"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CopyTask"}}}}}, "/v201411/copytasks/{taskid}": {"get": {"tags": ["CopyTasks"], "operationId": "CopyTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CopyTask"}}}}}, "/v2/{accountId}/copytasks": {"post": {"tags": ["CopyTasksV2"], "operationId": "CopyTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "copyTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CopyTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CopyTask"}}}}}, "/v2/{accountId}/copytasks/{taskid}": {"get": {"tags": ["CopyTasksV2"], "operationId": "CopyTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CopyTask"}}}}}, "/v201411/CustomData/{id}": {"get": {"tags": ["CustomData"], "operationId": "CustomData_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "filter", "in": "query", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/v201411/doclauncherconfigurations": {"get": {"tags": ["DocLauncherConfigurations"], "operationId": "DocLauncherConfigurations_GetAllConfigurations", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[DocLauncherConfiguration]"}}}}}, "/v201411/doclauncherconfigurations/{id}": {"get": {"tags": ["DocLauncherConfigurations"], "operationId": "DocLauncherConfigurations_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocLauncherConfiguration"}}}}}, "/v2/{accountId}/doclauncherconfigurations": {"get": {"tags": ["DocLauncherConfigurationsV2"], "operationId": "DocLauncherConfigurationsV2_GetAllConfigurations", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[DocLauncherConfiguration]"}}}}}, "/v2/{accountId}/doclauncherconfigurations/{id}": {"get": {"tags": ["DocLauncherConfigurationsV2"], "operationId": "DocLauncherConfigurationsV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocLauncherConfiguration"}}}}}, "/v201411/doclaunchertasks": {"post": {"tags": ["DocLauncherTasks"], "operationId": "DocLauncherTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "docLauncherTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocLauncherTask"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocLauncherTask"}}}}}, "/v201411/doclaunchertasks/{taskId}": {"get": {"tags": ["DocLauncherTasks"], "operationId": "DocLauncherTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskId", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocLauncherTask"}}}}}, "/v2/{accountId}/doclaunchertasks": {"post": {"tags": ["DocLauncherTasksV2"], "operationId": "DocLauncherTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "docLauncherTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocLauncherTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocLauncherTask"}}}}}, "/v2/{accountId}/doclaunchertasks/{taskId}": {"get": {"tags": ["DocLauncherTasksV2"], "operationId": "DocLauncherTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskId", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocLauncherTask"}}}}}, "/v201411/documentcomparetasks": {"post": {"tags": ["DocumentCompareTasks"], "operationId": "DocumentCompareTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentCompareTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentCompareTask"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentCompareTask"}}}}}, "/v201411/documentcomparetasks/{taskid}": {"get": {"tags": ["DocumentCompareTasks"], "operationId": "DocumentCompareTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentCompareTask"}}}}}, "/v2/{accountId}/documentcomparetasks": {"post": {"tags": ["DocumentCompareTasksV2"], "operationId": "DocumentCompareTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentCompareTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentCompareTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentCompareTask"}}}}}, "/v2/{accountId}/documentcomparetasks/{taskid}": {"get": {"tags": ["DocumentCompareTasksV2"], "operationId": "DocumentCompareTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentCompareTask"}}}}}, "/v201411/documentmergetasks": {"post": {"tags": ["DocumentMergeTasks"], "operationId": "DocumentMergeTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentMergeTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentMergeTask"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentMergeTask"}}}}}, "/v201411/documentmergetasks/{taskid}": {"get": {"tags": ["DocumentMergeTasks"], "operationId": "DocumentMergeTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentMergeTask"}}}}}, "/v2/{accountId}/documentmergetasks": {"post": {"tags": ["DocumentMergeTasksV2"], "operationId": "DocumentMergeTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentMergeTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentMergeTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentMergeTask"}}}}}, "/v2/{accountId}/documentmergetasks/{taskid}": {"get": {"tags": ["DocumentMergeTasksV2"], "operationId": "DocumentMergeTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentMergeTask"}}}}}, "/v2/{accountId}/documentPreviews/{id}": {"get": {"tags": ["DocumentPreviewV2"], "operationId": "DocumentPreviewV2_GetDocumentPreview", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "extractPages", "in": "query", "required": false, "type": "boolean"}, {"name": "extractOverlays", "in": "query", "required": false, "type": "boolean"}, {"name": "processOverlays", "in": "query", "required": false, "type": "boolean"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentPreview"}}}}}, "/v2/{accountId}/documentPreviews/{id}/pages": {"get": {"tags": ["DocumentPreviewV2"], "operationId": "DocumentPreviewV2_GetDocumentPreviewPages", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[PreviewPage]"}}}}}, "/v2/{accountId}/documentPreviews/{id}/pageImageStatuses": {"get": {"tags": ["DocumentPreviewV2"], "operationId": "DocumentPreviewV2_GetDocumentPreviewPageImageStatus", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[PreviewPageStatus]"}}}}}, "/v201411/documentprocesstrackingactivitytasks/{taskid}": {"get": {"tags": ["DocumentProcessTrackingTasks"], "operationId": "DocumentProcessTrackingTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentProcessTrackingTask"}}}}}, "/v201411/documentprocesstrackingactivitytasks": {"post": {"tags": ["DocumentProcessTrackingTasks"], "operationId": "DocumentProcessTrackingTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentProcessTrackingActivityTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentProcessTrackingActivityTask"}}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentProcessTrackingTask"}}}}}, "/v201411/documentprocesstrackinghistorytasks/{taskid}": {"get": {"tags": ["DocumentProcessTrackingTasks"], "operationId": "DocumentProcessTrackingTasks_GetHistory", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentProcessTrackingTask"}}}}}, "/v201411/documentprocesstrackinghistorytasks": {"post": {"tags": ["DocumentProcessTrackingTasks"], "operationId": "DocumentProcessTrackingTasks_PostHistory", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentProcessTrackingHistoryTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentProcessTrackingHistoryTask"}}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentProcessTrackingTask"}}}}}, "/v2/{accountId}/documentprocesstrackingactivitytasks/{taskid}": {"get": {"tags": ["DocumentProcessTrackingTasksV2"], "operationId": "DocumentProcessTrackingTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentProcessTrackingTask"}}}}}, "/v2/{accountId}/documentprocesstrackingactivitytasks": {"post": {"tags": ["DocumentProcessTrackingTasksV2"], "operationId": "DocumentProcessTrackingTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentProcessTrackingActivityTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentProcessTrackingActivityTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentProcessTrackingTask"}}}}}, "/v2/{accountId}/documentprocesstrackinghistorytasks/{taskid}": {"get": {"tags": ["DocumentProcessTrackingTasksV2"], "operationId": "DocumentProcessTrackingTasksV2_GetHistory", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentProcessTrackingTask"}}}}}, "/v2/{accountId}/documentprocesstrackinghistorytasks": {"post": {"tags": ["DocumentProcessTrackingTasksV2"], "operationId": "DocumentProcessTrackingTasksV2_PostHistory", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentProcessTrackingHistoryTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentProcessTrackingHistoryTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentProcessTrackingTask"}}}}}, "/v201411/documentreminders/{id}": {"get": {"tags": ["DocumentReminders"], "operationId": "DocumentReminders_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentReminder"}}}}, "put": {"tags": ["DocumentReminders"], "operationId": "DocumentReminders_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentReminder", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentReminder"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentReminder"}}}}, "delete": {"tags": ["DocumentReminders"], "operationId": "DocumentReminders_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["DocumentReminders"], "operationId": "DocumentReminders_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentReminder", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentReminder"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentReminder"}}}}}, "/v201411/documentreminders/{id}/recipientcontacts": {"get": {"tags": ["DocumentReminders"], "operationId": "DocumentReminders_GetRecipientContactItems", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Contact]"}}}}}, "/v201411/documentreminders/{id}/recipientusers": {"get": {"tags": ["DocumentReminders"], "operationId": "DocumentReminders_GetRecipientUserItems", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[User]"}}}}}, "/v201411/documentreminders/{id}/recipientgroups": {"get": {"tags": ["DocumentReminders"], "operationId": "DocumentReminders_GetRecipientGroupItems", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Group]"}}}}}, "/v201411/documentreminders": {"post": {"tags": ["DocumentReminders"], "operationId": "DocumentReminders_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentReminder", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentReminder"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentReminder"}}}}}, "/v2/{accountId}/documentreminders/{id}": {"get": {"tags": ["DocumentRemindersV2"], "operationId": "DocumentRemindersV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentReminder"}}}}, "put": {"tags": ["DocumentRemindersV2"], "operationId": "DocumentRemindersV2_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentReminder", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentReminder"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentReminder"}}}}, "delete": {"tags": ["DocumentRemindersV2"], "operationId": "DocumentRemindersV2_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["DocumentRemindersV2"], "operationId": "DocumentRemindersV2_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentReminder", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentReminder"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentReminder"}}}}}, "/v2/{accountId}/documentreminders/{id}/recipientcontacts": {"get": {"tags": ["DocumentRemindersV2"], "operationId": "DocumentRemindersV2_GetRecipientContactItems", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Contact]"}}}}}, "/v2/{accountId}/documentreminders/{id}/recipientusers": {"get": {"tags": ["DocumentRemindersV2"], "operationId": "DocumentRemindersV2_GetRecipientUserItems", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[User]"}}}}}, "/v2/{accountId}/documentreminders/{id}/recipientgroups": {"get": {"tags": ["DocumentRemindersV2"], "operationId": "DocumentRemindersV2_GetRecipientGroupItems", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Group]"}}}}}, "/v2/{accountId}/documentreminders": {"post": {"tags": ["DocumentRemindersV2"], "operationId": "DocumentRemindersV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentReminder", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentReminder"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentReminder"}}}}}, "/v201411/documents/{id}": {"get": {"tags": ["Documents"], "operationId": "Documents_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Document"}}}}, "put": {"tags": ["Documents"], "operationId": "Documents_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "document", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Document"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Document"}}}}, "delete": {"tags": ["Documents"], "operationId": "Documents_DeleteDocument", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Document"}}}}, "patch": {"tags": ["Documents"], "operationId": "Documents_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "document", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Document"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Document"}}}}}, "/v201411/documents": {"get": {"tags": ["Documents"], "operationId": "Documents_GetByPath", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "path", "in": "query", "required": true, "type": "string"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Document"}}}}}, "/v201411/documents/{id}/historyItems": {"get": {"tags": ["Documents"], "operationId": "Documents_GetHistoryItems", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[HistoryItem]"}}}}}, "/v201411/documents/{id}/versions": {"get": {"tags": ["Documents"], "operationId": "Documents_GetVersions", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v201411/documents/{id}/lock": {"get": {"tags": ["Documents"], "operationId": "Documents_GetLock", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentLock"}}}}, "put": {"tags": ["Documents"], "operationId": "Documents_PutLock", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "documentLock", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentLock"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentLock"}}}}, "post": {"tags": ["Documents"], "operationId": "Documents_PostLock", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentLock"}}}}, "delete": {"tags": ["Documents"], "operationId": "Documents_DeleteLock", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentLock"}}}}, "patch": {"tags": ["Documents"], "operationId": "Documents_PatchLock", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "documentLock", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentLock"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentLock"}}}}}, "/v201411/documents/{id}/sharelinks": {"get": {"tags": ["Documents"], "operationId": "Documents_GetShareLinks", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[ShareLink]"}}}}}, "/v201411/documents/{id}/documentprocesstrackingactivities": {"get": {"tags": ["Documents"], "operationId": "Documents_GetDocumentProcessTrackingActivities", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[DocumentProcessTrackingActivity]"}}}}}, "/v201411/documents/{id}/documentreminders": {"get": {"tags": ["Documents"], "operationId": "Documents_GetDocumentReminders", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[DocumentReminder]"}}}}}, "/v201411/documents/{id}/relateddocuments": {"get": {"tags": ["Documents"], "operationId": "Documents_GetRealtedDocuments", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v201411/documents/{id}/attacheddocuments": {"get": {"tags": ["Documents"], "operationId": "Documents_GetAttachedDocuments", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v201411/documentsearchtasks/{taskid}": {"get": {"tags": ["DocumentSearchTasks"], "operationId": "DocumentSearchTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SearchTask"}}}}}, "/v201411/documentsearchtasks/{taskid}/result": {"get": {"tags": ["DocumentSearchTasks"], "operationId": "DocumentSearchTasks_GetSearchResult", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v201411/documentsearchtasks": {"post": {"tags": ["DocumentSearchTasks"], "operationId": "DocumentSearchTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "searchTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentSearchTask"}}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SearchTask"}}}}}, "/v2/{accountId}/documentsearchtasks/{taskid}": {"get": {"tags": ["DocumentSearchTasksV2"], "operationId": "DocumentSearchTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SearchTask"}}}}}, "/v2/{accountId}/documentsearchtasks/{taskid}/result": {"get": {"tags": ["DocumentSearchTasksV2"], "operationId": "DocumentSearchTasksV2_GetSearchResult", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v2/{accountId}/documentsearchtasks": {"post": {"tags": ["DocumentSearchTasksV2"], "operationId": "DocumentSearchTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "searchTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentSearchTask"}}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SearchTask"}}}}}, "/v201411/documentsloadtasks/{taskid}": {"get": {"tags": ["DocumentsLoadTasks"], "operationId": "DocumentsLoadTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentsLoadTask"}}}}}, "/v201411/documentsloadtasks/{taskid}/result": {"get": {"tags": ["DocumentsLoadTasks"], "operationId": "DocumentsLoadTasks_GetDocumentLoadResult", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v201411/documentsloadtasks": {"post": {"tags": ["DocumentsLoadTasks"], "operationId": "DocumentsLoadTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentsLoadTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentsLoadTask"}}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentsLoadTask"}}}}}, "/v2/{accountId}/documentsloadtasks/{taskid}": {"get": {"tags": ["DocumentsLoadTasksV2"], "operationId": "DocumentsLoadTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentsLoadTask"}}}}}, "/v2/{accountId}/documentsloadtasks/{taskid}/result": {"get": {"tags": ["DocumentsLoadTasksV2"], "operationId": "DocumentsLoadTasksV2_GetDocumentLoadResult", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v2/{accountId}/documentsloadtasks": {"post": {"tags": ["DocumentsLoadTasksV2"], "operationId": "DocumentsLoadTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentsLoadTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentsLoadTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentsLoadTask"}}}}}, "/v2/{accountId}/documents/{id}": {"get": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Document"}}}}, "put": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "document", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Document"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Document"}}}}, "delete": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_Delete", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Document"}}}}, "patch": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "document", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Document"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Document"}}}}}, "/v2/{accountId}/documents": {"get": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_GetByPath", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "path", "in": "query", "required": false, "type": "string"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Document"}}}}}, "/v2/{accountId}/documents/{id}/historyItems": {"get": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_GetHistoryItems", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[HistoryItem]"}}}}}, "/v2/{accountId}/documents/{id}/versions": {"get": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_GetVersions", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/v2/{accountId}/documents/{id}/lock": {"get": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_GetLock", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentLock"}}}}, "put": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_PutLock", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "documentLock", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentLock"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentLock"}}}}, "post": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_PostLock", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentLock"}}}}, "delete": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_DeleteLock", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentLock"}}}}, "patch": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_PatchLock", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "documentLock", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentLock"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentLock"}}}}}, "/v2/{accountId}/documents/{id}/sharelinks": {"get": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_GetShareLinks", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[ShareLink]"}}}}}, "/v2/{accountId}/documents/{id}/documentprocesstrackingactivities": {"get": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_GetDocumentProcessTrackingActivities", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[DocumentProcessTrackingActivity]"}}}}}, "/v2/{accountId}/documents/{id}/documentreminders": {"get": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_GetDocumentReminders", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[DocumentReminder]"}}}}}, "/v2/{accountId}/documents/{id}/relateddocuments": {"get": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_GetRelatedDocuments", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v2/{accountId}/documents/{id}/attacheddocuments": {"get": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_GetAttachedDocuments", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v2/{accountId}/documents/{id}/workitems": {"get": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_GetDocumentWorkItems", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[WorkItem]"}}}}}, "/v2/{accountId}/documents/{id}/reviews": {"get": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_GetExternalReviewFromDocument", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Review"}}}}}, "/v2/{accountId}/documents/{id}/customdata": {"get": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_GetCustomData", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "filter", "in": "query", "required": false, "type": "string"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/v2/{accountId}/documents/{id}/publish": {"post": {"tags": ["DocumentsV2"], "operationId": "DocumentsV2_PublishDocument", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Document"}}}}}, "/v201411/documentxmlmergetasks": {"post": {"tags": ["DocumentXmlMergeTasks"], "operationId": "DocumentXmlMergeTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentXmlMergeTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentXmlMergeTask"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentXmlMergeTask"}}}}}, "/v201411/documentxmlmergetasks/{taskid}": {"get": {"tags": ["DocumentXmlMergeTasks"], "operationId": "DocumentXmlMergeTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentXmlMergeTask"}}}}}, "/v2/{accountId}/documentxmlmergetasks": {"post": {"tags": ["DocumentXmlMergeTasksV2"], "operationId": "DocumentXmlMergeTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "documentXmlMergeTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DocumentXmlMergeTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentXmlMergeTask"}}}}}, "/v2/{accountId}/documentxmlmergetasks/{taskid}": {"get": {"tags": ["DocumentXmlMergeTasksV2"], "operationId": "DocumentXmlMergeTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/DocumentXmlMergeTask"}}}}}, "/v201411/externalreviewtasks/{id}": {"get": {"tags": ["ExternalReviewTasks"], "operationId": "ExternalReviewTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExternalReviewTask"}}}}, "delete": {"tags": ["ExternalReviewTasks"], "operationId": "ExternalReviewTasks_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"204": {"description": "No Content"}}}}, "/v201411/externalreviewtasks": {"post": {"tags": ["ExternalReviewTasks"], "operationId": "ExternalReviewTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "externalReviewTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ExternalReviewTask"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExternalReviewTask"}}}}}, "/v201411/externalreviewtasks/{id}/documents": {"get": {"tags": ["ExternalReviewTasks"], "operationId": "ExternalReviewTasks_GetDocuments", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v2/{accountId}/externalreviewtasks/{id}": {"get": {"tags": ["ExternalReviewV2Tasks"], "operationId": "ExternalReviewV2Tasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExternalReviewTask"}}}}, "delete": {"tags": ["ExternalReviewV2Tasks"], "operationId": "ExternalReviewV2Tasks_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "No Content"}}}}, "/v2/{accountId}/externalreviewtasks": {"post": {"tags": ["ExternalReviewV2Tasks"], "operationId": "ExternalReviewV2Tasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "externalReviewTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ExternalReviewTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExternalReviewTask"}}}}}, "/v2/{accountId}/externalreviewtasks/{id}/documents": {"get": {"tags": ["ExternalReviewV2Tasks"], "operationId": "ExternalReviewV2Tasks_GetDocuments", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v201411/folderarchivetasks": {"post": {"tags": ["FolderArchiveTasks"], "operationId": "FolderArchiveTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "folderArchiveTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/FolderArchiveTask"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/FolderArchiveTask"}}}}}, "/v201411/folderarchivetasks/{taskid}": {"get": {"tags": ["FolderArchiveTasks"], "operationId": "FolderArchiveTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/FolderArchiveTask"}}}}}, "/v2/{accountId}/folderarchivetasks": {"post": {"tags": ["FolderArchiveTasksV2"], "operationId": "FolderArchiveTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "folderArchiveTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/FolderArchiveTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/FolderArchiveTask"}}}}}, "/v2/{accountId}/folderarchivetasks/{taskid}": {"get": {"tags": ["FolderArchiveTasksV2"], "operationId": "FolderArchiveTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/FolderArchiveTask"}}}}}, "/v201411/folders": {"get": {"tags": ["Folders"], "operationId": "Folders_GetByPathOrSystemFolder", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "folderParams.path", "in": "query", "required": false, "type": "string"}, {"name": "folderParams.systemFolder", "in": "query", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}, "post": {"tags": ["Folders"], "operationId": "Folders_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "folder", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Folder"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}}, "/v201411/folders/{id}": {"get": {"tags": ["Folders"], "operationId": "Folders_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}, "put": {"tags": ["Folders"], "operationId": "Folders_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "folder", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Folder"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}, "delete": {"tags": ["Folders"], "operationId": "Folders_Delete", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}, "patch": {"tags": ["Folders"], "operationId": "Folders_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "folder", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Folder"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}}, "/v201411/folders/{id}/documents": {"get": {"tags": ["Folders"], "operationId": "Folders_GetDocuments", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v201411/folders/{id}/folders": {"get": {"tags": ["Folders"], "operationId": "Folders_GetChildFolders", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Folder]"}}}}}, "/v201411/folders/{id}/sharelinks": {"get": {"tags": ["Folders"], "operationId": "Folders_GetShareLinks", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[ShareLink]"}}}}}, "/v201411/foldersearchtasks/{taskid}": {"get": {"tags": ["FolderSearchTasks"], "operationId": "FolderSearchTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SearchTask"}}}}}, "/v201411/foldersearchtasks/{taskid}/result": {"get": {"tags": ["FolderSearchTasks"], "operationId": "FolderSearchTasks_GetSearchResult", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Folder]"}}}}}, "/v201411/foldersearchtasks": {"post": {"tags": ["FolderSearchTasks"], "operationId": "FolderSearchTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "searchTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/FolderSearchTask"}}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SearchTask"}}}}}, "/v2/{accountId}/foldersearchtasks/{taskid}": {"get": {"tags": ["FolderSearchTasksV2"], "operationId": "FolderSearchTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SearchTask"}}}}}, "/v2/{accountId}/foldersearchtasks/{taskid}/result": {"get": {"tags": ["FolderSearchTasksV2"], "operationId": "FolderSearchTasksV2_GetSearchResult", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Folder]"}}}}}, "/v2/{accountId}/foldersearchtasks": {"post": {"tags": ["FolderSearchTasksV2"], "operationId": "FolderSearchTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "searchTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/FolderSearchTask"}}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SearchTask"}}}}}, "/v2/{accountId}/folders/path": {"get": {"tags": ["FoldersV2"], "operationId": "FoldersV2_GetByPath", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "path", "in": "query", "required": false, "type": "string"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}}, "/v2/{accountId}/folders/type": {"get": {"tags": ["FoldersV2"], "operationId": "FoldersV2_GetBySystemType", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "systemFolder", "in": "query", "required": true, "type": "string"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}}, "/v2/{accountId}/folders/search": {"post": {"tags": ["FoldersV2"], "operationId": "FoldersV2_Search", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "search", "in": "query", "required": true, "type": "string"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Folder]"}}}}}, "/v2/{accountId}/folders/{id}": {"get": {"tags": ["FoldersV2"], "operationId": "FoldersV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}, "put": {"tags": ["FoldersV2"], "operationId": "FoldersV2_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "folder", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Folder"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}, "delete": {"tags": ["FoldersV2"], "operationId": "FoldersV2_Delete", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}, "patch": {"tags": ["FoldersV2"], "operationId": "FoldersV2_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "folder", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Folder"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}}, "/v2/{accountId}/folders/eos": {"get": {"tags": ["FoldersV2"], "operationId": "FoldersV2_GetByEos", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "eosObjectType", "in": "query", "required": true, "type": "string"}, {"name": "eosObjectId", "in": "query", "required": true, "type": "string"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}}, "/v2/{accountId}/folders/{id}/documents": {"get": {"tags": ["FoldersV2"], "operationId": "FoldersV2_GetDocuments", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v2/{accountId}/folders/{id}/folders": {"get": {"tags": ["FoldersV2"], "operationId": "FoldersV2_GetChildFolders", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Folder]"}}}}}, "/v2/{accountId}/folders/{id}/sharelinks": {"get": {"tags": ["FoldersV2"], "operationId": "FoldersV2_GetShareLinks", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[ShareLink]"}}}}}, "/v2/{accountId}/folders": {"post": {"tags": ["FoldersV2"], "operationId": "FoldersV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "folder", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Folder"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Folder"}}}}}, "/v201411/groups/{id}": {"get": {"tags": ["Groups"], "operationId": "Groups_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Group"}}}}, "put": {"tags": ["Groups"], "operationId": "Groups_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "group", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Group"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Group"}}}}, "delete": {"tags": ["Groups"], "operationId": "Groups_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["Groups"], "operationId": "Groups_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "group", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Group"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Group"}}}}}, "/v201411/groups/{id}/groupmembers": {"get": {"tags": ["Groups"], "operationId": "Groups_GetUsers", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[User]"}}}}}, "/v201411/groups": {"get": {"tags": ["Groups"], "operationId": "Groups_GetAllGroups", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Group]"}}}}, "post": {"tags": ["Groups"], "operationId": "Groups_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "group", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Group"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Group"}}}}}, "/v201411/scim/groups": {"get": {"tags": ["GroupsScim"], "operationId": "GroupsScim_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "filter", "in": "query", "required": false, "type": "string"}, {"name": "startIndex", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "count", "in": "query", "required": false, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ListResponseScim[GroupScim]"}}}}, "post": {"tags": ["GroupsScim"], "operationId": "GroupsScim_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "group", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GroupScim"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/GroupScim"}}}}}, "/v201411/scim/groups/{id}": {"get": {"tags": ["GroupsScim"], "operationId": "GroupsScim_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/GroupScim"}}}}, "put": {"tags": ["GroupsScim"], "operationId": "GroupsScim_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "group", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GroupScim"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/GroupScim"}}}}, "delete": {"tags": ["GroupsScim"], "operationId": "GroupsScim_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["GroupsScim"], "operationId": "GroupsScim_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "patchRequest", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PatchRequestScim"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/GroupScim"}}}}}, "/v2/{accountId}/groups/{id}": {"get": {"tags": ["GroupsV2"], "operationId": "GroupsV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Group"}}}}}, "/v2/{accountId}/groups/{id}/groupmembers": {"get": {"tags": ["GroupsV2"], "operationId": "GroupsV2_GetUsers", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[User]"}}}}}, "/v2/{accountId}/groups": {"get": {"tags": ["GroupsV2"], "operationId": "GroupsV2_GetAllGroups", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Group]"}}}}}, "/v2/{accountId}/mailinglists/{id}": {"get": {"tags": ["MailingLists"], "operationId": "MailingLists_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/MailingList"}}}}, "put": {"tags": ["MailingLists"], "operationId": "MailingLists_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "group", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Group"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/MailingList"}}}}, "delete": {"tags": ["MailingLists"], "operationId": "MailingLists_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["MailingLists"], "operationId": "MailingLists_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "group", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Group"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/MailingList"}}}}}, "/v2/{accountId}/mailinglists/{id}/groupmembers": {"get": {"tags": ["MailingLists"], "operationId": "MailingLists_GetUsers", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[User]"}}}}}, "/v2/{accountId}/mailinglists": {"get": {"tags": ["MailingLists"], "operationId": "MailingLists_GetAllmailinglists", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[MailingList]"}}}}, "post": {"tags": ["MailingLists"], "operationId": "MailingLists_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "group", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Group"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/MailingList"}}}}}, "/v2/{accountId}/members": {"get": {"tags": ["Members"], "operationId": "Members_GetMembers", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Member]"}}}}}, "/v2/{accountId}/members/{id}": {"get": {"tags": ["Members"], "operationId": "Members_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Member"}}}}, "put": {"tags": ["Members"], "operationId": "Members_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "member", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Member"}}, {"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Member"}}}}, "patch": {"tags": ["Members"], "operationId": "Members_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "member", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Member"}}, {"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Member"}}}}}, "/v2/{accountId}/members/current": {"get": {"tags": ["Members"], "operationId": "Members_GetCurrent", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Member"}}}}, "put": {"tags": ["Members"], "operationId": "Members_PutCurrent", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "member", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Member"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Member"}}}}, "patch": {"tags": ["Members"], "operationId": "Members_PatchCurrent", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "member", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Member"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Member"}}}}}, "/v2/{accountId}/members/{id}/workitems": {"get": {"tags": ["Members"], "operationId": "Members_GetWorkflowActivities", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[WorkItem]"}}}}}, "/v2/{accountId}/members/current/workitems": {"get": {"tags": ["Members"], "operationId": "Members_GetCurrentWorkflowActivities", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[WorkItem]"}}}}}, "/v2/{accountId}/members/current/recentdocuments": {"get": {"tags": ["Members"], "operationId": "Members_GetCurrentRecentDocuments", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v2/{accountId}/members/{id}/workflowqueues": {"get": {"tags": ["Members"], "operationId": "Members_GetWorkflowQueues", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[WorkflowQueue]"}}}}}, "/v2/{accountId}/members/current/workflowqueues": {"get": {"tags": ["Members"], "operationId": "Members_GetCurrentWorkflowQueues", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[WorkflowQueue]"}}}}}, "/v2/{accountId}/members/current/watcheddocuments": {"get": {"tags": ["Members"], "operationId": "Members_GetCurrentWatchedDocuments", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v2/{accountId}/members/current/watcheddocumentsprocesstrackingactivities": {"get": {"tags": ["Members"], "operationId": "Members_GetCurrentWatchedDocumentsProcessTrackingActivities", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[DocumentProcessTrackingActivity]"}}}}}, "/v2/{accountId}/members/{id}/permissionsets": {"get": {"tags": ["Members"], "operationId": "Members_GetPermissionSetsForMember", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[PermissionSet]"}}}}}, "/v2/{accountId}/members/current/permissionsets": {"get": {"tags": ["Members"], "operationId": "Members_GetCurrentPermissionSets", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[PermissionSet]"}}}}}, "/v2/{accountId}/members/{id}/groups": {"get": {"tags": ["Members"], "operationId": "Members_GetGroups", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Group]"}}}}}, "/v2/{accountId}/members/current/groups": {"get": {"tags": ["Members"], "operationId": "Members_GetGroupsCurrent", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Group]"}}}}}, "/v2/{accountId}/members/current/workItems/summary": {"post": {"tags": ["Members"], "operationId": "Members_PostTaskSummary", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/TaskSummaryRequestBody"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TaskSummary"}}}}}, "/v2/{accountId}/members/current/workflowqueues/members": {"post": {"tags": ["Members"], "operationId": "Members_PostWorkflowMembers", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PostWorkflowMembersRequestBody"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[TaskSummaryTaskGroup]"}}}}}, "/v2/{accountId}/members/current/managedusers": {"get": {"tags": ["Members"], "operationId": "Members_GetManagedUsersCurrent", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[User]"}}}}}, "/v2/{accountId}/members/current/managedusers/tasks": {"get": {"tags": ["Members"], "operationId": "Members_GetTaskManagedUsersCurrent", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[TaskManagedUser]"}}}}}, "/Members/{id}": {"get": {"tags": ["Members"], "operationId": "Members_GetManagedUsers", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[User]"}}}}}, "/v201411/options": {"post": {"tags": ["Options"], "operationId": "Options_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "option", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Option"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Option"}}}}}, "/v201411/options/{id}": {"put": {"tags": ["Options"], "operationId": "Options_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "option", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Option"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Option"}}}}, "delete": {"tags": ["Options"], "operationId": "Options_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["Options"], "operationId": "Options_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "option", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Option"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Option"}}}}}, "/v2/{accountId}/options": {"post": {"tags": ["OptionsV2"], "operationId": "OptionsV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "option", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Option"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Option"}}}}}, "/v2/{accountId}/options/{id}": {"put": {"tags": ["OptionsV2"], "operationId": "OptionsV2_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "option", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Option"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Option"}}}}, "delete": {"tags": ["OptionsV2"], "operationId": "OptionsV2_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["OptionsV2"], "operationId": "OptionsV2_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "option", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Option"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Option"}}}}}, "/v201411/parties": {"get": {"tags": ["Parties"], "operationId": "Parties_GetAllParties", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Party]"}}}}, "post": {"tags": ["Parties"], "operationId": "Parties_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "party", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Party"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Party"}}}}}, "/v201411/parties/{id}": {"get": {"tags": ["Parties"], "operationId": "Parties_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Party"}}}}, "put": {"tags": ["Parties"], "operationId": "Parties_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "party", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Party"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Party"}}}}, "delete": {"tags": ["Parties"], "operationId": "Parties_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["Parties"], "operationId": "Parties_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "party", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Party"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Party"}}}}}, "/v2/{accountId}/parties": {"get": {"tags": ["PartiesV2"], "operationId": "PartiesV2_GetAllParties", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Party]"}}}}, "post": {"tags": ["PartiesV2"], "operationId": "PartiesV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "party", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Party"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Party"}}}}}, "/v2/{accountId}/parties/{id}": {"get": {"tags": ["PartiesV2"], "operationId": "PartiesV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Party"}}}}, "put": {"tags": ["PartiesV2"], "operationId": "PartiesV2_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "party", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Party"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Party"}}}}, "delete": {"tags": ["PartiesV2"], "operationId": "PartiesV2_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["PartiesV2"], "operationId": "PartiesV2_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "party", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Party"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Party"}}}}}, "/v201411/permissionsets/{id}": {"get": {"tags": ["PermissionSets"], "operationId": "PermissionSets_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/PermissionSet"}}}}}, "/v201411/permissionsets": {"get": {"tags": ["PermissionSets"], "operationId": "PermissionSets_GetAll", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[PermissionSet]"}}}}}, "/v2/{accountId}/permissionsets/{id}": {"get": {"tags": ["PermissionSetsV2"], "operationId": "PermissionSetsV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/PermissionSet"}}}}}, "/v2/{accountId}/permissionsets": {"get": {"tags": ["PermissionSetsV2"], "operationId": "PermissionSetsV2_GetAll", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[PermissionSet]"}}}}}, "/v2/{accountId}/processdefinitions": {"get": {"tags": ["ProcessDefinitionsV2"], "operationId": "ProcessDefinitionsV2_GetAll", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[ProcessDefinition]"}}}}}, "/v2/{accountId}/processdefinitions/{id}": {"get": {"tags": ["ProcessDefinitionsV2"], "operationId": "ProcessDefinitionsV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ProcessDefinition"}}}}}, "/v201411/scim/resourcetypes": {"get": {"tags": ["ResourceTypesScim"], "operationId": "ResourceTypesScim_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/ResourceTypeScim"}}}}}}, "/v201411/scim/resourcetypes/user": {"get": {"tags": ["ResourceTypesScim"], "operationId": "ResourceTypesScim_GetUser", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResourceTypeScim"}}}}}, "/v201411/scim/resourcetypes/group": {"get": {"tags": ["ResourceTypesScim"], "operationId": "ResourceTypesScim_GetGroup", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResourceTypeScim"}}}}}, "/v2/{accountId}/reviews/{id}": {"get": {"tags": ["ReviewsV2"], "operationId": "ReviewsV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Review"}}}}, "put": {"tags": ["ReviewsV2"], "operationId": "ReviewsV2_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "review", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Review"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Review"}}}}, "delete": {"tags": ["ReviewsV2"], "operationId": "ReviewsV2_Delete", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Review"}}}}}, "/v2/{accountId}/reviews": {"post": {"tags": ["ReviewsV2"], "operationId": "ReviewsV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "review", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Review"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/v201411/salesforceattachmentimporttasks": {"post": {"tags": ["SalesforceAttachmentImportTasks"], "operationId": "SalesforceAttachmentImportTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "salesforceAttachmentImportTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SalesforceAttachmentImportTask"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SalesforceAttachmentImportTask"}}}}}, "/v201411/salesforceattachmentimporttasks/{taskid}": {"get": {"tags": ["SalesforceAttachmentImportTasks"], "operationId": "SalesforceAttachmentImportTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SalesforceAttachmentImportTask"}}}}}, "/v2/{accountId}/salesforceattachmentimporttasks": {"post": {"tags": ["SalesforceAttachmentImportTasksV2"], "operationId": "SalesforceAttachmentImportTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "salesforceAttachmentImportTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SalesforceAttachmentImportTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SalesforceAttachmentImportTask"}}}}}, "/v2/{accountId}/salesforceattachmentimporttasks/{taskid}": {"get": {"tags": ["SalesforceAttachmentImportTasksV2"], "operationId": "SalesforceAttachmentImportTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SalesforceAttachmentImportTask"}}}}}, "/v201411/salesforcecontentdocumentexporttasks": {"post": {"tags": ["SalesforceContentDocumentExportTasks"], "operationId": "SalesforceContentDocumentExportTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "salesforceContentDocumentExportTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SalesforceContentDocumentExportTask"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SalesforceContentDocumentExportTask"}}}}}, "/v201411/salesforcecontentdocumentexporttasks/{taskid}": {"get": {"tags": ["SalesforceContentDocumentExportTasks"], "operationId": "SalesforceContentDocumentExportTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SalesforceContentDocumentExportTask"}}}}}, "/v2/{accountId}/salesforcecontentdocumentexporttasks": {"post": {"tags": ["SalesforceContentDocumentExportTasksV2"], "operationId": "SalesforceContentDocumentExportTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "salesforceContentDocumentExportTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SalesforceContentDocumentExportTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SalesforceContentDocumentExportTask"}}}}}, "/v2/{accountId}/salesforcecontentdocumentexporttasks/{taskid}": {"get": {"tags": ["SalesforceContentDocumentExportTasksV2"], "operationId": "SalesforceContentDocumentExportTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SalesforceContentDocumentExportTask"}}}}}, "/v201411/salesforcecontentdocumentimporttasks": {"post": {"tags": ["SalesforceContentDocumentImportTasks"], "operationId": "SalesforceContentDocumentImportTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "salesforceContentDocumentImportTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SalesforceContentDocumentImportTask"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SalesforceContentDocumentImportTask"}}}}}, "/v201411/salesforcecontentdocumentimporttasks/{taskid}": {"get": {"tags": ["SalesforceContentDocumentImportTasks"], "operationId": "SalesforceContentDocumentImportTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SalesforceContentDocumentImportTask"}}}}}, "/v2/{accountId}/salesforcecontentdocumentimporttasks": {"post": {"tags": ["SalesforceContentDocumentImportTasksV2"], "operationId": "SalesforceContentDocumentImportTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "salesforceContentDocumentImportTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SalesforceContentDocumentImportTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SalesforceContentDocumentImportTask"}}}}}, "/v2/{accountId}/salesforcecontentdocumentimporttasks/{taskid}": {"get": {"tags": ["SalesforceContentDocumentImportTasksV2"], "operationId": "SalesforceContentDocumentImportTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SalesforceContentDocumentImportTask"}}}}}, "/v201411/scim/schemas": {"get": {"tags": ["SchemasScim"], "operationId": "SchemasScim_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/v201411/scim/schemas/{schema}": {"get": {"tags": ["SchemasScim"], "operationId": "SchemasScim_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "schema", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/v201411/scim/serviceproviderconfig": {"get": {"tags": ["ServiceProviderConfig"], "operationId": "ServiceProviderConfig_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/v201411/sharelinks/{id}": {"get": {"tags": ["ShareLinks"], "operationId": "ShareLinks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ShareLink"}}}}, "put": {"tags": ["ShareLinks"], "operationId": "ShareLinks_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "shareLink", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ShareLink"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ShareLink"}}}}, "delete": {"tags": ["ShareLinks"], "operationId": "ShareLinks_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["ShareLinks"], "operationId": "ShareLinks_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "shareLink", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ShareLink"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ShareLink"}}}}}, "/v201411/sharelinks": {"post": {"tags": ["ShareLinks"], "operationId": "ShareLinks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "shareLink", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ShareLink"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ShareLink"}}}}}, "/v2/{accountId}/sharelinks/{id}": {"get": {"tags": ["ShareLinksV2"], "operationId": "ShareLinksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ShareLink"}}}}, "put": {"tags": ["ShareLinksV2"], "operationId": "ShareLinksV2_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "shareLink", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ShareLink"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ShareLink"}}}}, "delete": {"tags": ["ShareLinksV2"], "operationId": "ShareLinksV2_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["ShareLinksV2"], "operationId": "ShareLinksV2_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "shareLink", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ShareLink"}}, {"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ShareLink"}}}}}, "/v2/{accountId}/sharelinks": {"post": {"tags": ["ShareLinksV2"], "operationId": "ShareLinksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "shareLink", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ShareLink"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ShareLink"}}}}}, "/v201411/signaturetasks": {"post": {"tags": ["SignatureTasks"], "operationId": "SignatureTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "signatureTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SignatureTask"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SignatureTask"}}}}}, "/v201411/signaturetasks/{taskid}": {"get": {"tags": ["SignatureTasks"], "operationId": "SignatureTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SignatureTask"}}}}, "delete": {"tags": ["SignatureTasks"], "operationId": "SignatureTasks_Delete", "consumes": [], "produces": [], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"204": {"description": "No Content"}}}}, "/v2/{accountId}/signaturetasks": {"post": {"tags": ["SignatureTasksV2"], "operationId": "SignatureTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "signatureTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SignatureTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SignatureTask"}}}}}, "/v2/{accountId}/signaturetasks/{taskid}": {"get": {"tags": ["SignatureTasksV2"], "operationId": "SignatureTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SignatureTask"}}}}, "delete": {"tags": ["SignatureTasksV2"], "operationId": "SignatureTasksV2_Delete", "consumes": [], "produces": [], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"204": {"description": "No Content"}}}}, "/SiteWarmUp/WarmUp": {"get": {"tags": ["SiteWarmUpApi"], "operationId": "SiteWarmUpApi_WarmUp", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "hcOptions", "in": "query", "required": true, "type": "object"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/SiteWarmUp/Ping": {"get": {"tags": ["SiteWarmUpApi"], "operationId": "SiteWarmUpApi_Ping", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/v201411/splitdocumenttasks": {"post": {"tags": ["SplitDocumentTasks"], "operationId": "SplitDocumentTasks_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "splitDocumentTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SplitDocumentTask"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SplitDocumentTask"}}}}}, "/v201411/splitdocumenttasks/{taskid}": {"get": {"tags": ["SplitDocumentTasks"], "operationId": "SplitDocumentTasks_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SplitDocumentTask"}}}}}, "/v2/{accountId}/splitdocumenttasks": {"post": {"tags": ["SplitDocumentTasksV2"], "operationId": "SplitDocumentTasksV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "splitDocumentTask", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SplitDocumentTask"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SplitDocumentTask"}}}}}, "/v2/{accountId}/splitdocumenttasks/{taskid}": {"get": {"tags": ["SplitDocumentTasksV2"], "operationId": "SplitDocumentTasksV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "taskid", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/SplitDocumentTask"}}}}}, "/v201411/users": {"get": {"tags": ["Users"], "operationId": "Users_GetUsers", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[User]"}}}}, "post": {"tags": ["Users"], "operationId": "Users_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/User"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/User"}}}}}, "/v201411/users/{id}": {"get": {"tags": ["Users"], "operationId": "Users_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/User"}}}}, "put": {"tags": ["Users"], "operationId": "Users_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/User"}}, {"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/User"}}}}, "delete": {"tags": ["Users"], "operationId": "Users_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["Users"], "operationId": "Users_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/User"}}, {"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/User"}}}}}, "/v201411/users/current": {"get": {"tags": ["Users"], "operationId": "Users_GetCurrent", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/User"}}}}, "put": {"tags": ["Users"], "operationId": "Users_PutCurrent", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/User"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/User"}}}}, "patch": {"tags": ["Users"], "operationId": "Users_PatchCurrent", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/User"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/User"}}}}}, "/v201411/users/{id}/workitems": {"get": {"tags": ["Users"], "operationId": "Users_GetWorkflowActivities", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[WorkItem]"}}}}}, "/v201411/users/current/workitems": {"get": {"tags": ["Users"], "operationId": "Users_GetCurrentWorkflowActivities", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[WorkItem]"}}}}}, "/v201411/users/{id}/managedusers": {"get": {"tags": ["Users"], "operationId": "Users_GetManagedUsers", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[User]"}}}}}, "/v201411/users/current/managedusers": {"get": {"tags": ["Users"], "operationId": "Users_GetCurrentManagedUsers", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[User]"}}}}}, "/v201411/users/current/recentdocuments": {"get": {"tags": ["Users"], "operationId": "Users_GetRecentDocumentsCurrent", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v201411/users/{id}/workflowqueues": {"get": {"tags": ["Users"], "operationId": "Users_GetWorkflowQueues", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[WorkflowQueue]"}}}}}, "/v201411/users/current/workflowqueues": {"get": {"tags": ["Users"], "operationId": "Users_GetCurrentWorkflowQueues", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[WorkflowQueue]"}}}}}, "/v201411/users/{id}/groups": {"get": {"tags": ["Users"], "operationId": "Users_GetGroups", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Group]"}}}}}, "/v201411/users/current/groups": {"get": {"tags": ["Users"], "operationId": "Users_GetCurrentGroups", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Group]"}}}}}, "/v201411/users/current/watcheddocuments": {"get": {"tags": ["Users"], "operationId": "Users_GetCurrentWatchedDocuments", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v201411/users/current/watcheddocumentsprocesstrackingactivities": {"get": {"tags": ["Users"], "operationId": "Users_GetCurrentWatchedDocumentsProcessTrackingActivities", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[DocumentProcessTrackingActivity]"}}}}}, "/v201411/users/{id}/permissionsets": {"get": {"tags": ["Users"], "operationId": "Users_GetPermissionSetsForUser", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[PermissionSet]"}}}}}, "/v201411/users/current/permissionsets": {"get": {"tags": ["Users"], "operationId": "Users_GetPermissionSetsCurrent", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[PermissionSet]"}}}}}, "/v201411/scim/users": {"get": {"tags": ["UsersScim"], "operationId": "UsersScim_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "filter", "in": "query", "required": false, "type": "string"}, {"name": "startIndex", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "count", "in": "query", "required": false, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ListResponseScim[UserScim]"}}}}, "post": {"tags": ["UsersScim"], "operationId": "UsersScim_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UserScim"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/UserScim"}}}}}, "/v201411/scim/users/{id}": {"get": {"tags": ["UsersScim"], "operationId": "UsersScim_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/UserScim"}}}}, "put": {"tags": ["UsersScim"], "operationId": "UsersScim_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UserScim"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/UserScim"}}}}, "delete": {"tags": ["UsersScim"], "operationId": "UsersScim_Delete", "consumes": [], "produces": [], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"204": {"description": "No Content"}}}, "patch": {"tags": ["UsersScim"], "operationId": "UsersScim_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "patchRequest", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PatchRequestScim"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/UserScim"}}}}}, "/v201411/workflowdefinitions": {"get": {"tags": ["WorkflowDefinitions"], "operationId": "WorkflowDefinitions_GetAll", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[WorkflowDefinition]"}}}}}, "/v201411/workflowdefinitions/{id}": {"get": {"tags": ["WorkflowDefinitions"], "operationId": "WorkflowDefinitions_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/WorkflowDefinition"}}}}}, "/v2/{accountId}/workflowdefinitions": {"get": {"tags": ["WorkflowDefinitionsV2"], "operationId": "WorkflowDefinitionsV2_GetAll", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[WorkflowDefinition]"}}}}}, "/v2/{accountId}/workflowdefinitions/{id}": {"get": {"tags": ["WorkflowDefinitionsV2"], "operationId": "WorkflowDefinitionsV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/WorkflowDefinition"}}}}}, "/v201411/workflowqueues/{id}": {"get": {"tags": ["WorkflowQueues"], "operationId": "WorkflowQueues_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/WorkflowQueue"}}}}}, "/v201411/workflowqueues/{id}/workitems": {"get": {"tags": ["WorkflowQueues"], "operationId": "WorkflowQueues_GetWorkItems", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[WorkItem]"}}}}}, "/v2/{accountId}/workflowqueues/{id}": {"get": {"tags": ["WorkflowQueuesV2"], "operationId": "WorkflowQueuesV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/WorkflowQueue"}}}}}, "/v2/{accountId}/workflowqueues/{id}/workitems": {"get": {"tags": ["WorkflowQueuesV2"], "operationId": "WorkflowQueuesV2_GetWorkItems", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[WorkItem]"}}}}}, "/v201411/workflows/{instanceId}": {"get": {"tags": ["Workflows"], "operationId": "Workflows_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "instanceId", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Workflow"}}}}, "delete": {"tags": ["Workflows"], "operationId": "Workflows_Delete", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "instanceId", "in": "path", "required": true, "type": "string", "format": "uuid"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Workflow"}}}}}, "/v201411/workflows": {"post": {"tags": ["Workflows"], "operationId": "Workflows_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "workflow", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Workflow"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Workflow"}}}}}, "/v201411/workflows/{instanceId}/signal": {"post": {"tags": ["Workflows"], "operationId": "Workflows_PostSignal", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "instanceId", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "value", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Signal"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Workflow"}}}}}, "/v2/{accountId}/workflows/{instanceId}": {"get": {"tags": ["WorkflowsV2"], "operationId": "WorkflowsV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "instanceId", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Workflow"}}}}, "delete": {"tags": ["WorkflowsV2"], "operationId": "WorkflowsV2_Delete", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "instanceId", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Workflow"}}}}}, "/v2/{accountId}/workflows": {"post": {"tags": ["WorkflowsV2"], "operationId": "WorkflowsV2_Post", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "workflow", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Workflow"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Workflow"}}}}}, "/v2/{accountId}/workflows/{instanceId}/signal": {"post": {"tags": ["WorkflowsV2"], "operationId": "WorkflowsV2_PostSignal", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "instanceId", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "value", "in": "body", "required": true, "schema": {"$ref": "#/definitions/Signal"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Workflow"}}}}}, "/v201411/workitems/{id}/documents": {"get": {"tags": ["WorkItems"], "operationId": "WorkItems_GetDocuments", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v201411/workitems/{id}": {"get": {"tags": ["WorkItems"], "operationId": "WorkItems_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/WorkItem"}}}}, "put": {"tags": ["WorkItems"], "operationId": "WorkItems_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "workItem", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WorkItem"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/WorkItem"}}}}, "patch": {"tags": ["WorkItems"], "operationId": "WorkItems_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "workItem", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WorkItem"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/WorkItem"}}}}}, "/v2/{accountId}/workitems/{id}/documents": {"get": {"tags": ["WorkItemsV2"], "operationId": "WorkItemsV2_GetDocuments", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}, {"name": "pageSortParams.offset", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSortParams.sortProperty", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.sortDirection", "in": "query", "required": false, "type": "integer", "format": "int32", "enum": [0, 1]}, {"name": "pageSortParams.filter", "in": "query", "required": false, "type": "string"}, {"name": "pageSortParams.filterExact", "in": "query", "required": false, "type": "boolean"}, {"name": "pageSortParams.caseInsensitive", "in": "query", "required": false, "type": "boolean"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiCollection[Document]"}}}}}, "/v2/{accountId}/workitems/{id}": {"get": {"tags": ["WorkItemsV2"], "operationId": "WorkItemsV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "expand", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/WorkItem"}}}}, "put": {"tags": ["WorkItemsV2"], "operationId": "WorkItemsV2_Put", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "workItem", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WorkItem"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/WorkItem"}}}}, "patch": {"tags": ["WorkItemsV2"], "operationId": "WorkItemsV2_Patch", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "workItem", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WorkItem"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/WorkItem"}}}}}, "/v2/{accountId}/workitems/{id}/history": {"get": {"tags": ["WorkItemsV2"], "operationId": "WorkItemsV2_Get", "consumes": [], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ActivityHistory"}}}}}, "/v2/{accountId}/workitems/{id}/assign": {"patch": {"tags": ["WorkItemsV2"], "operationId": "WorkItemsV2_PatchAssignment", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "workItem", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WorkItem"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/WorkItem"}}}}}, "/v2/{accountId}/workitems/{id}/unassign": {"patch": {"tags": ["WorkItemsV2"], "operationId": "WorkItemsV2_PatchUnassignment", "consumes": ["application/json", "text/json", "application/scim+json", "application/x-www-form-urlencoded"], "produces": ["application/json", "text/json", "application/scim+json"], "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uuid"}, {"name": "workItem", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WorkItem"}}, {"name": "accountId", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/WorkItem"}}}}}}, "definitions": {"Account": {"type": "object", "properties": {"Id": {"type": "string"}, "Name": {"type": "string"}, "Type": {"type": "string"}, "DefaultCulture": {"type": "string"}, "DefaultTimeZone": {"type": "string"}, "AttributeGroups": {"$ref": "#/definitions/ApiCollection[AttributeGroup]"}, "SalesForceOrgId": {"type": "string"}, "LogoUrl": {"type": "string"}, "BrandingUrl": {"type": "string"}, "Href": {"type": "string"}}}, "ApiCollection[AttributeGroup]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/AttributeGroup"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "AttributeGroup": {"type": "object", "properties": {"Name": {"type": "string"}, "Attributes": {"type": "array", "items": {"$ref": "#/definitions/BaseAttribute"}}, "IsSystem": {"type": "boolean"}, "Href": {"type": "string"}}}, "BaseAttribute": {"type": "object", "properties": {"Name": {"type": "string"}, "RepeatingAttribute": {"type": "boolean"}}}, "PageSortParams": {"type": "object", "properties": {"Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "SortProperty": {"type": "string"}, "SortDirection": {"format": "int32", "enum": [0, 1], "type": "integer"}, "Filter": {"type": "string"}, "FilterExact": {"type": "boolean"}, "CaseInsensitive": {"type": "boolean"}}}, "ApiCollection[BulkTask]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/BulkTask"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "BulkTask": {"type": "object", "properties": {"Name": {"type": "string"}, "Description": {"type": "string"}, "Status": {"type": "string"}, "CompletedDate": {"format": "date-time", "type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "CreatedBy": {"type": "string"}, "Href": {"type": "string"}}}, "BulkWorkflowTask": {"type": "object", "properties": {"Name": {"type": "string"}, "Description": {"type": "string"}, "Document": {"$ref": "#/definitions/Document"}, "WorkflowDefinitionId": {"type": "string"}, "Status": {"type": "string"}, "TotalWorkflowCount": {"format": "int32", "type": "integer"}, "SuccessWorkflowCount": {"format": "int32", "type": "integer"}, "FailureWorkflowCount": {"format": "int32", "type": "integer"}, "Outcome": {"type": "string"}, "CompletedDate": {"format": "date-time", "type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "CreatedBy": {"type": "string"}, "Items": {"type": "array", "items": {"$ref": "#/definitions/BulkWorkflowTaskItem"}}, "Href": {"type": "string"}}}, "Document": {"type": "object", "properties": {"Name": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "CreatedBy": {"type": "string"}, "UpdatedDate": {"format": "date-time", "type": "string"}, "UpdatedBy": {"type": "string"}, "Description": {"type": "string"}, "ParentFolder": {"$ref": "#/definitions/Folder"}, "Path": {"type": "string"}, "HistoryItems": {"$ref": "#/definitions/ApiCollection[HistoryItem]"}, "AttributeGroups": {"type": "object"}, "AccessLevel": {"$ref": "#/definitions/AccessLevel"}, "PageCount": {"format": "int32", "type": "integer"}, "EosParentInfo": {"$ref": "#/definitions/EosInfo"}, "Lock": {"$ref": "#/definitions/DocumentLock"}, "LockStatus": {"type": "string"}, "IsInTrash": {"type": "boolean"}, "Version": {"type": "string"}, "PreviewUrl": {"type": "string"}, "Versions": {"$ref": "#/definitions/ApiCollection[Document]"}, "ShareLinks": {"$ref": "#/definitions/ApiCollection[ShareLink]"}, "DocumentProcessTrackingActivities": {"$ref": "#/definitions/ApiCollection[DocumentProcessTrackingActivity]"}, "DocumentReminders": {"$ref": "#/definitions/ApiCollection[DocumentReminder]"}, "RelatedDocuments": {"$ref": "#/definitions/ApiCollection[Document]"}, "AttachedDocuments": {"$ref": "#/definitions/ApiCollection[Document]"}, "WorkItems": {"$ref": "#/definitions/ApiCollection[WorkItem]"}, "DownloadDocumentHref": {"type": "string", "readOnly": true}, "Score": {"format": "float", "type": "number"}, "Uid": {"type": "string"}, "NativeFileSize": {"format": "int64", "type": "integer"}, "PdfFileSize": {"format": "int64", "type": "integer"}, "ContentCreatedDate": {"format": "date-time", "type": "string"}, "Extension": {"type": "string"}, "Href": {"type": "string"}}}, "BulkWorkflowTaskItem": {"type": "object", "properties": {"Id": {"type": "string"}, "Status": {"type": "string"}, "Workflow": {"$ref": "#/definitions/Workflow"}}}, "Folder": {"type": "object", "properties": {"Name": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "CreatedBy": {"type": "string"}, "UpdatedDate": {"format": "date-time", "type": "string"}, "UpdatedBy": {"type": "string"}, "Description": {"type": "string"}, "ParentFolder": {"$ref": "#/definitions/Folder"}, "BrowseDocumentsUrl": {"type": "string"}, "AccessLevel": {"$ref": "#/definitions/AccessLevel"}, "Documents": {"$ref": "#/definitions/ApiCollection[Document]"}, "Folders": {"$ref": "#/definitions/ApiCollection[Folder]"}, "Path": {"type": "string"}, "AttributeGroups": {"type": "object"}, "PropagateAttributeGroupsToChildren": {"type": "boolean"}, "EosInfo": {"$ref": "#/definitions/EosInfo"}, "EosParentInfo": {"$ref": "#/definitions/EosInfo"}, "ShareLinks": {"$ref": "#/definitions/ApiCollection[ShareLink]"}, "Security": {"$ref": "#/definitions/Securities"}, "CreateDocumentHref": {"type": "string", "readOnly": true}, "Href": {"type": "string"}}}, "ApiCollection[HistoryItem]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/HistoryItem"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "AccessLevel": {"type": "object", "properties": {"See": {"type": "boolean"}, "Read": {"type": "boolean"}, "Write": {"type": "boolean"}, "Move": {"type": "boolean"}, "Create": {"type": "boolean"}, "SetAccess": {"type": "boolean"}}}, "EosInfo": {"type": "object", "properties": {"Name": {"type": "string"}, "Path": {"type": "string"}, "ObjectId": {"type": "string"}, "ObjectType": {"type": "string"}, "Folder": {"type": "string"}}}, "DocumentLock": {"type": "object", "properties": {"IsLocked": {"type": "boolean"}, "LockDate": {"format": "date-time", "type": "string"}, "Type": {"type": "string"}, "Comment": {"type": "string"}, "LockOwner": {"$ref": "#/definitions/User"}, "CheckInHref": {"type": "string"}, "SignatureHref": {"type": "string"}, "Href": {"type": "string"}}}, "ApiCollection[Document]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/Document"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "ApiCollection[ShareLink]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/ShareLink"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "ApiCollection[DocumentProcessTrackingActivity]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/DocumentProcessTrackingActivity"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "ApiCollection[DocumentReminder]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/DocumentReminder"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "ApiCollection[WorkItem]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/WorkItem"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "Workflow": {"type": "object", "properties": {"Name": {"type": "string"}, "StartDate": {"format": "date-time", "type": "string"}, "EndDate": {"format": "date-time", "type": "string"}, "Status": {"type": "string"}, "Info": {"type": "string"}, "Params": {"type": "string"}, "WorkflowDocuments": {"$ref": "#/definitions/ApiCollection[Document]"}, "Href": {"type": "string"}}}, "ApiCollection[Folder]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/Folder"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "Securities": {"type": "object", "properties": {"Roles": {"type": "array", "items": {"$ref": "#/definitions/Security[String]"}}, "Groups": {"type": "array", "items": {"$ref": "#/definitions/Security[Group]"}}, "Users": {"type": "array", "items": {"$ref": "#/definitions/Security[User]"}}}}, "HistoryItem": {"type": "object", "properties": {"UserEmail": {"type": "string"}, "Action": {"type": "string"}, "MoreInfo": {"type": "string"}, "Comment": {"type": "string"}, "User": {"$ref": "#/definitions/User"}, "RelatedDocument": {"$ref": "#/definitions/Document"}, "CreatedDate": {"format": "date-time", "type": "string"}}}, "User": {"type": "object", "properties": {"UserName": {"type": "string"}, "Email": {"type": "string"}, "FirstName": {"type": "string"}, "LastName": {"type": "string"}, "Address1": {"type": "string"}, "Address2": {"type": "string"}, "Address3": {"type": "string"}, "City": {"type": "string"}, "State": {"type": "string"}, "ZipCode": {"type": "string"}, "Title": {"type": "string"}, "Company": {"type": "string"}, "Department": {"type": "string"}, "EnableStartDate": {"format": "date-time", "type": "string"}, "EnableEndDate": {"format": "date-time", "type": "string"}, "FaxNumber": {"type": "string"}, "PhoneNumber": {"type": "string"}, "ManagedBy": {"$ref": "#/definitions/User"}, "ManagedUsers": {"$ref": "#/definitions/ApiCollection[User]"}, "WorkItems": {"$ref": "#/definitions/ApiCollection[WorkItem]"}, "RecentDocuments": {"$ref": "#/definitions/ApiCollection[Document]"}, "WatchedDocumentsProcessTrackingActivities": {"$ref": "#/definitions/ApiCollection[DocumentProcessTrackingActivity]"}, "WorkflowQueues": {"$ref": "#/definitions/ApiCollection[WorkflowQueue]"}, "Groups": {"$ref": "#/definitions/ApiCollection[Group]"}, "SendActivationEmail": {"type": "boolean"}, "Role": {"type": "string"}, "PermissionSets": {"$ref": "#/definitions/ApiCollection[PermissionSet]"}, "Persona": {"$ref": "#/definitions/Group"}, "PortalOnly": {"type": "boolean"}, "Country": {"type": "string"}, "WatchedDocuments": {"$ref": "#/definitions/ApiCollection[Document]"}, "CreatedDate": {"format": "date-time", "type": "string"}, "UpdatedDate": {"format": "date-time", "type": "string"}, "ExemptFromUserSync": {"type": "boolean"}, "Href": {"type": "string"}}}, "ShareLink": {"type": "object", "properties": {"Url": {"type": "string"}, "ExpirationDate": {"format": "date-time", "type": "string"}, "AllowNativeDownload": {"type": "boolean"}, "AllowPdfDownload": {"type": "boolean"}, "PreviewBehavior": {"type": "string"}, "CreatedBy": {"type": "string"}, "UpdatedBy": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "UpdatedDate": {"format": "date-time", "type": "string"}, "Folder": {"$ref": "#/definitions/Folder"}, "Document": {"$ref": "#/definitions/Document"}, "Href": {"type": "string"}}}, "DocumentProcessTrackingActivity": {"type": "object", "properties": {"Name": {"type": "string"}, "TypeName": {"type": "string"}, "Status": {"type": "string"}, "Output": {"type": "string"}, "StageName": {"type": "string"}, "StartDate": {"format": "date-time", "type": "string"}, "EndDate": {"format": "date-time", "type": "string"}, "DueDate": {"format": "date-time", "type": "string"}, "ProcessUid": {"format": "uuid", "type": "string", "example": "00000000-0000-0000-0000-000000000000"}, "ProcessName": {"type": "string"}, "Document": {"$ref": "#/definitions/Document"}, "UserActions": {"$ref": "#/definitions/ApiCollection[UserAction]"}, "Href": {"type": "string"}}}, "DocumentReminder": {"type": "object", "properties": {"Name": {"type": "string"}, "ReminderDate": {"format": "date-time", "type": "string"}, "RecipientContacts": {"$ref": "#/definitions/ApiCollection[Contact]"}, "RecipientUsers": {"$ref": "#/definitions/ApiCollection[User]"}, "RecipientGroups": {"$ref": "#/definitions/ApiCollection[Group]"}, "RecipientAdHocEmails": {"type": "array", "items": {"type": "string"}}, "Document": {"$ref": "#/definitions/Document"}, "EmailSubject": {"type": "string"}, "EmailBody": {"type": "string"}, "EmailFromAddress": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "CreatedBy": {"type": "string"}, "UpdatedDate": {"format": "date-time", "type": "string"}, "UpdatedBy": {"type": "string"}, "Href": {"type": "string"}}}, "WorkItem": {"type": "object", "properties": {"Name": {"type": "string"}, "Type": {"type": "string"}, "Assignee": {"$ref": "#/definitions/User"}, "AssignDate": {"format": "date-time", "type": "string"}, "DueDate": {"format": "date-time", "type": "string"}, "Documents": {"$ref": "#/definitions/ApiCollection[Document]"}, "AssigneeInstructions": {"type": "string"}, "Workflow": {"$ref": "#/definitions/Workflow"}, "Selections": {"$ref": "#/definitions/WorkItemSelection"}, "Selected": {"$ref": "#/definitions/WorkItemSelection"}, "CreatedDate": {"format": "date-time", "type": "string"}, "WorkItemUrl": {"type": "string"}, "Href": {"type": "string"}}}, "Security[String]": {"type": "object", "properties": {"Item": {"type": "string"}, "AccessType": {"type": "string"}}}, "Security[Group]": {"type": "object", "properties": {"Item": {"$ref": "#/definitions/Group"}, "AccessType": {"type": "string"}}}, "Security[User]": {"type": "object", "properties": {"Item": {"$ref": "#/definitions/User"}, "AccessType": {"type": "string"}}}, "ApiCollection[User]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/User"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "ApiCollection[WorkflowQueue]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/WorkflowQueue"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "ApiCollection[Group]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/Group"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "ApiCollection[PermissionSet]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/PermissionSet"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "Group": {"type": "object", "properties": {"Name": {"type": "string"}, "Description": {"type": "string"}, "GroupType": {"type": "string"}, "GroupMembers": {"$ref": "#/definitions/ApiCollection[User]"}, "CreatedDate": {"format": "date-time", "type": "string"}, "UpdatedDate": {"format": "date-time", "type": "string"}, "Href": {"type": "string"}}}, "ApiCollection[UserAction]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/UserAction"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "ApiCollection[Contact]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/Contact"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "WorkItemSelection": {"type": "object", "properties": {"Items": {"type": "array", "items": {"type": "object"}}, "Comment": {"type": "string"}, "Allowed": {"type": "string"}}}, "WorkflowQueue": {"type": "object", "properties": {"Name": {"type": "string"}, "Description": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "UpdatedDate": {"format": "date-time", "type": "string"}, "WorkItems": {"$ref": "#/definitions/ApiCollection[WorkItem]"}, "Href": {"type": "string"}}}, "PermissionSet": {"type": "object", "properties": {"Name": {"type": "string"}, "Permissions": {"type": "array", "items": {"type": "string"}}, "Href": {"type": "string"}}}, "UserAction": {"type": "object", "properties": {"AssignedTo": {"$ref": "#/definitions/UserInfo"}, "WorkflowQueue": {"$ref": "#/definitions/WorkflowQueue"}, "Action": {"type": "string"}, "Output": {"type": "string"}, "Comments": {"type": "string"}, "AssignedBy": {"$ref": "#/definitions/UserInfo"}, "ActionBy": {"$ref": "#/definitions/UserInfo"}, "StartDate": {"format": "date-time", "type": "string"}, "UpdatedDate": {"format": "date-time", "type": "string"}, "EndDate": {"format": "date-time", "type": "string"}}}, "Contact": {"type": "object", "properties": {"Email": {"type": "string"}, "FirstName": {"type": "string"}, "LastName": {"type": "string"}, "Address1": {"type": "string"}, "Address2": {"type": "string"}, "Address3": {"type": "string"}, "City": {"type": "string"}, "State": {"type": "string"}, "ZipCode": {"type": "string"}, "Country": {"type": "string"}, "Title": {"type": "string"}, "Company": {"type": "string"}, "Department": {"type": "string"}, "FaxNumber": {"type": "string"}, "PhoneNumber": {"type": "string"}, "Shared": {"type": "boolean"}, "Groups": {"$ref": "#/definitions/ApiCollection[Group]"}, "Href": {"type": "string"}}}, "UserInfo": {"type": "object", "properties": {"Email": {"type": "string"}, "FullName": {"type": "string"}, "User": {"$ref": "#/definitions/User"}}}, "ChangeSecurityTask": {"type": "object", "properties": {"Status": {"type": "string"}, "Security": {"$ref": "#/definitions/Securities"}, "Folder": {"$ref": "#/definitions/Folder"}, "Href": {"type": "string"}}}, "Clause": {"type": "object", "properties": {"Name": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "CreatedBy": {"type": "string"}, "UpdatedDate": {"format": "date-time", "type": "string"}, "UpdatedBy": {"type": "string"}, "Description": {"type": "string"}, "ParentFolder": {"$ref": "#/definitions/Folder"}, "Options": {"$ref": "#/definitions/ApiCollection[Option]"}, "AttributeGroups": {"type": "object"}, "Href": {"type": "string"}}}, "ApiCollection[Option]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/Option"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "Option": {"type": "object", "properties": {"Name": {"type": "string"}, "Text": {"type": "string"}, "Notes": {"type": "string"}, "Clause": {"$ref": "#/definitions/Clause"}, "Href": {"type": "string"}}}, "ApiCollection[Clause]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/Clause"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "CopyTask": {"type": "object", "properties": {"DocumentsToCopy": {"type": "array", "items": {"$ref": "#/definitions/Document"}}, "FoldersToCopy": {"type": "array", "items": {"$ref": "#/definitions/Folder"}}, "DocumentResults": {"type": "array", "items": {"$ref": "#/definitions/Document"}}, "FolderResults": {"type": "array", "items": {"$ref": "#/definitions/Folder"}}, "FailedDocuments": {"type": "array", "items": {"$ref": "#/definitions/Failed[Document]"}}, "FailedFolders": {"type": "array", "items": {"$ref": "#/definitions/Failed[Folder]"}}, "ResultDocument": {"$ref": "#/definitions/Document"}, "Message": {"type": "string"}, "DownloadUrl": {"type": "string"}, "DestinationFolder": {"$ref": "#/definitions/Folder"}, "DestinationDocumentName": {"type": "string"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "Failed[Document]": {"type": "object", "properties": {"Source": {"$ref": "#/definitions/Document"}, "Reason": {"type": "string"}}}, "Failed[Folder]": {"type": "object", "properties": {"Source": {"$ref": "#/definitions/Folder"}, "Reason": {"type": "string"}}}, "ApiCollection[DocLauncherConfiguration]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/DocLauncherConfiguration"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "DocLauncherConfiguration": {"type": "object", "properties": {"Name": {"type": "string"}, "DestinationFolder": {"$ref": "#/definitions/Folder"}, "Href": {"type": "string"}}}, "DocLauncherTask": {"type": "object", "properties": {"Data": {"type": "string"}, "DataType": {"type": "string"}, "DestinationFolder": {"$ref": "#/definitions/Folder"}, "DocLauncherConfiguration": {"$ref": "#/definitions/DocLauncherConfiguration"}, "UrlExpirationDate": {"format": "date-time", "type": "string"}, "DocLauncherResultUrl": {"type": "string"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "DocumentCompareTask": {"type": "object", "properties": {"OriginalDocument": {"$ref": "#/definitions/Document"}, "RevisedDocument": {"$ref": "#/definitions/Document"}, "Message": {"type": "string"}, "DownloadUrl": {"type": "string"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "DocumentMergeTask": {"type": "object", "properties": {"DocumentsToMerge": {"type": "array", "items": {"$ref": "#/definitions/Document"}}, "DeleteOriginals": {"type": "boolean"}, "ResultDocument": {"$ref": "#/definitions/Document"}, "Message": {"type": "string"}, "DownloadUrl": {"type": "string"}, "DestinationFolder": {"$ref": "#/definitions/Folder"}, "DestinationDocumentName": {"type": "string"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "DocumentPreview": {"type": "object", "properties": {"Document": {"$ref": "#/definitions/Document"}, "PreviewReason": {"format": "int32", "enum": [0, 1, 2, -8, -7, -6, -5, -4, -3, -2, -1], "type": "integer"}, "IsPreviewAvailable": {"type": "boolean"}, "PageImagesAvailability": {"type": "array", "items": {"type": "boolean"}}, "Pages": {"type": "array", "items": {"$ref": "#/definitions/PreviewPage"}}, "Href": {"type": "string"}}}, "PreviewPage": {"type": "object", "properties": {"Width": {"format": "double", "type": "number"}, "Height": {"format": "double", "type": "number"}, "Rotate": {"format": "int32", "type": "integer"}, "PageOverlays": {"type": "array", "items": {"$ref": "#/definitions/PageOverlay"}}, "OverlayStartIndex": {"format": "int32", "type": "integer"}, "OverlayEndIndex": {"format": "int32", "type": "integer"}, "ImageStatus": {"format": "int32", "enum": [0, 1, 2], "type": "integer"}, "PageIndex": {"format": "int32", "type": "integer"}}}, "PageOverlay": {"type": "object", "properties": {"t": {"format": "double", "type": "number"}, "r": {"format": "double", "type": "number"}, "b": {"format": "double", "type": "number"}, "l": {"format": "double", "type": "number"}, "ch": {"type": "array", "items": {"type": "string"}}}}, "ApiCollection[PreviewPage]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/PreviewPage"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "ApiCollection[PreviewPageStatus]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/PreviewPageStatus"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "PreviewPageStatus": {"type": "object", "properties": {"ImageStatus": {"format": "int32", "enum": [0, 1, 2], "type": "integer"}, "PageIndex": {"format": "int32", "type": "integer"}}}, "DocumentProcessTrackingTask": {"type": "object", "properties": {"Result": {"$ref": "#/definitions/ApiCollection[DocumentProcessTrackingActivity]"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "DocumentProcessTrackingActivityTask": {"type": "object", "properties": {"Folder": {"$ref": "#/definitions/Folder"}, "IncludeSubFolders": {"type": "boolean"}, "Result": {"$ref": "#/definitions/ApiCollection[DocumentProcessTrackingActivity]"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "DocumentProcessTrackingHistoryTask": {"type": "object", "properties": {"Document": {"$ref": "#/definitions/Document"}, "ProcessUid": {"format": "uuid", "type": "string", "example": "00000000-0000-0000-0000-000000000000"}, "IncludeCurrent": {"type": "boolean"}, "Result": {"$ref": "#/definitions/ApiCollection[DocumentProcessTrackingActivity]"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "SearchTask": {"type": "object", "properties": {"Status": {"type": "string"}, "AllWords": {"type": "string"}, "AnyWords": {"type": "string"}, "WithoutWords": {"type": "string"}, "Phrase": {"type": "string"}, "InFolder": {"$ref": "#/definitions/Folder"}, "IncludeSubFolders": {"type": "boolean"}, "IsInTrash": {"type": "boolean"}, "Title": {"type": "string"}, "Description": {"type": "string"}, "UpdatedBy": {"$ref": "#/definitions/User"}, "Result": {"$ref": "#/definitions/ApiCollection[BasicSpringCMObject]"}, "Href": {"type": "string"}}}, "ApiCollection[BasicSpringCMObject]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/BasicSpringCMObject"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "BasicSpringCMObject": {"type": "object", "properties": {"Href": {"type": "string"}}}, "DocumentSearchTask": {"type": "object", "properties": {"Content": {"type": "string"}, "Extensions": {"type": "string"}, "UpdatedOnOrAfter": {"format": "date-time", "type": "string"}, "UpdatedOnOrBefore": {"format": "date-time", "type": "string"}, "AnyAttributeField": {"type": "string"}, "AttributeGroups": {"type": "array", "items": {"$ref": "#/definitions/AttributeGroupSearchTerm"}}, "AttributeFields": {"type": "array", "items": {"$ref": "#/definitions/AttributeFieldSearchTerm"}}, "Status": {"type": "string"}, "AllWords": {"type": "string"}, "AnyWords": {"type": "string"}, "WithoutWords": {"type": "string"}, "Phrase": {"type": "string"}, "InFolder": {"$ref": "#/definitions/Folder"}, "IncludeSubFolders": {"type": "boolean"}, "IsInTrash": {"type": "boolean"}, "Title": {"type": "string"}, "Description": {"type": "string"}, "UpdatedBy": {"$ref": "#/definitions/User"}, "Result": {"$ref": "#/definitions/ApiCollection[BasicSpringCMObject]"}, "Href": {"type": "string"}}}, "AttributeGroupSearchTerm": {"type": "object", "properties": {"Group": {"type": "string"}, "Exclude": {"type": "boolean"}}}, "AttributeFieldSearchTerm": {"type": "object", "properties": {"Group": {"type": "string"}, "Field": {"type": "string"}, "Value": {"type": "string"}, "MinValue": {"type": "string"}, "MaxValue": {"type": "string"}, "Operator": {"format": "int32", "enum": [0, 1, 2, 3, 4, 5], "type": "integer"}}}, "DocumentsLoadTask": {"type": "object", "properties": {"Documents": {"type": "array", "items": {"$ref": "#/definitions/Document"}}, "Result": {"$ref": "#/definitions/ApiCollection[Document]"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "Review": {"type": "object", "properties": {"Sender": {"$ref": "#/definitions/User"}, "Documents": {"$ref": "#/definitions/ApiCollection[Document]"}, "UploadedDocuments": {"$ref": "#/definitions/ApiCollection[Document]"}, "Recipient": {"$ref": "#/definitions/Reviewer"}, "Status": {"type": "string"}, "DueDate": {"format": "date-time", "type": "string"}, "EmailMessage": {"type": "string"}, "EmailSubject": {"type": "string"}, "Comment": {"type": "string"}, "AlsoNotify": {"type": "array", "items": {"$ref": "#/definitions/Reviewer"}}, "AddSignature": {"type": "boolean"}, "HideDueDateFromEmail": {"type": "boolean"}, "EmailAppearance": {"format": "int32", "type": "integer"}, "CreatedDate": {"format": "date-time", "type": "string"}, "Href": {"type": "string"}}}, "Reviewer": {"type": "object", "properties": {"Name": {"type": "string"}, "Email": {"type": "string"}}}, "DocumentXmlMergeTask": {"type": "object", "properties": {"TemplateDocument": {"$ref": "#/definitions/Document"}, "DataXml": {"type": "string"}, "AttributeDocument": {"$ref": "#/definitions/Document"}, "ContentDocument": {"$ref": "#/definitions/Document"}, "ResultDocument": {"$ref": "#/definitions/Document"}, "Message": {"type": "string"}, "DownloadUrl": {"type": "string"}, "DestinationFolder": {"$ref": "#/definitions/Folder"}, "DestinationDocumentName": {"type": "string"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "ExternalReviewTask": {"type": "object", "properties": {"Sender": {"$ref": "#/definitions/User"}, "Documents": {"$ref": "#/definitions/ApiCollection[Document]"}, "Recipient": {"$ref": "#/definitions/ReviewContact"}, "AlsoNotify": {"type": "array", "items": {"$ref": "#/definitions/ReviewContact"}}, "Status": {"type": "string"}, "DueDate": {"format": "date-time", "type": "string"}, "EmailSubject": {"type": "string"}, "EmailMessage": {"type": "string"}, "Comment": {"type": "string"}, "AddSignature": {"type": "boolean"}, "Href": {"type": "string"}}}, "ReviewContact": {"type": "object", "properties": {"Name": {"type": "string"}, "Email": {"type": "string"}}}, "FolderArchiveTask": {"type": "object", "properties": {"BaseFolder": {"$ref": "#/definitions/Folder"}, "DocumentsToZip": {"$ref": "#/definitions/ApiCollection[Document]"}, "FoldersToZip": {"$ref": "#/definitions/ApiCollection[Folder]"}, "ArchiveSize": {"format": "int64", "type": "integer"}, "ResultDocument": {"$ref": "#/definitions/Document"}, "Message": {"type": "string"}, "DownloadUrl": {"type": "string"}, "DestinationFolder": {"$ref": "#/definitions/Folder"}, "DestinationDocumentName": {"type": "string"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "FolderParams": {"type": "object", "properties": {"Path": {"type": "string"}, "SystemFolder": {"type": "string"}}}, "FolderSearchTask": {"type": "object", "properties": {"Status": {"type": "string"}, "AllWords": {"type": "string"}, "AnyWords": {"type": "string"}, "WithoutWords": {"type": "string"}, "Phrase": {"type": "string"}, "InFolder": {"$ref": "#/definitions/Folder"}, "IncludeSubFolders": {"type": "boolean"}, "IsInTrash": {"type": "boolean"}, "Title": {"type": "string"}, "Description": {"type": "string"}, "UpdatedBy": {"$ref": "#/definitions/User"}, "Result": {"$ref": "#/definitions/ApiCollection[BasicSpringCMObject]"}, "Href": {"type": "string"}}}, "ListResponseScim[GroupScim]": {"type": "object", "properties": {"schemas": {"type": "array", "items": {"type": "string"}, "readOnly": true}, "totalResults": {"format": "int32", "type": "integer"}, "startIndex": {"format": "int32", "type": "integer"}, "itemsPerPage": {"format": "int32", "type": "integer"}, "Resources": {"type": "array", "items": {"$ref": "#/definitions/GroupScim"}}}}, "GroupScim": {"type": "object", "properties": {"displayName": {"type": "string"}, "members": {"type": "array", "items": {"$ref": "#/definitions/MemberScim"}}, "schemas": {"type": "array", "items": {"type": "string"}, "readOnly": true}, "externalId": {"type": "string"}, "id": {"type": "string"}, "meta": {"$ref": "#/definitions/MetaScim"}}}, "MemberScim": {"type": "object", "properties": {"value": {"type": "string"}, "$ref": {"type": "string"}, "display": {"type": "string"}}}, "MetaScim": {"type": "object", "properties": {"resourceType": {"type": "string"}, "created": {"format": "date-time", "type": "string"}, "lastModified": {"format": "date-time", "type": "string"}, "location": {"type": "string"}}}, "PatchRequestScim": {"type": "object", "properties": {"Operations": {"type": "array", "items": {"$ref": "#/definitions/PatchOperationScim"}}}}, "PatchOperationScim": {"type": "object", "properties": {"op": {"type": "string"}, "value": {"type": "object"}, "path": {"type": "string"}}}, "MailingList": {"type": "object", "properties": {"Name": {"type": "string"}, "Description": {"type": "string"}, "GroupMembers": {"$ref": "#/definitions/ApiCollection[User]"}, "CreatedDate": {"format": "date-time", "type": "string"}, "UpdatedDate": {"format": "date-time", "type": "string"}, "Href": {"type": "string"}}}, "ApiCollection[MailingList]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/MailingList"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "ApiCollection[Member]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/Member"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "Member": {"type": "object", "properties": {"Email": {"type": "string"}, "FirstName": {"type": "string"}, "MiddleName": {"type": "string"}, "LastName": {"type": "string"}, "Suffix": {"type": "string"}, "UserName": {"type": "string"}, "ManagedBy": {"$ref": "#/definitions/Member"}, "Persona": {"$ref": "#/definitions/Group"}, "WorkItems": {"$ref": "#/definitions/ApiCollection[WorkItem]"}, "RecentDocuments": {"$ref": "#/definitions/ApiCollection[Document]"}, "Groups": {"$ref": "#/definitions/ApiCollection[Group]"}, "WatchedDocumentsProcessTrackingActivities": {"$ref": "#/definitions/ApiCollection[DocumentProcessTrackingActivity]"}, "WorkflowQueues": {"$ref": "#/definitions/ApiCollection[WorkflowQueue]"}, "Role": {"type": "string"}, "PermissionSets": {"$ref": "#/definitions/ApiCollection[PermissionSet]"}, "WatchedDocuments": {"$ref": "#/definitions/ApiCollection[Document]"}, "Href": {"type": "string"}}}, "TaskSummaryRequestBody": {"type": "object", "properties": {"TaskGroupIds": {"type": "array", "items": {"format": "uuid", "type": "string", "example": "00000000-0000-0000-0000-000000000000"}}, "DueDateRange": {"format": "int32", "enum": [0, 1, 2, 3, 4], "type": "integer"}, "AssignedUserUids": {"type": "array", "items": {"format": "uuid", "type": "string", "example": "00000000-0000-0000-0000-000000000000"}}, "OnlyUnassigned": {"type": "boolean"}, "SortColumn": {"type": "string"}, "PageSize": {"format": "int32", "type": "integer"}, "StartIndex": {"format": "int32", "type": "integer"}}}, "TaskSummary": {"type": "object", "properties": {"WorkItems": {"type": "array", "items": {"$ref": "#/definitions/TaskSummaryWorkItem"}}, "WorkflowQueues": {"type": "array", "items": {"$ref": "#/definitions/TaskSummaryTaskGroup"}}, "Href": {"type": "string"}}}, "TaskSummaryWorkItem": {"type": "object", "properties": {"Name": {"type": "string"}, "Type": {"type": "string"}, "Source": {"type": "string"}, "WorkflowName": {"type": "string"}, "AssigneeId": {"format": "uuid", "type": "string", "example": "00000000-0000-0000-0000-000000000000"}, "AssignorId": {"format": "uuid", "type": "string", "example": "00000000-0000-0000-0000-000000000000"}, "WorkflowQueueId": {"format": "uuid", "type": "string", "example": "00000000-0000-0000-0000-000000000000"}, "AssignDate": {"format": "date-time", "type": "string"}, "DueDate": {"format": "date-time", "type": "string"}, "Documents": {"type": "array", "items": {"$ref": "#/definitions/Document"}}, "CreatedDate": {"format": "date-time", "type": "string"}, "WorkItemUrl": {"type": "string"}, "Id": {"format": "uuid", "type": "string", "example": "00000000-0000-0000-0000-000000000000"}, "Information": {"type": "string"}}}, "TaskSummaryTaskGroup": {"type": "object", "properties": {"Id": {"format": "uuid", "type": "string", "example": "00000000-0000-0000-0000-000000000000"}, "Name": {"type": "string"}, "Permissions": {"$ref": "#/definitions/TaskGroupPermissions"}, "Members": {"type": "array", "items": {"$ref": "#/definitions/TaskGroupMember"}}}}, "TaskGroupPermissions": {"type": "object", "properties": {"CanClaim": {"type": "boolean"}, "CanUnclaim": {"type": "boolean"}, "CanAssign": {"type": "boolean"}}}, "TaskGroupMember": {"type": "object", "properties": {"Id": {"format": "uuid", "type": "string", "example": "00000000-0000-0000-0000-000000000000"}, "FullName": {"type": "string"}, "Email": {"type": "string"}}}, "PostWorkflowMembersRequestBody": {"type": "object", "properties": {"TaskGroupIds": {"type": "array", "items": {"format": "uuid", "type": "string", "example": "00000000-0000-0000-0000-000000000000"}}}}, "ApiCollection[TaskSummaryTaskGroup]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/TaskSummaryTaskGroup"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "ApiCollection[TaskManagedUser]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/TaskManagedUser"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "TaskManagedUser": {"type": "object", "properties": {"Uid": {"format": "uuid", "type": "string", "example": "00000000-0000-0000-0000-000000000000"}, "Name": {"type": "string"}, "Children": {"type": "array", "items": {"$ref": "#/definitions/TaskManagedUser"}}, "Count": {"format": "int32", "type": "integer"}, "Href": {"type": "string"}}}, "Party": {"type": "object", "properties": {"ExternalId": {"type": "string"}, "Name": {"type": "string"}, "ContactName": {"type": "string"}, "Type": {"type": "string"}, "ContactPhoneNumber": {"type": "string"}, "Source": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "CreatedBy": {"type": "string"}, "UpdatedDate": {"format": "date-time", "type": "string"}, "UpdatedBy": {"type": "string"}, "Href": {"type": "string"}}}, "ApiCollection[Party]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/Party"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "ApiCollection[ProcessDefinition]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/ProcessDefinition"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "ProcessDefinition": {"type": "object", "properties": {"Name": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "UpdatedDate": {"format": "date-time", "type": "string"}, "Href": {"type": "string"}}}, "ResourceTypeScim": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "endpoint": {"type": "string"}, "schema": {"type": "string"}, "schemas": {"type": "array", "items": {"type": "string"}, "readOnly": true}, "id": {"type": "string"}, "meta": {"$ref": "#/definitions/MetaScim"}}}, "SalesforceAttachmentImportTask": {"type": "object", "properties": {"Folder": {"$ref": "#/definitions/Folder"}, "Message": {"type": "string"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "SalesforceContentDocumentExportTask": {"type": "object", "properties": {"SfObjectId": {"type": "string"}, "Document": {"$ref": "#/definitions/Document"}, "SfContentDocumentId": {"type": "string"}, "Name": {"type": "string"}, "Format": {"type": "string"}, "SfContentVersionId": {"type": "string"}, "Message": {"type": "string"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "SalesforceContentDocumentImportTask": {"type": "object", "properties": {"Folder": {"$ref": "#/definitions/Folder"}, "Document": {"$ref": "#/definitions/Document"}, "Name": {"type": "string"}, "SfContentVersionId": {"type": "string"}, "Message": {"type": "string"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "SignatureTask": {"type": "object", "properties": {"Document": {"$ref": "#/definitions/Document"}, "EmailSubject": {"type": "string"}, "EmailMessage": {"type": "string"}, "ToEmails": {"type": "array", "items": {"type": "string"}}, "CcEmails": {"type": "array", "items": {"type": "string"}}, "SignaturesOrdered": {"type": "boolean"}, "ExpirationDate": {"format": "date-time", "type": "string"}, "SignerPassword": {"type": "string"}, "InPersonSigning": {"type": "boolean"}, "WrittenRequired": {"type": "boolean"}, "Message": {"type": "string"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "HealthCheckOptions": {"type": "object", "properties": {"IsLogOnly": {"type": "boolean"}}}, "SplitDocumentTask": {"type": "object", "properties": {"Document": {"$ref": "#/definitions/Document"}, "Pages": {"type": "string"}, "ResultDocument": {"$ref": "#/definitions/Document"}, "Message": {"type": "string"}, "DownloadUrl": {"type": "string"}, "DestinationFolder": {"$ref": "#/definitions/Folder"}, "DestinationDocumentName": {"type": "string"}, "Status": {"type": "string"}, "Href": {"type": "string"}}}, "ListResponseScim[UserScim]": {"type": "object", "properties": {"schemas": {"type": "array", "items": {"type": "string"}, "readOnly": true}, "totalResults": {"format": "int32", "type": "integer"}, "startIndex": {"format": "int32", "type": "integer"}, "itemsPerPage": {"format": "int32", "type": "integer"}, "Resources": {"type": "array", "items": {"$ref": "#/definitions/UserScim"}}}}, "UserScim": {"type": "object", "properties": {"schemas": {"type": "array", "items": {"type": "string"}, "readOnly": true}, "userName": {"type": "string"}, "name": {"$ref": "#/definitions/UserNameScim"}, "profileUrl": {"type": "string"}, "displayName": {"type": "string", "readOnly": true}, "title": {"type": "string"}, "active": {"type": "boolean"}, "addresses": {"type": "array", "items": {"$ref": "#/definitions/AdressScim"}}, "emails": {"type": "array", "items": {"$ref": "#/definitions/EmailScim"}}, "phoneNumbers": {"type": "array", "items": {"$ref": "#/definitions/PhoneNumberScim"}}, "groups": {"type": "array", "items": {"$ref": "#/definitions/MemberScim"}}, "externalId": {"type": "string"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/AttributeScim"}}, "urn:ietf:params:scim:schemas:extension:enterprise:2.0:User": {"$ref": "#/definitions/EnterpriseUserScim"}, "urn:ietf:params:scim:schemas:extension:springcm:2.0:User": {"$ref": "#/definitions/SpringCMUserScim"}, "id": {"type": "string"}, "meta": {"$ref": "#/definitions/MetaScim"}}}, "UserNameScim": {"type": "object", "properties": {"givenName": {"type": "string"}, "familyName": {"type": "string"}, "formatted": {"type": "string", "readOnly": true}}}, "AdressScim": {"type": "object", "properties": {"type": {"type": "string", "readOnly": true}, "streetAddress": {"type": "string"}, "locality": {"type": "string"}, "region": {"type": "string"}, "postalCode": {"type": "string"}, "country": {"type": "string"}, "formatted": {"type": "string", "readOnly": true}, "primary": {"type": "boolean", "readOnly": true}}}, "EmailScim": {"type": "object", "properties": {"value": {"type": "string"}, "type": {"type": "string", "readOnly": true}, "primary": {"type": "boolean", "readOnly": true}}}, "PhoneNumberScim": {"type": "object", "properties": {"value": {"type": "string"}, "type": {"type": "string"}}}, "AttributeScim": {"type": "object", "properties": {"value": {"type": "string"}, "display": {"type": "string"}, "type": {"type": "string"}, "primary": {"type": "boolean"}}}, "EnterpriseUserScim": {"type": "object", "properties": {"manager": {"$ref": "#/definitions/NameValueScim"}}}, "SpringCMUserScim": {"type": "object", "properties": {"persona": {"$ref": "#/definitions/AttributeScim"}}}, "NameValueScim": {"type": "object", "properties": {"value": {"type": "string"}, "$ref": {"type": "string"}, "displayName": {"type": "string"}}}, "ApiCollection[WorkflowDefinition]": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/definitions/WorkflowDefinition"}}, "Href": {"type": "string"}, "Offset": {"format": "int32", "type": "integer"}, "Limit": {"format": "int32", "type": "integer"}, "First": {"type": "string"}, "Previous": {"type": "string"}, "Next": {"type": "string"}, "Last": {"type": "string"}, "Total": {"format": "int32", "type": "integer"}}}, "WorkflowDefinition": {"type": "object", "properties": {"Name": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "UpdatedDate": {"format": "date-time", "type": "string"}, "Href": {"type": "string"}}}, "Signal": {"type": "object", "properties": {"Data": {"type": "string"}}}, "ActivityHistory": {"type": "object", "properties": {"TaskId": {"format": "uuid", "type": "string", "example": "00000000-0000-0000-0000-000000000000"}, "Source": {"type": "string"}, "HistoryEvents": {"type": "array", "items": {"$ref": "#/definitions/Activity"}}, "Href": {"type": "string"}}}, "Activity": {"type": "object", "properties": {"Title": {"type": "string"}, "Description": {"type": "string"}, "Initials": {"type": "string"}, "CommentText": {"type": "string"}, "IsCompleted": {"type": "boolean"}, "LastUpdateDate": {"format": "date-time", "type": "string"}, "Email": {"type": "string"}, "ExternalActivityUrl": {"type": "string"}, "DueDate": {"format": "date-time", "type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "AssignedUser": {"type": "string"}}}}}