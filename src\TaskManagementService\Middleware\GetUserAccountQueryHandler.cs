﻿#pragma warning disable <PERSON>1134

using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

using Flurl;

using MediatR;

using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Logging;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Authentication;
using Microsoft.Kiota.Http.HttpClientLibrary;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Exceptions;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API;

using J = System.Text.Json.Serialization.JsonPropertyNameAttribute;

namespace TaskManagementService.Middleware;

public sealed class GetUserAccountQueryHandler(
    ILogger<GetUserAccountQueryHandler> logger,
    HybridCache cache,
    IHttpClientFactory httpClientFactory,
    IClmApiConfig clmApiConfig,
    IAuthenticationProvider authenticationProvider)
    :
        IRequestHandler<GetUserAccountQuery, UserAccount>,
        IRequestHandler<GetClmUserAccountQuery, ClmUserAccount>
{
    public async Task<UserAccount> Handle(GetUserAccountQuery request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));
        ArgumentNullException.ThrowIfNull(request.RequestContext, nameof(request.RequestContext));

        var cacheKey = GetCacheKey(request.RequestContext.AccountId, request.RequestContext.UserId);
        var userAccount = await cache.GetOrCreateAsync(cacheKey, async cancel => await GetUserAccountAsync(request.RequestContext, cancel), cancellationToken: cancellationToken);
        return userAccount;

        static string GetCacheKey(Guid accountId, Guid userId) => $"TMS_UA_{accountId}_{userId}";
    }

    private async Task<UserAccount> GetUserAccountAsync(IRequestContextService requestContext, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(requestContext, nameof(requestContext));

        using var httpClient = httpClientFactory.CreateClient("UserAccount");
        httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", requestContext.Authorization);

        var requestUrl = requestContext.Issuer.AppendPathSegments("/oauth/userinfo");

        using var request = new HttpRequestMessage(HttpMethod.Get, requestUrl.ToUri());
        var response = await httpClient.SendAsync(request, cancellationToken);
        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var user = JsonSerializer.Deserialize<DocusignUser>(content);
            var account = user?.Accounts.FirstOrDefault(a => a.AccountId == requestContext.AccountId);
            if (account != null)
            {
                var userAccount = new UserAccount(requestContext.AccountId, requestContext.UserId, account.AccountName ?? string.Empty, account.BaseUri);
                if (!requestContext.IsClmAccount)
                {
                    return userAccount;
                }

                var clmRequestUrl = clmApiConfig.AuthUrl.AppendPathSegments($"v2/{requestContext.AccountId}/account");
                using var clmRequest = new HttpRequestMessage(HttpMethod.Get, clmRequestUrl.ToUri());

                var clmResponse = await httpClient.SendAsync(clmRequest, cancellationToken);
                if (!clmResponse.IsSuccessStatusCode)
                {
                    logger.LogInformation("Could not retrieve CLM account information. Status Code: {StatusCode}", clmResponse.StatusCode);
                    return userAccount;
                }

                var clmContent = await clmResponse.Content.ReadAsStringAsync(cancellationToken);
                var clmAccount = JsonSerializer.Deserialize<DocusignClmAccount>(clmContent);
                if (clmAccount != null)
                {
                    userAccount = userAccount with
                    {
                        ClmApiBaseUrl = clmAccount.ApiBaseUrl,
                    };
                }

                return userAccount;
            }
        }
        else
        {
            logger.LogWarning("Failed to retrieve user information. Status Code: {StatusCode}", response.StatusCode);
        }

        throw new TaskManagementServiceException(ApplicationError.CreateFailedToRetrieveUserError(requestContext.UserId, requestContext.AccountId));
    }

    public async Task<ClmUserAccount> Handle(GetClmUserAccountQuery request, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));
        ArgumentNullException.ThrowIfNull(request.RequestContext, nameof(request.RequestContext));

        if (request.RequestContext is not { IsClmAccount: true })
        {
            return null;
        }

        var cacheKey = GetCacheKey(request.RequestContext.AccountId, request.RequestContext.UserId);
        var clmUserAccount = await cache.GetOrCreateAsync(cacheKey, async cToken => await GetClmUserAccountAsync(request.RequestContext, cToken), cancellationToken: cancellationToken);
        return clmUserAccount;

        static string GetCacheKey(Guid accountId, Guid userId) => $"TMS_CLM_UA_{accountId}_{userId}";
    }

    private async Task<ClmUserAccount> GetClmUserAccountAsync(IRequestContextService requestContext, CancellationToken cancellationToken)
    {
        if (requestContext.ClmApiBaseUrl == null)
        {
            return null;
        }

        try
        {
            using var httpClient = httpClientFactory.CreateClient("UserAccount");
            using var requestAdapter = new HttpClientRequestAdapter(authenticationProvider, httpClient: httpClient);
            requestAdapter.BaseUrl = requestContext.ClmApiBaseUrl.ToString();
            var clmClient = new CLMV2APIClient(requestAdapter);

            var currentMember = await clmClient.V2[requestContext.AccountId.ToString()].Members.Current.GetAsync(cancellationToken: cancellationToken);
            if (currentMember?.Href != null)
            {
                var currentClmUserIdString = currentMember.Href.Split("/").Last();
                if (Guid.TryParse(currentClmUserIdString, out var currentClmUserId))
                {
                    var currentClmUserAccount = new ClmUserAccount
                    {
                        Role = currentMember.Role ?? string.Empty,
                        UserName = currentMember.UserName ?? string.Empty,
                        UserId = currentClmUserId,
                    };

                    return currentClmUserAccount;
                }
            }
        }
        catch (ApiException e)
        {
            logger.LogWarning(e, $"CLM V2 API Exception in {nameof(GetClmUserAccountAsync)}");
        }

        return null;
    }

    [GeneratedCode("QuickType", "V1")]
    public sealed class DocusignUser
    {
        [J("sub")] public Guid Sub { get; init; }
        [J("name")] public string Name { get; init; }
        [J("given_name")] public string GivenName { get; init; }
        [J("family_name")] public string FamilyName { get; init; }
        [J("created")] public DateTimeOffset Created { get; init; }
        [J("email")] public string Email { get; init; }
        [J("accounts")] public List<DocusignAccount> Accounts { get; init; } = [];
    }

    [GeneratedCode("QuickType", "V1")]
    public sealed class DocusignAccount
    {
        [J("account_id")] public Guid AccountId { get; set; }
        [J("account_name")] public string AccountName { get; set; }
        [J("base_uri")] public Uri BaseUri { get; set; }
    }

    [GeneratedCode("QuickType", "V1")]
    public sealed class DocusignClmAccount
    {
        [J("Id")] public Guid Id { get; init; }
        [J("ApiBaseUrl")] public Uri ApiBaseUrl { get; init; }
        [J("ApiBaseDownloadUrl")] public Uri ApiBaseDownloadUrl { get; init; }
        [J("ApiBaseUploadUrl")] public Uri ApiBaseUploadUrl { get; init; }
        [J("WebLandingPageUrl")] public Uri WebLandingPageUrl { get; init; }
        [J("DocumentPreviewUrl")] public Uri DocumentPreviewUrl { get; init; }
    }
}
