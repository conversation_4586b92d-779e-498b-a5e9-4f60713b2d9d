// <auto-generated/>
using System.Runtime.Serialization;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public enum AggregateFunction
    #pragma warning restore CS1591
    {
        [EnumMember(Value = "COUNT")]
        #pragma warning disable CS1591
        COUNT,
        #pragma warning restore CS1591
        [EnumMember(Value = "MIN")]
        #pragma warning disable CS1591
        MIN,
        #pragma warning restore CS1591
        [EnumMember(Value = "MAX")]
        #pragma warning disable CS1591
        MAX,
        #pragma warning restore CS1591
    }
}
