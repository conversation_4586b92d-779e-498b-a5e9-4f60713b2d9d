﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>TaskManagementService</RootNamespace>
    <UserSecretsId>************************************</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.Redis" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" />
    <PackageReference Include="Azure.ResourceManager.Redis" />
    <PackageReference Include="DocuSign.Adm.Common.AspNet.OAuth" />
    <PackageReference Include="DocuSign.Msf.AspNet.HeaderPropagation" />
    <PackageReference Include="DocuSign.Msf.AspNet.OpenTelemetry.SemanticConventions" />
    <PackageReference Include="DocuSign.Msf.AspNet.OpenTelemetry" />
    <PackageReference Include="DocuSign.Msf.AspNet.VersionEndpoint" />
    <PackageReference Include="DocuSign.Platform.Extensions.Configuration.FileExtensions" />
    <PackageReference Include="DocuSign.Platform.Runtime" />
    <PackageReference Include="DocuSign.Platform.Storage.Clients.Redis.ConnectionInfo" />
    <PackageReference Include="Microsoft.AspNetCore.HeaderPropagation" />
    <PackageReference Include="DocuSign.OneConfig.CodeGen.Build" />
    <PackageReference Include="DocuSign.OneConfig.Core" />
    <PackageReference Include="DocuSign.OneConfig.Extensions.DotNet" />
    <PackageReference Include="DocuSign.OneConfig.Extensions.Msf" />
    <PackageReference Include="Microsoft.Azure.StackExchangeRedis" />
    <PackageReference Include="Microsoft.Extensions.Http.Resilience" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" />
    <PackageReference Include="Microsoft.Kiota.Authentication.Azure" />
    <PackageReference Include="Microsoft.Kiota.Bundle" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Http" />
    <PackageReference Include="OpenTelemetry.Instrumentation.GrpcNetClient" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Serilog.AspNetCore" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" />
    <PackageReference Include="System.Text.Json" />
  </ItemGroup>

  <ItemGroup>
    <OneConfigGen Include="Config/Schemas/**/*.proto" />
  </ItemGroup>

  <ItemGroup>
    <OneConfigIni Include="Config/Files/**/*.ini" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TaskManagementService.Core\TaskManagementService.Core.csproj" />
    <ProjectReference Include="..\TaskManagementService.Infrastructure\TaskManagementService.Infrastructure.csproj" />
    <ProjectReference Include="..\TaskManagementService.ServiceIntegrations\TaskManagementService.ServiceIntegrations.csproj" />
  </ItemGroup>

  <Target Name="CheckDotnetToolRestore" BeforeTargets="Build">
    <Exec Command="dotnet tool restore" IgnoreExitCode="true">
      <Output TaskParameter="ExitCode" PropertyName="DotnetToolRestoreExitCode" />
    </Exec>
    <Error Text="The 'dotnet tool restore' command failed." Condition="'$(DotnetToolRestoreExitCode)' != '0'" />
  </Target>

</Project>
