using System;
using System.Linq;

using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Models;

using Xunit;

namespace TaskManagementService.Tests.Models;

[Trait("TestType", "UnitTest")]
public class TaskFilterTest
{
    [Fact]
    public void DueDate()
    {
        var taskFilter = new TaskFilter { DueDateFrom = new DateTimeOffset(2025, 01, 01, 0, 0, 0, new TimeSpan(0)), DueDateTo = new DateTimeOffset(2025, 2, 01, 0, 0, 0, new TimeSpan(0)), };

        var tasks = new UserTask[] { new() { DueDate = "2024-10-14T05:56:04.9837193Z" }, new() { DueDate = "2025-01-14T05:56:04.9837193Z" }, new() { DueDate = "2025-10-14T05:56:04.9837193Z" }, new() { DueDate = string.Empty } };

        var result = tasks.Where(taskFilter.IsMatchedDueDate).ToList();
        Assert.Single(result);
        Assert.Equal("2025-01-14T05:56:04.9837193Z", result.First().DueDate);
    }

    [Fact]
    public void AssignedDate()
    {
        var taskFilter = new TaskFilter { AssignedDateFrom = new DateTimeOffset(2025, 04, 01, 0, 0, 0, new TimeSpan(0)), AssignedDateTo = new DateTimeOffset(2025, 4, 30, 0, 0, 0, new TimeSpan(0)), };

        var tasks = new UserTask[] { new() { AssignedDate = "2025-10-14T05:56:04.9837193Z" }, new() { AssignedDate = "2025-04-14T05:56:04.9837193Z" }, new() { AssignedDate = "2025-01-14T05:56:04.9837193Z" }, new() { AssignedDate = string.Empty } };

        var result = tasks.Where(taskFilter.IsMatchedAssignedDate).ToList();
        Assert.Single(result);
        Assert.Equal("2025-04-14T05:56:04.9837193Z", result.First().AssignedDate);
    }

    [Fact]
    public void IsMatchedAssignedUserOnlyAssigned()
    {
        var taskFilter = new TaskFilter { OnlyUnassigned = true };
        var tasks = new UserTask[] { new() { Assignees = null }, new() { Assignees = [new TaskUser(Guid.NewGuid(), "s", "<EMAIL>", CollaboratorType.Editor)] } };
        var result = tasks.Where(taskFilter.IsMatchedAssignedUser).ToList();
        Assert.Single(result);
    }

    [Fact]
    public void IsMatchedAssignedUserTaskUser()
    {
        var userId = Guid.NewGuid();
        var taskFilter = new TaskFilter { AssignedUserIds = [userId] };
        var tasks = new UserTask[] { new() { Assignees = null }, new() { Assignees = [new TaskUser(userId, "s", "<EMAIL>", CollaboratorType.Editor)] } };
        var result = tasks.Where(taskFilter.IsMatchedAssignedUser).ToList();
        Assert.Single(result);
    }

    [Fact]
    public void IsMatchedAssignedUserGroupUser()
    {
        const string groupId = "123";
        var taskGroup = new TaskGroupWithPermissions { Id = groupId, Members = null!, Name = "Group name", Source = TaskSource.ESignature };
        var taskFilter = new TaskFilter { GroupAssignees = [groupId] };
        var tasks = new UserTask[] { new() { Assignees = null }, new() { Assignees = [new TaskSigningGroup(groupId, "s", CollaboratorType.Assignee, null!, 1)], TaskGroupAssignee = taskGroup } };
        var result = tasks.Where(taskFilter.IsMatchedAssignedUser).ToList();
        Assert.Single(result);
    }
}
