﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DocuSign.eSign.dll" />
    <PackageReference Include="Flurl.Http" />
    <PackageReference Include="Mapster" />
    <PackageReference Include="Mapster.DependencyInjection" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="Microsoft.Extensions.Caching.Hybrid" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" />
    <PackageReference Include="Microsoft.Extensions.Http.Resilience" />
    <PackageReference Include="DocuSign.OneConfig.CodeGen.Build" />
    <PackageReference Include="DocuSign.OneConfig.Core" />
    <PackageReference Include="DocuSign.OneConfig.Extensions.DotNet" />
    <PackageReference Include="DocuSign.OneConfig.Extensions.Msf" />
  </ItemGroup>

  <ItemGroup>
    <OneConfigGen Include="Config/Schemas/**/*.proto" />
  </ItemGroup>

</Project>
