{"Logging": {"LogLevel": {"Default": "Information", "System": "Warning", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "Warning", "Microsoft.Extensions.Diagnostics.HealthChecks": "Warning", "Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware": "Information"}}, "ForwardedHeaders": {"AllowedHosts": ["services.stage.docusign.net", "*.services.stage.docusign.net"]}, "Security": {"Authorities": ["https://account-s.docusign.com/"]}, "OptionalFeatures": {"EnableRedis": true}}