# Contributing to Task Management Service

Thank you for your interest in contributing to the Task Management Service! This document provides guidelines and instructions for contributing to this project.

## Table of Contents

* [Getting Started](#getting-started)
* [Development Setup](#development-setup)
* [Code Standards](#code-standards)
* [Pull Request Process](#pull-request-process)
* [Testing](#testing)
* [Build and Deployment](#build-and-deployment)
* [Architecture Guidelines](#architecture-guidelines)

## Getting Started

### Prerequisites

* **. NET 8 SDK** (version 8.0.414 or later as specified in `global.json`)
* **Git** for version control
* **Docker** (for containerized development and testing)
* **PowerShell** (cross-platform) - used as the script shell

### Fork and Clone

1. Fork the repository on GitHub
2. Clone your fork locally:


```bash
   git clone https://github.com/your-username/task-management-service.git
   cd task-management-service
   ```

3. Add the upstream repository as a remote:


```bash
   git remote add upstream https://github.com/original-owner/task-management-service.git
   ```

## Development Setup

### Initial Setup

1. **Restore tools and dependencies:**


```bash
   dotnet tool restore
   dotnet restore
   ```

2. **Build the solution:**


```bash
   dotnet build
   ```

3. **Run tests to ensure everything works:**


```bash
   dotnet test
   ```

### Local Development Options

#### Option 1: Direct . NET Run

```bash
cd src/TaskManagementService
dotnet run
```

#### Option 2: Docker Compose (Recommended for Full Stack Testing)

The Docker Compose setup includes nginx as a reverse proxy with SSL support:

```bash
docker-compose up
```

#### Option 3: True Dev Environment

For testing in the MSF (Microservices Framework) environment, follow the [MSF True Dev documentation](https://github.docusignhq.com/pages/Microservices/msf-docs/developer-productivity/msf-truedev-environment/).

**Note:** If local changes don't appear immediately in True Dev, touch the `Dockerfile` in `src/TaskManagementService` to force a rebuild.

## Code Standards

### C# Coding Standards

This project follows Microsoft's C# coding conventions and uses StyleCop for code analysis.

#### Key Guidelines:

* **Target Framework:** . NET 8.0
* **Language Version:** C# 12 (latest)
* **Nullable Reference Types:** Enabled
* **Treat Warnings as Errors:** Enabled

#### Code Style:

* Use **PascalCase** for public members, types, and namespaces
* Use **camelCase** for private fields and local variables
* Use **meaningful names** that describe the purpose
* Follow **SOLID principles**
* Use **async/await** for asynchronous operations
* Implement proper **exception handling**

#### StyleCop Rules:

* The project uses StyleCop analyzers with custom rules defined in `build/stylecop.ruleset`
* XML documentation is currently disabled (SA0001)
* Ensure your code passes StyleCop analysis before submitting

# Best Practices / Coding Style for C# Development

Based on [DocuSign CSharp Best Practices and Coding Style](https://mediawiki.docusigntest.com/wiki/CSharp_Best_Practices_and_Coding_Style)

## Scope

DocuSign has millions of lines of code in production. The new coding guidelines are not retroactive and apply only to new and refactored code. This means that existing code is not subject to the new guidelines, but any new or refactored code must adhere to them. This approach is a common practice in software development, where existing code is not modified unless it is necessary for fixing bugs, improving quality, or adding new features.

Similarly, generated code should not be modified to align with the new guideline, future code generators and refactored existing generators should generate code based on this guideline, when possible.

## Goals

Following coding best practices will help us write testable, clean and maintainable code which will lead to higher productivity and higher product quality, more organized work and will reduce the cost. This coding conventions serve the following purposes:

*   Creates a consistent look to the code, so that readers can focus on content, not layout.
*   Enables readers to understand the code more quickly by making assumptions based on previous experience.
*   Facilitates copying, changing, and maintaining the code.
*   Minimize defects
*   Minimize code review controversy
*   Maximize readability and maintainability
*   Maximize code consistency, it is natural for any team member to work on any part of the code
*   Maximize compliance
*   Provide a central location for team members to discover coding rules
*   Promote engineering best practices

## Principles

*   Prefer simplicity over complexity.
*   Prefer explicitness over implicitness.
*   Prefer language constructs over comments (self-documenting code).
*   Prefer practices that make reading code easier (because we read code far more often than we write it).
*   Prefer rules that can be enforced by tools and the compiler.
*   Prefer rules that provide more benefit than the cost of enforcement.

## Structure

This document is mostly gathered from Microsoft's C# guidelines and best practices. In some cases, guidelines were modified to match the existing code/state in DocuSign.

We understand that most engineers are already following many Microsoft C# standards, and have preferences in some areas. We also understand that this document is dynamic and may change over time. We respect engineer preferences and we hope that engineers preferences help with enhancing this guideline continuously.

This document has two types of statements, preferences and rules.

#### Preferences

Examples:

* 🔸 Avoid putting unrelated apps in the same repository.
* 🔸 Avoid nested function calls

Preferences are not strict rules. In most cases, these preferences can be followed, but there are exceptions. Use your best judgment, and follow preferences if possible. Preferences can be identified with 🔸 symbol and usually have words such as "Avoid", "where possible", "Consider" in them.

#### Rules

Rules can be identified by ❗symbol, Example:

* ❗ Naming conventions
* ❗ Do not modify generated code to follow this guideline.
* ❗ All names should be in English
* ❗ Use file scoped format namespaces.

The expectation is to follow all rules in the new and refactored code. Use the slack channel and communicate when you find a rule that needs to be updated or enhanced, or need an exception.

## Discussion Group

The slack channel [#eng-csharp-guidelines](https://docusign.slack.com/archives/C05P1SCBV32) is assigned for the discussion and comments on this guideline.

Please send your questions, comments and suggestions through this slack channel.

## Coding Standard

#### Language

❗ Code, comments, attributes, methods and everything else should be readable by all engineers in DocuSign, and therefore should be written in one Language. Since most of the code and comments are in English, English is chosen as the language of the code. All comments, variable names, methods names, project names and everything else in the code should be written in English.

❗ Localization should not be hardcoded and localized strings and resources should be in resource-only DLLs.

#### Code Repositories

Creating a new code repository takes some effort, it is much faster to add the new code to an existing repository and take advantage of everything that already exists and working. However, new repositories enable teams to work independently, and do their work faster. Code will be built and tested faster and because it has less dependencies, refactoring and customizing it will be much easier.

*   ❗ Divide and Conquer when you can. Create a separate repository for each service, microservice, library (NuGet or a set of closely related NuGets), and application. Each repository can have multiple related projects if they must be built and deployed together. However, it is important to maintain a balance and avoid splitting the repository too much, as this can affect the development and debugging process. This approach provides the freedom to code independently of all other applications, making it easier and faster to make code changes while fixing bugs, making updates, testing, and deploying.
*   🔸 Avoid putting unrelated apps in the same repository.
*   ❗ Code and tests must be in the same repo. Code coverage should only consider tests that are in the same repo as the main code.
*   ❗ Choose a name that is established for the project and be very specific to differentiate between similar ideas later.
*   ❗ Repo names should not include the team or organization names.
*   🔸 Be consistent, use the same name for pipeline, wiki and other related objects where possible.
*   🔸 Consider splitting the repository if it contains many projects.

#### ADO Project Name and Pipelines

GitHub repository name is made of two parts, organization name and repository name, separated by a forward slash, such as Core/Core or platform-common / BenchmarkDotNetBaseline.

Azure Devops are organized under an entity called organization. Each organization has its own properties such as name, settings, and permissions. DocusignOrg is an example of an organization. An organization can have zero or more ADO projects, each can have multiple pipelines, code repository, dashboard, wiki and more.

Scaffolding creates an ADO project that is connected to a github repository.

🔸 If repo is in GitHub, consider using the same name for the github repository and the ADO project name. If an ADO project depends on multiple repositories, use the name of the default or the most important repo.

#### Project Structure

🔸 Although there is no single correct way to organize code, this document recommends using the file structure below as it is a common way of organizing files and folders in a repository:

```
$/
  build/
  docs/
  samples/
  src/
  tests/
  .editorconfig
  .gitignore
  .gitattributes
  LICENSE
  NuGet.Config
  README.md
 dirs.proj
  ...

```

* ❗ License file must be located in the root folder of the repository, per legal requirements.
* ❗ Readme file should also be put in the root folder.
* 🔸 Avoid putting solution files in the root folder as the build pipeline may fail with unclear error and local build commands will require solution or proj name.

Folder structure:

*   src - Contains the actual source code for the application (the various projects with their classes etc.)
*   tests - Contains unit test projects
*   docs - Contains documentation files
*   samples - This is where you would place files that provide examples to users on how to use your code or library
*   build - Contains build customization scripts

🔸 Notice that in some cases the same structure could be recursively applied to the individual projects of the solution (for instance, you could have a src and test folder for each project). Either way is acceptable as long as all projects in the repo are built and deployed together. This document describes the first approach only, as it can be expanded and cover both cases.

##### Naming projects and namespaces

❗ Name of the project should ease understanding the functionality of the code it contains. For instance, if a project contains math related operations, it would probably contain Math in its name.

🔸 Use ProductName.Component naming convention for projects if the project is made of multiple components. If the product is a single component, ProductName should be sufficient. In most cases ProductName should be the same as repo name.

🔸 Use DocuSign.ProductName.Component as the main namespace of the project.

##### Subfolders

❗ In the case of subfolders within the same project, the namespaces of the files within the subfolders reflect the same hierarchical structure. For example, in a MyApplication.DataAccess project, classes within a SqlServer folder would belong to MyApplication.DataAccess.SqlServer namespace.

🔸 Avoid using excessive subfolders or deeply nested subfolders as they can get in the way of navigation and discovering code. Consider folders as a category or a container, only add a new folder when there is an obvious need for it. As an example, have a folder for all Attribute classes and a folder for all data objects in the project.

##### Use multiple projects to separate functionality or layers

🔸 Group large codes into multiple projects by layer or major functionality. Keep the balance and avoid creating many small projects. Logically grouping code will keep the structure clean and easier to understand. Within the same project it is possible to create additional groupings, putting classes and components with similar functionalities into subfolders.

❗ Do not create circular references.

🔸 The resulting folder structure could look like this:

```
$/
  MyApplication.sln
  src/
    MyApplication.UI/
      MyApplication.UI.csproj
    MyApplication.DataAccess/
      SqlServer/
        SqlServerRepository.cs
      IRepository.cs
      MyApplication.DataAccess.csproj
```

##### Unit tests organization

❗ The structure and naming convention of unit test projects should reflect those of the source code that it's being tested, with an addition of a Tests suffix to the names of projects and classes. For instance, the MyApplication.Math project would be tested by a unit test project called MyApplication.Math.Tests; the unit tests for a class called Calculator would be found in a test class named CalculatorTests.

```
$/
  MyApplication.sln
  src/
    MyApplication.Math/
      Calculator.cs
      MyApplication.Math.csproj
      Utilities/
        MathUtilities.cs
  tests/
    MyApplication.Math.Tests/
      CalculatorTests.cs
      MyApplication.Math.Tests.csproj
      Utilities/
        MathUtilitiesTests.cs
```

#### File Organization

* ❗ The build system might run on a case sensitive operating system. Always assume that file names and folder names are case sensitive, even when you are using an OS with a case insensitive file system.
* ❗ Source file name and class name should match. This will help with finding and navigating the code.

```
MyClass.cs → public class MyClass { ... }
```

* ❗ Directory names should follow the namespace for the class.
```
System.Windows.Forms.Control should use the path System\Windows\Forms\Control.cs
```

* 🔸 Dot ('.') in the project name and project folder is acceptable. Dots are not acceptable in the subfolders of a project.

### Layout

#### File, Classes and Namespaces

* ❗ Use file scoped format namespaces. It eliminates the horizontal space in the beginning of every line in the class.
* ❗ Do not put multiple namespaces or classes per file. This will make the code more readable and also make it easier to find the .cs file for a particular class.
* ❗ Do not put enums into classes, enums should be outside of classes. Putting enums in classes will make refactoring harder in future.

* ❗ Place **using** directives at the top of the file.
* 🔸 Consider placing **using** with .NET namespaces first, then other namespaces, grouped and ordered alphabetically.

```csharp
// .NET namespaces first
using System;
using System.Collections;

// Then any other namespaces in alphabetical order
using Company.Business;
using Company.Standard;
using Telerik.Ajax;
using Telerik.WebControls;
```

* ❗ Maintain a common order for each class
```
1.  Member variable
2.  Constructors, Finalizer and Deconstructors
3.  Private nested Structs and Classes
4.  Properties
5.  Methods
```
* ❗ Sequence declarations within type groups are based on StyleCop's SA1202 ordering: public, internal, protected internal, protected, private.

#### Indentation

❗ Indentation and code style must be consistent in the entire repo. At this time, there are different styles in DocuSign. These are the recommendations:

* 🔸 Basic indentation is 4 spaces (not tabs).
* 🔸 Save tab as spaces

#### Lines

* ❗ Write only one statement per line.
* ❗ Write only one declaration per line.
* ❗ If continuation lines are not indented automatically, indent them one tab stop (i.e. four spaces).
* ❗ Add at least one blank line between method definitions and property definitions.
* 🔸 Limit line length to 130 characters. Anything beyond that would make PR reviews hard, and they can cause word and line wraps in PR reviews. When an expression does not fit, follow the general guidelines:
    * ❗ Break after a comma
    * ❗ Break after an operator
    * ❗ Align the new line with the beginning of the expression at the same level on the previous line
    * ❗ Prefer higher-level breaks to lower-level breaks

```csharp
// Good
longMethodCall(expr1, expr2, expr3, expr4, expr5);

longMethodCall(
    expr1,
    expr2,
    expr3,
    expr4,
    expr5);

longMethodCall(expr1,
               expr2,
               expr3,
               expr4,
               expr5);

// Bad
longMethodCall(expr1, expr2
        ,expr3, expr4, expr5);

// Good (break occurs outside the parenthesized expression, which is higher-level.)
var result = a * b / (c - g + f) +
          4 * z;

// Bad
var result = a * b / (c - g +
           f) + 4 * z;
```

#### Braces

* ❗ Always place curly braces on a new line. This is known as the [Allman style](https://en.wikipedia.org/wiki/Indentation_style#Allman_style).

```csharp
while (x < y)
{
    firstMethod();
    secondMethod();
}
lastMethod();
```

* ❗ Always put curly braces even if it might not be required. Such as having only one statement in the if clause. This is to enforce consistency.

```csharp
if (x > y)
{
    doSomething();
}

// Use
if ((val1 > val2) && (val1 > val3))
{
    return val1;
}

// do not use
if ((val1 > val2) && (val1 > val3))
    return val1;

// do not use
if ((val1 > val2) && (val1 > val3)) return val1;
```

* ❗ Use braces for if/else/if/else… compound statements

```csharp
if (a < b)
{
    return -1;
}
else if (a == b)
{
    return 0;
}
else
{
    return 1;
}
```

#### White Space

* ❗ Avoid more than one empty line at any time. Avoid extra white spaces.
* ❗ Keywords like **if** and **while** should be followed by a white space.
* ❗ Semicolons in **for** statements should be followed by a white space.
* ❗ Commas should be followed by a white space.
* ❗ Add a white space around operators like **+**, **-**, **==** etc.
* ❗ Do not add white space after **(** and before **)**.

```csharp
// Good
a = (b + c) * d;
while (true)
doSomething(a, b, c, d);
for (i = 0; i < 10; i++)

// Bad
a=(b+c)*d;
while(true)
doSomething(a,b,c,d);
for(i=0;i<10;i++)
```

* ❗ Use parentheses to make clauses in an expression apparent, as shown in the following code.
```csharp
// Good
if (val1 > val2 && (val1 > val3 || val1 > val4))
{
    // Take appropriate action.
}

// Bad
if ((val1 > val2) && (val1 > val3 || val1 > val4))
{
    // Take appropriate action.
}
```

### Naming Convention

In this document any of the guidance pertaining to elements marked **public** is also applicable when working with **protected** and **protected internal** elements, all of which are intended to be visible to external callers. The proper casing for language elements are:

*   Pascal casing: the first letter of every word is capitalized.
*   Camel casing: the first letter of every word, except for the first word, is capitalized.

❗ All names should be written in English. English is the language for international development.

#### Pascal case

❗ Use pascal casing ("PascalCasing") for namespaces and when naming a class, record, or struct.
```csharp
public class DataService
{
}

public record PhysicalAddress(
    string Street,
    string City,
    string StateOrProvince,
    string ZipCode);

public struct ValueCoordinate
{
}
```
❗ Use Pascal casing for file names. Assume file names are case sensitive.

❗ When naming an **interface**, use pascal casing in addition to prefixing the name with an **I**. This clearly indicates to consumers that it's an **interface**.

```csharp
public interface IWorkerQueue
{
}
```

❗ When naming **public** members of types, such as fields, properties, events, methods, and local functions, use pascal casing.

```csharp
public class ExampleEvents
{
    // A public field, Avoid using public fields
    public bool IsValid;
    // An init-only property
    public IWorkerQueue WorkerQueue { get; init; }
    // An event
    public event Action EventProcessing;
    // Method
    public void StartEventProcessing()
    {
        // Local function
        static int CountQueueItems() => WorkerQueue.Count;
        // ...
    }
}
```
❗ Use pascal casing for parameters of a record as they're the public properties of the record.

```csharp
public record PhysicalAddress(
    string Street,
    string City,
    string StateOrProvince,
    string ZipCode);
```

❗ Use pascal casing for Enum type and enum values.

```csharp
enum Season
{
    Spring,
    Summer,
    Autumn,
    Winter
}
```

❗ Use pascal casing for constant, static and read only static fields.

```csharp
public class DataService
{
    private const CompanyName = "DocuSign"
    private static readonly SomeVar;
}
```

#### Camel case

❗ Use camel casing ("camelCasing") when naming **private** or **internal** fields.
* ❗ Use camel casing for local variables.
* ❗ Prefix private (not static) fields with **_**.

```csharp
public class DataService
{
    private IWorkerQueue _workerQueue;
    private static IWorkerQueue WorkerQueue;

    [ThreadStatic]
    private static TimeSpan TimeSpan;
}
```

❗ When writing method parameters, use camel casing.

```csharp
public T SomeMethod<T>(int someNumber, bool isValid)
{
}
```

#### Additional Naming Convention

* ❗ Enum types use a singular noun for non-flags, and a plural noun for flags. Flags is an attribute for the enum that allows using bitwise operation on the enum.

* ❗ Identifiers shouldn't contain two consecutive underscore (_) characters. Those names are reserved for compiler-generated identifiers.

* 🔸 Avoid using abbreviations, Unless the full name is excessive:
    * 🔸 Avoid abbreviations longer than 5 characters.
    * 🔸 Abbreviations must be widely known and accepted.
    * 🔸 Use upper case for 2-character abbreviations, and Pascal Case for longer abbreviations, such as **UIControl** and **HtmlSource**.

* ❗ Prefix boolean variables with **Can**, **Is**, or **Has**. Examples: **CanEvaluate**, **IsVisible**, **HasLicense**.

* 🔸 Avoid boolean variables that represent the negation of things. e.g., use **IsInitialized** instead of **IsNotInitialized**.

* ❗ Do not include the class name within a property name, i.e. use **Customer.Name** instead of **Customer.CustomerName**.

* ❗ Do not use Hungarian Notation. Hungarian notation is a defined set of pre and postfixes which are applied to names to reflect the type of the variable. This style was used in early Windows programming, but is now obsolete. Use **Name** instead of **strName** and **Colors** instead of **ColorsEnum**

*   Exceptions
    * ❗ All fields and variable names that contain GUI elements like buttons should be postfixed with their type name without abbreviations. e.g., **cancelButton**, **nameTextBox**.
    * ❗ Async method must have the suffix "Async".
    * ❗ Attribute types end with the word Attribute.

* ❗ Do not fully qualify names when the namespace is imported. Qualified names can be broken after a dot (.) if they are too long for a single line, as shown in the following example.

```csharp
var currentPerformanceCounterCategory = new System.Diagnostics.
    PerformanceCounterCategory();
```

* ❗ Do not change the names of objects in the auto-generated codes to fit guidelines.

### Commenting

Use comments to say something important that cannot be said by code. Code should be simple, explicit and self-documenting. This means that most of the time the programing language (C# or other) is sufficient to express the intent of the developer so anyone can easily understand it. In the rare cases when the programming language is not expressive enough, use human language (English) to explain the situation. Following this simple principle leads to significant benefits:

*   Every comment is important. If a comment exists in the code it must be saying something important. Otherwise, the developer would have used only the programming language without comments.
*   Comments are read, maintained and trusted. Since every comment is important, developers have good reason to read, maintain and trust them.
*   No noisy comments. The code is more compact because most of it is self-documenting. Gone are the fluffy, summary comments that don't say anything important. The signal-to-noise ratio increases dramatically.
*   Less risk of missing important information in comments. When the code is full of noisy comments people tend to ignore them which can easily lead to ignoring important information hidden among the noise.
*   Write comments in the right place

Here are a few examples of cases when comments make sense and should be encouraged:

*   Explain contracts. Data contracts, service contracts, API contracts, etc. tend to be based on combinations of data, methods and rules on how to use them. It is not always obvious what combinations of methods to use and how to interpret the various combinations of fields in contract objects. Besides, public contracts are usually required to be documented even if they are relatively simple.
*   Track issues and ideas. Using TODO comments along with a ticket is a great way to track issues, concerns and ideas as close to the source code as possible. Usually those are non-fatal problems or ideas for future improvements.
*   Document non-obvious assumptions and behaviors. These should ideally be minimized and eliminated but it is not always possible, especially when related to external or legacy systems, frameworks and tools.

The best example when comments should NOT be used is to repeat what the code is already telling us.

* ❗ Write self explanatory code, which is easy to understand without the need of extensive documentation or comments.
* ❗ Attributes and methods names should clearly describe their purpose.
* ❗ Do not write long methods.
* 🔸 Avoid using comments to explain flows, unless it is absolutely necessary.
* ❗ Do not comment out code. Use version control system to track changes
* ❗ Do not over comment

#### Copyright and file headers

* ❗ Do not add copyrights to the .cs files. Include a license file in the repo instead.

#### General guidelines

* ❗ Write comments in English
* 🔸 Avoid placing comments at the end of a line of code.
* ❗ Begin comment text with an uppercase letter.
* ❗ Insert one space between comment delimiter (//) and comment text.
* 🔸 Use // or /// and avoid but never /* ... */.
* ❗ The length of comment should not exceed the length of code.
* ❗ Don't create formatted blocks of asterisks around comments.
* 🔸 Ensure all public members have the necessary XML comments providing appropriate descriptions about their behavior.

```xml
/// <summary>
/// Get a value indicating whether the user has a license.
/// </summary>
/// <returns>
/// <c>true</c> if the user has a license; otherwise <c>false</c>.
/// </returns>

public bool HasLicense() { ... }
```

### Language Conventions

* ❗ Do no omit access modifiers. Explicitly declare all identifiers with the appropriate access modifiers instead of allowing defaults.

```csharp
// Good
private void WriteEvent(string message)

// Bad
void WriteEvent(string message)
```

* ❗ Use the built-in C# data type aliases, instead of the .NET common type system.

```csharp
// Good
short
int
long
string

// Bad
Int16
Int32
Int64
String
```

* 🔸 Keep methods short
> Shorter methods are usually simpler, easier to read, understand, review, test and evolve. Since the majority of the code exists inside methods, simpler methods reduce complexity for the entire code base at scale. Simpler code base allows developers to understand and evolve the code faster with fewer defects which increases the overall productivity.
> As an approximate guideline, 50-60 lines of code should be the max method size, with most methods being much smaller, and with very few methods being slightly larger. A reader should be able to rapidly understand a method's logic (a rule of thumb is within 10 seconds).
> Smaller methods have a single responsibility of doing one thing and doing it well. Lines of code have strong cohesion meaning they logically belong together and directly contribute to a single goal. A great principle to follow is the "Compose Method", defined in "Refactoring to Patterns":

> *Transform the logic into a small number of intention-revealing steps at the same level of detail*

> There are multiple benefits for keeping methods short. For example:
> > **Abstracting details**. Well-defined methods abstract related verbose logic behind a well-defined name and contract. Abstraction is our main tool for reducing complexity. If complexity is left unchecked, it will outgrow anyone's ability to understand and maintain the code, regardless of how smart people are.
> > **Self-documenting code**. More well-defined, smaller methods means more opportunities to encapsulate logic behind well-defined names which communicate additional meaning. In contrast, long sequences of anonymous, verbose lines in large methods increase the burden on the readers to identify and understand the logical steps.
> > **Simpler logic** across all levels. Higher level logic becomes simpler because it is composed of a small number of intention revealing steps (method calls) at the same level of detail. This composition principle can be applied across all levels of the system, thus, having a positive impact across the board.
> > **Reusable building blocks**. Identifying, encapsulating and naming a piece of logic often (though not always) makes it reusable in multiple contexts. This reduces duplication, verbosity, complexity and increases the agility to compose new logic faster from tested building blocks. It is a misconception that a new method should only be introduced if it is reusable. There are many other benefits (even beyond the ones listed here).
> > **Easier unit testing**. Smaller methods tend to have well defined inputs and outputs and fewer dependencies which naturally make them easier to unit test.
> > **Simpler implementation**. Smaller methods have fewer details to deal with which makes them easier to implement and maintain.
> > **Simpler naming**. Smaller methods have a well-defined scope where variables have strong, clear meaning. This allows using shorter and simpler parameter and variable names that otherwise would not be acceptable in a large method. In the example below, "first" and "second" are generally vague names that would not make sense in a larger method with many variables but in a well-defined method they are perfectly acceptable and even increase readability:

	```csharp
    bool AreEqual(T first, T second)…
    ```


*   var
    * ❗ Use var when you are using anonymous types.
    * 🔸 Use var when the type of the declaration is obvious from the initializer, especially if it is an object creation. This eliminates redundancy.
    * ❗ Use explicit types if doing so is necessary for the code to be correctly understood and maintained.
    * 🔸 Use descriptive variable names regardless of whether you use "var". Variable names should represent the semantics of the variable, not details of its storage; decimalRate is bad; interestRate is good.
    * ❗ Do not use var when the type is not apparent from the right side of the assignment.

```csharp
// Good
var welcomeMessage = "This is a welcome message!";
var account = new Account();

// Bad
var result = ExampleClass.ResultSoFar();
var firstPayment = principal * (rate / 12) + annualFees / 12 + closingCosts;
```

* 🔸 Favor Object and Collection initializers over separate statements.
```csharp
Cat cat = new Cat { Age = 10, Name = "Fluffy" };
Cat sameCat = new Cat("Fluffy"){ Age = 10 };
```
* 🔸 Avoid **this.** unless absolutely necessary.
* 🔸 use **nameof(...)** instead of "..." whenever possible and relevant.
* ❗ When including non-ASCII characters in the source code use Unicode escape sequences (\uXXXX) instead of literal characters. Literal non-ASCII characters occasionally get garbled by a tool or editor.
* ❗ Do not use goto and labels
* ❗ Avoid duplicate codes and classes
* ❗ Methods must have a clear and specific purpose and their name describes their purpose.
* 🔸 Avoid writing long methods and functions. Consider refactoring a method if you cannot see the entire method code on your screen.
* 🔸 Avoid writing long class files.
* ❗ Do not create instances that might not be used. It makes code less readable and makes more work for the GC

```csharp
// Bad
MyClass myClass = new MyClass();
if (someCondition) {
    myClass = new MyClass(20);
}
```
* 🔸 Don't depend on the state of global objects or the execution of methods. Instead, depend only on the return values of methods.
    *   Code will be easier to reason about.
    *   Code will be easier to test.
    *   Mixing async and synchronous code is far simpler.
    *   Race conditions can typically be avoided altogether.
    *   Depending on return values makes coordinating async code simple.
    *   (Bonus) it works really well with dependency injection.

* 🔸 Use a Guard to check the integrity of the preconditions to avoid errors during executions.
    *   Use ArgumentNullException.ThrowIfNull to check nulls only if your code uses .net 6 or newer only.

```csharp
// Use this approach
public Students GetStudents(Student student) {
    Guard.AgainstNull(student, nameof(student))
    // Write the code.
}

// This approach is not the best
public Students GetStudents(Student student) {
    if (student != null)
    {
        // Write the code.
    }
    else
    {
        Console.WriteLine("Student model do not contain any data");
    }
}

// In a common library, define this class
public static class Guard
{
    public static void AgainstNull(object argument, string argumentName)
    {
        if (argument == null)
        {
            throw new ArgumentNullException(argumentName);
        }
    }

    // Add more checks here
}
```

* 🔸 Use less coupled code
    *   Separate data classes from global objects and operations
    *   Split logic into several small simple methods
    *   Use dependency Injection
    *   Write less stateful code

#### Async Programming

Async programming improves responsiveness. Scenarios below are typical areas of using async programming:

*   Web Access (HttpClient)
*   Working with files (JsonSerializer, StreamReader, StreamWriter, XmlReader, XmlWriter)
*   Working with images (MediaCapture, BitmapEncoder, BitmapDecoder)
*   WCF and any kind of RPC.

With async programming, there are some details to keep in mind that can prevent unexpected behavior.

* ❗ async methods need to have an await keyword in their body or they will never yield. An exception for this case is when the async method returns Task.
* ❗ Add "Async" as the suffix of every async method name you write.
* ❗ async void should only be used for event handlers.
* 🔸 Avoid mixing LINQ with async code. LINQ uses deferred (lazy) execution and async calls won't happen immediately.
* 🔸 Write code that awaits Tasks in a non-blocking manner
* 🔸 Using Task is preferred over ValueTask. Use ValueTask when
    *   The async is expected to return synchronously very often
    *   Consumer always directly await it
    *   Performance implication outweigh usability implication
* ❗ Do not await ValueTask concurrently
* ❗ Do not await a ValueTask more than once.
* ❗ Do not ValueTask instance into a variable, it makes more likely to be misused

```csharp
// Bad
ValueTask<int> vt = SomeValueTaskReturningMethodAsync();
```
* 🔸Avoid using  GetAwaiter().GetResult()
```csharp
// Avoid
task.GetAwaiter().GetResult()

```

### Unit Tests

Regression defects are defects that are introduced when a change is made to the application. Unit testing provides confidence that new code doesn't break existing functionality.

When you have a suite of well-named unit tests, each test should be able to clearly explain the expected output for a given input. In addition, it should be able to verify that it actually works.

#### Less coupled code

When code is tightly coupled, it can be difficult to unit test. Without creating unit tests for the code that you're writing, coupling might be less apparent.

Writing tests for your code will naturally decouple your code, because it would be more difficult to test otherwise.

#### Characteristics of a good unit test

*   **Fast**: It isn't uncommon for mature projects to have thousands of unit tests. Unit tests should take little time to run, milliseconds.
*   **Isolated**: Unit tests are standalone, can be run in isolation, and have no dependencies on any outside factors such as a file system or database.
*   **Repeatable**: Running a unit test should be consistent with its results, that is, it always returns the same result if you don't change anything in between runs.
*   **Self-Checking**: The test should be able to automatically detect if it passed or failed without any human interaction.
*   **Timely**: A unit test shouldn't take a disproportionately long time to write compared to the code being tested. If you find testing the code taking a large amount of time compared to writing the code, consider a design that is more testable.

#### Best Practices

##### Code coverage

* ❗ Set a minimum code coverage for your team, and for every project. [This document](https://docs.google.com/document/d/15p6JED5fv3LF-dCEp7d1IgSW_MbVGdRYWblYnuJEtTo/edit#heading=h.wbs71pbeyf31) provides the minimum expected code coverage for MSF projects.
* 🔸 Code coverage should get better or remain the same in every PR.

##### Dependencies

* 🔸 Unit tests should not introduce dependencies on infrastructure and network. Dependencies make the tests slow and brittle and should be reserved for integration tests.
* 🔸 Avoid dependencies in your application by following the [Explicit Dependencies Principle](https://deviq.com/principles/explicit-dependencies-principle) and [using Dependency Injection](https://learn.microsoft.com/en-us/dotnet/core/extensions/dependency-injection).
* 🔸 Keep your unit tests in a separate project from your integration tests. This approach ensures your unit test project doesn't have references to or dependencies on infrastructure packages.

##### Test Project

* ❗ Test live in a separate project. Each project will have its own test project.
* ❗ Name unit test projects [Project].Tests
* 🔸 Prefer xUnit over other frameworks. If xUnit does not work for your project, use NUnit.
* 🔸 Place integration tests and other tests in separate projects
* ❗ All test projects should be directly under the tests folder. Do not use nested project folders. Alternatively, a team can choose to co-locate tests with libraries or services. It should be consistent within the repo.
* ❗ The folder hierarchy under the src folder must match with the folder hierarchy under the test folder.
* ❗ Add unit test projects to the build and make sure they will be executed in the build pipeline.

##### Test Namespaces and classes

* ❗ Each class will have one corresponding test class. The test class name should be **[ClassName]Tests**.
* ❗ Namespaces of any test class must match the class name that is being tested, with a **.Tests** after the project name.

##### Tests

* ❗ Each test should test one and only one act or scenario.
* ❗ Each test should test one internal or public method.
* 🔸 Consider naming your test consist of three parts:
    *   The name of the method being tested.
    *   The scenario under which it's being tested.
    *   The expected behavior when the scenario is invoked.

```csharp
// Bad
public void TestSingle()
{
    var stringCalculator = new StringCalculator();
    var actual = stringCalculator.Add("0");
    Assert.Equal(0, actual);
}

// Better
public void AddSingleNumberReturnsSameNumber()
{
    var stringCalculator = new StringCalculator();
    var actual = stringCalculator.Add("0");
    Assert.Equal(0, actual);
}
```

* 🔸 Use Arrange, Act, Assert pattern when unit testing. As the name implies, it consists of three main actions:
    *   Arrange your objects, create and set them up as necessary.
    *   Act on an object.
    *   Assert that something is as expected.

```csharp
// Bad
public void AddEmptyStringReturnsZero()
{
    // Arrange
    var stringCalculator = new StringCalculator();

    // Assert
    Assert.Equal(0, stringCalculator.Add(""));
}

// Better
public void AddEmptyStringReturnsZero()
{
    // Arrange
    var stringCalculator = new StringCalculator();

    // Act
    var actual = stringCalculator.Add("");

    // Assert
    Assert.Equal(0, actual);
}
```

* 🔸 When writing tests, you want to focus on the behavior. The input to be used in a unit test should be the simplest possible in order to verify the behavior that you're currently testing.
* 🔸 Avoid tests that include more information than required to pass the test. They have a higher chance of introducing errors into the test and can make the intent of the test less clear.
* ❗ Follow the Naming Conventions section to name the variables used in unit tests.
* 🔸 Use method local const values instead of magic string and numbers

```csharp
void AddMaximumSumResultThrowsOverflowException()
{
    var stringCalculator = new StringCalculator();
    const string MAXIMUM_RESULT = "1001";
    Action actual = () => stringCalculator.Add(MAXIMUM_RESULT);
    Assert.Throws<OverflowException>(actual);
}
```

* 🔸 Focus on the end result, rather than implementation details.
* ❗ Avoid logic such as manual string concatenation, logical conditions (if, while, for, and switch, and other conditions) in tests. If logic in your test seems unavoidable, consider splitting the test up into two or more different tests.
* 🔸 Prefer helper methods to setup and teardown tests
* ❗ Validate private methods by unit testing public/internal methods
* ❗ Avoid test interdependence
* ❗ Test positive and negative scenarios
* ❗ Test for Security Vulnerabilities

### More Things to Consider

#### Shared Project Properties

* ❗ Manage package version centrally in Directory.Packages.props file. Avoid using VersionOverride in csproj files, unless it is absolutely necessary.
* ❗ Use the latest version of packages, when possible.
* ❗ Use Directory.Build.props for common project properties.

#### Use Automated Code Analyzers

* 🔸 Enable static code analysis in Directory.Build.props:

```xml
<AnalysisLevel>latest</AnalysisLevel>

<!-- Use Default, Minimum, Recommended or All -->
<AnalysisMode>Recommended</AnalysisMode>
```

#### Security

* ❗ Do not hard code any token, username, password or secure data in the code, tests and resources.
* ❗ Do not generate SQL statements on the fly. It increases the chance of SQL Injection.

#### Logs

* ❗ Do not log tokens, username, passwords or any secure data.
* ❗ Do not log in an exception handler block, when the exception is rethrown.

#### Metrics

* 🔸 Emit metrics on errors
* 🔸 Avoid emitting a metric in an exception handler block when the exception is rethrown.
* 🔸 Emit metrics on unexpected cases

### Existing code and repos [Re-emphasized]

* ❗ Do not change the entire repository code style to follow this coding standard document, unless the code base is small and not published yet.
* ❗ Do not modify auto generated code to follow this coding standard.
* 🔸 When possible, generate code that follows this standard
* ❗ Indentation must be uniform in a repository

### PRs and Code reviews

* ❗ Write clear PR description along with a reference to the ticket and other documents
* ❗ No PR should break the production code if it is merged to production by mistake.
* ❗ Keep PRs small
* ❗ Accept that there are multiple correct solutions to a problem
* 🔸 PR should not reduce the code coverage

### Automated Tools

* ❗ Use the .editorconfig file that was generated during project creation
* 🔸 Use StyleCops

## Others

### Prefer explicit over implicit interface implementation
When a class implements an interface implicitly, the interface methods are defined as public which makes them part of the overall class interface. This leads to the following problems:
* Allows the interface methods to be called via class-typed reference. This violates the principle of coding against abstractions (in this case, interfaces). Implementing an interface explicitly only allows the interface methods to be invoked via interface-typed reference (compiler enforced), thus enforcing coding against abstractions.

```csharp
// Avoid: Invocation via class-typed reference which creates class coupling
// Rejected by the C# compiler if the interface is implemented explicitly
int CalculateSum(Calculator calculator)
{
    int result = calculator.Add(_first, _second);
    return result;
}

// Prefer: Invocation via interface-typed reference promoting coding against abstractions
int CalculateSum(ICalculator calculator)
{
    int result = calculator.Add(_first, _second);
    return result;
}
```


* Makes it hard to distinguish between class and interface methods in the class definition. This in turn makes it easier to add class-level public members that are not part of any interface, again, violating the principle of coding against abstractions. It also makes it quite hard for someone reading the code to understand if a public method belongs to the class interface or to an interface implemented by the class. It gets even harder to understand which method belongs to which interface if the class implements multiple interfaces.

There could be rare cases where implicit interface implementation is desirable, like adding get/set accessor to an interface-defined property. Those should only be used sparingly, only as exceptions, not by default.

### Avoid nested function calls
Nested function calls cause several complications which is why it is best to avoid them:
* Blurring the boundaries between function calls by forming a seemingly single, merged list of arguments. Here is a simple example to demonstrate the problem but in real world code it often looks worse due to longer names and lines:

```
F1(a, b, F2(c, d, F3(e, f), g, h), i)
```

* Changing the order in which the code is read vs. the order in which it is executed. Nested calls not only reverse the read vs execute order but can easily force the reader to go back and forth along a long line to follow the logic and the flow of data. In the example above a reader would read F1 first, then F2, then F3 but the order of execution is reverse F3, F2, F1. That creates an additional burden for a person reading the code and trying to understand it.
* Hiding the meaning of intermediate results. Unless named arguments are used, the values produced by nested function calls are anonymous. The function name does provide information but that often isn't enough to reveal the domain-specific intent.
Hiding the value of the intermediate result during debugging. It is harder to see in the debugger the return value of a nested function call.
*Making it easier to introduce bugs due to lack of function output validation and pre-processing before it is passed to the next function. A simple example could be that a function return value should be trimmed before passing it to another function. Generally, it is harder to see those kinds of problems on a single complex line with multiple calls because the reader is forced to deal with more details at once.

The main technique to avoid nested function calls and resolve pretty much all problems described above is to extract nested function calls on a separate line, assign the return value to a variable with intention-revealing descriptive name and use the variable as input to the next function call that needs it. Here is a hypothetical version which begins to add meaning and natural order to the earlier example:

```
city = F3(e, f);
address = F2(c, d, city, g, h);
person = F1(a, b, address, i);
```

### Do not pass literals as positional arguments
Programming languages support built-in literal keywords to specify values for many of the built-in types. For example:
* null
* true, false
* Numeric literals like 1, 100, 3.14, etc.
Passing such literals as positional arguments obscures their meaning. The reader is forced to look at the signature of the function being called. If that is done at scale, it leads to significant overhead for something that should have been made obvious in the first place. Here are a few examples:

- Boolean literal
```csharp
var type = Type.GetType(typeName, false);
```

It is not clear what it means to pass false here unless everyone knows/remembers how the GetType function works.

- Null literal

```csharp
new DsmsProxy(endpoint, null, isInteractive: isLocalBox)
```

Null is passed to the DsmsProxy constructor but it is not clear if that is the right choice. It is not clear what the meaning of null is. This code cannot be easily understood or verified unless the reader spends additional time learning more about the DsmsProxy constructor.

- Numeric literal

```csharp
Array.Copy(parameters, 0, values, 2, parameters.Length);
```

Same problem here. The reader has to spend extra time to understand and verify what should be a simple and obvious line of code. Not everyone remembers (or should be forced to remember) how all overloads of the Array.Copy work.

- Multiple literals

```csharp
Flags[(int)FlagName.InitFile] = new Flag('N', "init-file", AddInitFile, null, true);
```

This line is using several literals as positional arguments making the code look more cryptic and hard to understand.

If someone actually wants to understand or verify that such code is correct, they will have to dig deeper into documentation or function definition to find out, making a big context switch and wasting valuable time. Or, they are likely to conserve energy and let it go which means more parts of the code will become cryptic, less understood and carry higher risk of bugs.

While the VS editor may provide additional information on the second parameter, there are many other scenarios where that information will not be available. For example:
* Reviewing code in ADO or other code review environments
* Searching code in ADO or other environments
    * Editing code outside VS
Etc.

Here are some of the simple techniques to avoid this problem:

- Use named arguments
```csharp
var type = Type.GetType(typeName, throwOnError: false);
new DsmsProxy(endpoint, clientCert: null, isInteractive: isLocalBox)
Array.Copy(parameters, sourceIndex: 0, values, destinationIndex: 2, parameters.Length);
```

```csharp
// Good:
Array.Copy(parameters, sourceIndex: 0, values, destinationIndex: 2, parameters.Length);

// Bad:
var type = Type.GetType(typeName: typeName, throwOnError: false);
new DsmsProxy(endpoint: endpoint, clientCert: clientCert, isInteractive: isLocalBox);
Array.Copy(parameters, sourceIndex: 0, values, destinationIndex: 2, length: parameters.Length);
```

The author of the method has already thought about the meaning of the parameter and presumably has given it a descriptive name so just go ahead and use it.

- Named constants or variables
```csharp
const bool UseDefaultSerializer = true;

Add(typeof(BondAdHocSecretType), UseDefaultSerializer);
```
Names are one of the most important tools to communicate meaning that is important to people. While the compiler may not care about the names we choose, people reading our code, possibly for years to come, will benefit greatly from well-chosen, meaningful names.

### Correct implementation of Get and TryGet patterns
Implementing and using functions that return data is very common. It is very easy to get it wrong and cause significant negative effects across large parts of the code base. Here are the top level principles to follow:
* Get pattern: Functions that make a strong promise to provide a value must deliver on their promise or throw an exception!
* TryGet pattern: Functions that make a weak promise to provide a value must explicitly return success/failure status and out parameter value without throwing an exception!

One of the main goals of these principles is to eliminate the negative impact from the **anti-pattern of Get functions returning null**. Unfortunately, that is a very common anti-pattern in many code bases and even in the .NET Framework. Here is why it should be avoided. If Get functions are allowed to return null then developers have to account for that possibility and start adding null checks after each such call. Because the Get pattern is so common (most functions really) that can lead to significant increase in the code base size with adding verbose null check logic. For example, if a popular Get function is called 100 times then it could lead to 100 null checks and that is just one function. Such verbose code raises the maintenance cost and the risk of bugs. In addition, there is actually a good chance that such null checks will not be added everywhere because it is very easy to call a function without checking its return value for null. That can easily lead to hiding null reference problems until much later and much farther away from the source making them more difficult to troubleshoot. Because of the risk of missing null checks, developers start adding more null checks than needed. Because of replicating the null check code it also ends up being different - throwing different types of exceptions or not throwing at all.

The end result is that large parts of the code will have inconsistent mix of:
* Necessary null checks but with inconsistent verbose logic
* Unnecessary null checks that mislead people who read and maintain the code
* Missing null checks leading to masking problems until much later

That is why the correct implementation of Get pattern is to deliver a value or throw which eliminates all 3 problems listed above.

Developers make a choice which pattern to implement, Get or TryGet. Sometimes, throwing exceptions is not desirable so implementing TryGet is preferred. It is also not uncommon to provide both on the same class/interface in which case it is usually best to implement the Get pattern by using the TryGet implementation internally:

```csharp
if (!TryGet(…))
{
throw …
}
```

Another important question is how to consistently implement TryGet functions. One common invariant across most/all TryGet functions is that they must always return one of two possible combinations:
* True  +  valid out parameter
* False  +  non-deterministic out parameter

It is best to encode this invariant correlation between the return value and the out parameter inside the function body by implementing the following pattern:
```csharp
bool TryGet(out T output)
{
	// Always start by initializing the output parameter to null/default
	output = null;

	// Compute the output parameter value here. Avoid separate flags and multiple return statements
	…

	// Single return statement at the end with strong, explicit correlation between return value and output parameter value
	return output != null;
}
```

This works well for reference types. When the type of the output parameter is a value type, a different pattern may have to be implemented if there is no default value to use as "non-deterministic".

### Keep lines short
If the line is short then there isn't much room for complex code structure, hence, shorter lines are generally simpler. Shorter, simpler lines are easier to read, understand and verify. Shorter lines also keep the reader's attention into a "comfort window" instead of forcing them to scan or even scroll too much to the right. 160-180 characters is a reasonable max line length though most lines should be much shorter.

Constructs that usually make lines more complex:
* Long lists of function arguments
* Complex arithmetic and bool expressions
* Nested function calls
* Chained function calls
* Excessively long function, type, variable and property names

The main tool for simplifying a long, complex line is to split it into several smaller, simpler lines:
* Use a vertical list of arguments if a horizontal list of arguments is too long. Vertical list of arguments is usually easier to scan/read quickly. It makes it easier to review changes to the list as those changes are clearly seen on separate line(s). In contrast, changes to a single long line are harder to identify.
* Put every parameter on its own line which means no arguments are written on the line making the function call. This separates the reader's concerns between what function is being called vs. what inputs are being passed.
* Avoid wrapping a long horizontal list of arguments into multiple lines. This is the worst of both worlds as it forces the reader to follow the text both left to right and top to bottom.
* Use a vertical list of bool subexpressions instead of a horizontal list. For example:

```csharp
if ((expression1 && expression2) || (expression3 && expression4))

if ((expression1 && expression2) ||
	(expression3 && expression4))
```

* Pre-compute complex bool sub-expressions, assign the result to a local variable with intention revealing name and substitute the sub-expression with the variable. Be careful not to cause performance or correctness issues if the code relies on short-circuiting bool expression evaluation
* Techniques applicable to bool sub-expressions should be applicable to arithmetic expressions as well.
* Nested functions bring several complications to the code and are better to be avoided in most cases.
* Chained function calls have fewer problems compared to nested function calls but can also benefit from writing them on separate lines of code. One option is to split them into separate statements where the next call is a method call on the output of the preceding function. Another option is to keep them in a single statement but write each call on a separate line and align them in a vertical list.

## References

* [C# Coding Conventions (C# Programming Guide)](https://learn.microsoft.com/en-us/dotnet/csharp/fundamentals/coding-style/coding-conventions?redirectedfrom=MSDN) -- From Microsoft
* https://learn.microsoft.com/en-us/dotnet/csharp/language-reference/keywords/async
* https://learn.microsoft.com/en-us/dotnet/csharp/asynchronous-programming/task-asynchronous-programming-model

* https://learn.microsoft.com/en-us/dotnet/csharp/asynchronous-programming/async-scenarios
* [C# Coding Style Guide](http://www.icsharpcode.net/TechNotes/SharpDevelopCodingStyle03.pdf) -- By Mike Krüger
* [CSharpGuidelines](https://github.com/dennisdoomen/CSharpGuidelines) -- From Aviva Solutions
* https://learn.microsoft.com/en-us/dotnet/csharp/fundamentals/coding-style/identifier-names
* https://learn.microsoft.com/en-us/dotnet/csharp/fundamentals/coding-style/coding-conventions
* https://learn.microsoft.com/en-us/dotnet/core/testing/unit-testing-best-practices

### Project Structure

The solution follows a clean architecture pattern:

```
src/
├── TaskManagementService/              # Web API layer
├── TaskManagementService.Core/         # Business logic and domain models
├── TaskManagementService.Infrastructure/   # Data access and external services
├── TaskManagementService.Integration.Tests/
├── TaskManagementService.Tests/        # Unit tests
├── TaskManagementService.Perf.Tests/   # Performance tests
└── TaskManagementService.Periodic.Tests/  # Scheduled/periodic tests
```

### Dependency Management

* **Central Package Management:** Uses `Directory.Packages.props` for centralized package version management
* **Package References:** Add new packages to the appropriate `.csproj` file
* **Version Alignment:** Ensure all projects use consistent package versions

## Pull Request Process

### Before Submitting a PR

1. **Create a feature branch:**


```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes following the coding standards**

3. **Run the full test suite:**


```bash
   dotnet test
   ```

4. **Build in release mode:**


```bash
   dotnet build -c Release
   ```

5. **Format your code:**


```bash
   ./tools/format-code.sh
   ```

### PR Requirements

* **Description:** Provide a clear description of what the PR does
* **Tests:** Include appropriate unit tests for new functionality
* **Documentation:** Update documentation if you're changing behavior
* **Breaking Changes:** Clearly mark and justify any breaking changes
* **Code Coverage:** Maintain or improve existing code coverage

### PR Template Checklist

* [ ] Code builds without warnings
* [ ] All tests pass
* [ ] New functionality includes tests
* [ ] Documentation updated (if applicable)
* [ ] Breaking changes documented (if applicable)
* [ ] StyleCop analysis passes

## Testing

### Test Categories

1. **Unit Tests** (`TaskManagementService.Tests/`)
   - Test individual components in isolation
   - Use mocking for dependencies
   - Fast execution, no external dependencies

2. **Integration Tests** (`TaskManagementService.Integration.Tests/`)
   - Test component interactions
   - May use test databases or external services
   - Include API endpoint testing

3. **Performance Tests** (`TaskManagementService.Perf.Tests/`)
   - Load testing and performance benchmarks
   - Run separately from unit tests

### Running Tests

```bash
# Run all tests
dotnet test

# Run specific test project
dotnet test src/TaskManagementService.Tests/

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run tests in watch mode
dotnet watch test
```

### Test Guidelines

* **Arrange-Act-Assert (AAA)** pattern
* **Descriptive test names** that explain the scenario
* **One assertion per test** (when possible)
* **Use test data builders** for complex object creation
* **Mock external dependencies** properly

## Build and Deployment

### Local Build

```bash
# Development build
dotnet build

# Release build
dotnet build -c Release

# Publish for deployment
cd src/TaskManagementService
dotnet publish -c Release -f net8.0 -r linux-x64 --no-self-contained --verbosity minimal /p:DebugType=embedded
```

### CI/CD Pipeline

* **Trigger Branches:** `main` and `release/*`
* **PR Validation:** Automatic builds and tests for PRs to `main`
* **Azure Pipelines:** Configuration in `azure-pipelines.yml`

### Versioning

* **Semantic Versioning:** Major. Minor. Build. Revision
* **Automatic Versioning:** Build pipeline sets version numbers via environment variables
* **Local Development:** Defaults to *******

## Architecture Guidelines

### Domain-Driven Design

* **Core Layer:** Business entities, value objects, and domain services
* **Application Layer:** Use cases, commands, queries, and handlers
* **Infrastructure Layer:** Data persistence, external service integrations
* **Web Layer:** Controllers, middleware, and API configuration

### Design Patterns

* **CQRS (Command Query Responsibility Segregation)**
* **Repository Pattern** for data access
* **Dependency Injection** for IoC
* **Options Pattern** for configuration

### API Design

* **RESTful conventions**
* **OpenAPI/Swagger documentation**
* **Consistent error responses**
* **Proper HTTP status codes**
* **API versioning strategy**

### Security

* **JWT Bearer authentication**
* **Authorization policies**
* **Input validation**
* **HTTPS enforcement**
* **Security headers**

## Questions and Support

### Getting Help

* **Documentation:** Check the `docs/` folder for architectural decisions and guides
* **Issues:** Search existing issues before creating new ones
* **Discussions:** Use GitHub Discussions for questions and ideas

### Code Review Process

1. All PRs require at least one approval
2. Code owners (defined in `CODEOWNERS`) may be automatically requested
3. Automated checks must pass before merge
4. Prefer small, focused PRs over large changes

### Continuous Integration

* **Build Status:** All builds must pass
* **Test Coverage:** Maintain existing coverage levels
* **Security Scans:** Automated security analysis runs on PRs
* **Code Quality:** SonarQube analysis (if configured)

## Additional Resources

* [. NET Coding Conventions](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/inside-a-program/coding-conventions)
* [ASP. NET Core Best Practices](https://docs.microsoft.com/en-us/aspnet/core/fundamentals/best-practices)
* [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)
* [MSF Documentation](https://github.docusignhq.com/pages/Microservices/msf-docs/) (Internal)

---

Thank you for contributing to the Task Management Service! Your contributions help make this project better for everyone.
