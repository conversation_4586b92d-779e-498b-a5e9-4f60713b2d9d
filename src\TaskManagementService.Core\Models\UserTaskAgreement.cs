﻿#pragma warning disable SA1402
#pragma warning disable SA1134
#pragma warning disable CS8618
#pragma warning disable CA2227
#pragma warning disable CA1002

using System.Text.Json.Nodes;

namespace TaskManagementService.Core.Models;

public class UserTaskAgreement
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Model { get; set; }
    public string? Source { get; set; }
    public string? Version { get; set; }
    public string? Type { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset ModifiedAt { get; set; }
    public long Etag { get; set; }
    public AgreementData? Data { get; set; }
}

public class AgreementData
{
    public List<Party>? Parties { get; set; }
    public JsonNode? AgreementDocumentData { get; set; }
    public string? Name { get; set; }
    public Guid SourceId { get; set; }
    public List<string>? Languages { get; set; }
    public string SourceName { get; set; }
    public Value TotalValue { get; set; }
    public DateTimeOffset EffectiveDate { get; set; }
    public string AgreementStatus { get; set; }
    public Guid SourceAccountId { get; set; }
    public string? ExtractionStatus { get; set; }
    public Guid DocumentStorageId { get; set; }
    public string? SourceIngestionId { get; set; }
    public long PendingExtractionReviewCount { get; set; }
    public Guid Id { get; set; }
}

public class Party
{
    public PartyDetails? PartyDetails { get; set; }
    public string? DisplayName { get; set; }
    public ExtractionReview? ExtractionReview { get; set; }
}

public class ExtractionReview
{
    public long ExtractionCount { get; set; }
    public string Source { get; set; }
}

public class PartyDetails
{
    public Guid Id { get; set; }
}

public class TotalValue
{
    public Value Value { get; set; }
}

public class Value
{
    public double DoubleValue { get; set; }
    public string CurrencyCode { get; set; }
}
