﻿using System.Net.Http.Headers;

using Microsoft.Kiota.Abstractions.Authentication;

using TaskManagementService.Core.Interfaces;

namespace TaskManagementService.Infrastructure.Implementations;

public sealed class AuthorizationHeaderAccessTokenProvider(IRequestContextService requestContextService) : IAccessTokenProvider
{
    private const string BearerScheme = "Bearer";

    public AllowedHostsValidator AllowedHostsValidator { get; } = new AllowedHostsValidator(Array.Empty<string>());

    public Task<string> GetAuthorizationTokenAsync(
        Uri uri,
        Dictionary<string, object>? additionalAuthenticationContext = null,
        CancellationToken cancellationToken = default)
    {
        var authorization = requestContextService.Authorization;
        return AuthenticationHeaderValue.TryParse(authorization, out var headerValue) &&
               headerValue.Scheme.Equals(BearerScheme, StringComparison.OrdinalIgnoreCase) &&
               !string.IsNullOrWhiteSpace(headerValue.Parameter)
            ? Task.FromResult(headerValue.Parameter)
            : Task.FromResult(string.Empty);
    }
}
