﻿using System;

using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;

using Swashbuckle.AspNetCore.SwaggerGen;

using TaskManagementService.Middleware;

namespace TaskManagementService.Swagger;

public class RequiredHeadersOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        ArgumentNullException.ThrowIfNull(operation, nameof(operation));
        ArgumentNullException.ThrowIfNull(context, nameof(context));

        operation.Parameters?.Add(new OpenApiParameter
        {
            Name = RequestContextMiddleware.ShardIdHeaderName,
            In = ParameterLocation.Header,
            Required = true,
            Schema = new OpenApiSchema
            {
                Type = "string",
                Example = new OpenApiString("s1.us", false),
                Format = "string",
            },
        });
    }
}
