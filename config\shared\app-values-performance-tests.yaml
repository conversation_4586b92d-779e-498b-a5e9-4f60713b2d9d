experiment:
  enabled: true
  duration: 120m
  progressDeadlineSeconds: 900
  name: experiment
  analyses:
    - template: post-deployment

replicaCount: 1

autoscaling:
  enabled: false

virtualService:
  enabled: false

virtualServiceMesh:
  enabled: false

periodicTests:
  enabled: false

authorization:
  allow:
    enabled: true
    name: ingress-authz
    rules:
      - from:
          - source:
              principals:
                - "cluster.local/ns/{{ .Release.Namespace }}/sa/{{ .Release.Name }}-serviceaccount"
rollout:
  enabled: true
  analysisTemplate:
    spec:
      preDeploymentAnalysis: {}
      inflightDeploymentAnalysis: {}
      postDeploymentAnalysis:
        containers:
          - name: task-management-service-performance-tests
            image: task-management-service-performance-tests
            command: [ "/home/<USER>/app/run_perf_tests.sh" ]
            args: [ "http://$(RELEASE_NAME):80", "PostDeployment" ]
