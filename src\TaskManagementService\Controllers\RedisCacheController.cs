using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;

namespace TaskManagementService.Controllers;

[ExcludeFromCodeCoverage]
[Route("[controller]")]
public class RedisCacheController : Controller
{
    private readonly IDistributedCache _redisCache;
    private readonly ILogger<RedisCacheController> _logger;

    public RedisCacheController(IDistributedCache redisCache, ILogger<RedisCacheController> logger)
    {
        ArgumentNullException.ThrowIfNull(redisCache);
        _redisCache = redisCache;
        _logger = logger;
    }

    [HttpGet("read/{key}")]
    public async Task<IActionResult> ReadFromCacheAsync(string key)
    {
        _logger.LogInformation("Retrieving paired valued of {Key} from redis cache", key);
        var value = await _redisCache.GetStringAsync(key);
        if (value is null)
        {
            _logger.LogWarning("Key {Key} not found in redis cache", key);
            return NotFound();
        }

        return Ok(value);
    }

    [HttpPost("add/{key}:{value}")]
    public async Task<IActionResult> AddToCacheAsync(string key, string value)
    {
        _logger.LogInformation("Adding {Key} : {Value} into redis cache", key, value);
        await _redisCache.SetStringAsync(key, value);
        return Accepted();
    }
}
