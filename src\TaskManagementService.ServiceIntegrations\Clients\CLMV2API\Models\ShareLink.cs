// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class ShareLink : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The AllowNativeDownload property</summary>
        public bool? AllowNativeDownload { get; set; }
        /// <summary>The AllowPdfDownload property</summary>
        public bool? AllowPdfDownload { get; set; }
        /// <summary>The CreatedBy property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CreatedBy { get; set; }
#nullable restore
#else
        public string CreatedBy { get; set; }
#endif
        /// <summary>The CreatedDate property</summary>
        public DateTimeOffset? CreatedDate { get; set; }
        /// <summary>The Document property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document? Document { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document Document { get; set; }
#endif
        /// <summary>The ExpirationDate property</summary>
        public DateTimeOffset? ExpirationDate { get; set; }
        /// <summary>The Folder property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder? Folder { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder Folder { get; set; }
#endif
        /// <summary>The Href property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Href { get; set; }
#nullable restore
#else
        public string Href { get; set; }
#endif
        /// <summary>The PreviewBehavior property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? PreviewBehavior { get; set; }
#nullable restore
#else
        public string PreviewBehavior { get; set; }
#endif
        /// <summary>The UpdatedBy property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? UpdatedBy { get; set; }
#nullable restore
#else
        public string UpdatedBy { get; set; }
#endif
        /// <summary>The UpdatedDate property</summary>
        public DateTimeOffset? UpdatedDate { get; set; }
        /// <summary>The Url property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Url { get; set; }
#nullable restore
#else
        public string Url { get; set; }
#endif
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ShareLink"/> and sets the default values.
        /// </summary>
        public ShareLink()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ShareLink"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ShareLink CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ShareLink();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "AllowNativeDownload", n => { AllowNativeDownload = n.GetBoolValue(); } },
                { "AllowPdfDownload", n => { AllowPdfDownload = n.GetBoolValue(); } },
                { "CreatedBy", n => { CreatedBy = n.GetStringValue(); } },
                { "CreatedDate", n => { CreatedDate = n.GetDateTimeOffsetValue(); } },
                { "Document", n => { Document = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document.CreateFromDiscriminatorValue); } },
                { "ExpirationDate", n => { ExpirationDate = n.GetDateTimeOffsetValue(); } },
                { "Folder", n => { Folder = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder.CreateFromDiscriminatorValue); } },
                { "Href", n => { Href = n.GetStringValue(); } },
                { "PreviewBehavior", n => { PreviewBehavior = n.GetStringValue(); } },
                { "UpdatedBy", n => { UpdatedBy = n.GetStringValue(); } },
                { "UpdatedDate", n => { UpdatedDate = n.GetDateTimeOffsetValue(); } },
                { "Url", n => { Url = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteBoolValue("AllowNativeDownload", AllowNativeDownload);
            writer.WriteBoolValue("AllowPdfDownload", AllowPdfDownload);
            writer.WriteStringValue("CreatedBy", CreatedBy);
            writer.WriteDateTimeOffsetValue("CreatedDate", CreatedDate);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document>("Document", Document);
            writer.WriteDateTimeOffsetValue("ExpirationDate", ExpirationDate);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Folder>("Folder", Folder);
            writer.WriteStringValue("Href", Href);
            writer.WriteStringValue("PreviewBehavior", PreviewBehavior);
            writer.WriteStringValue("UpdatedBy", UpdatedBy);
            writer.WriteDateTimeOffsetValue("UpdatedDate", UpdatedDate);
            writer.WriteStringValue("Url", Url);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
