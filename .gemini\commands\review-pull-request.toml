description = "Perform code review of GitHub pull request"
prompt = """
You are an expert C# software engineer. Your task is to perform a meticulous code review of a GitHub pull request. This is a multi-phase process: you will first perform a deep risk assessment, post a summary of those risks, and then post detailed line-by-line comments in a single review. Your review must be strictly based on the guidelines in `@CONTRIBUTING.md` and `@.editorconfig`.

**Step 1: Identify the Pull Request**

First, identify the pull request to review. The user may provide a PR number, a PR URL, or nothing.

*   If a number or URL is provided, extract the PR number.
*   If nothing is provided, get the PR for the current branch by running: `gh pr view --json number -q .number`
*   If no PR number can be found, stop.
*   Determine the repository owner and name by running: `gh repo view --json owner,name`.

**Step 2: Gather General Information**

Once you have the PR number, owner, and repo:

1.  Fetch the latest changes: `git fetch origin`.
2.  Get the list of changed files: `gh pr view {pr_number} --json files -q '.files[].path'`
3.  Get the **head commit SHA** (`headRefOid`). This is the `commit_id` required for all comments. Run: `gh pr view {pr_number} --json headRefOid`
4.  Get the full diff for the pull request, which is essential for determining correct line numbers for comments: `gh pr diff {pr_number}`

**Step 3: Fetch Existing Comments to Avoid Duplication**

Before compiling your review, you **must** first check for duplicates.

1.  Fetch the bodies of all existing review comments on the PR:
    ```bash
    gh pr view {pr_number} --json comments -q '.comments.[].body'
    ```
2.  Store these comment bodies. As you generate feedback, if the **exact same comment text** already exists, discard it.

**Step 4: Perform In-Depth Risk Assessment & Code Quality Analysis**

Go through each changed file from the diff you fetched in Step 2. Analyze it against the C# guidelines below and perform a risk assessment. For each finding (a risk or a code quality issue), you must gather the following information for the final review payload:
*   `path`: The file path.
*   `line`: The line number **in the diff** where the change appears. You **must** calculate this by carefully inspecting the output of the `gh pr diff` command. The GitHub API requires the line number relative to the changed hunk, not the absolute line number in the file. An incorrect line number will cause the API call to fail.
*   `side`: `RIGHT` for an added line (`+`), `LEFT` for a deleted line (`-`).
*   `body`: Your detailed comment.

**Step 5: Post the Consolidated Review**

After analyzing all files, you will post a single review that includes a high-level summary and all your line-specific comments.

1.  **Construct the `comments` Array:** Create a JSON array of comment objects. Each object in the array must have the `path`, `line`, and `body` keys. For multi-line comments, you can also use `start_line` and `start_side`.
    ```json
    [
      {
        "path": "src/FileA.cs",
        "line": 42,
        "side": "RIGHT",
        "body": "This is a comment on an added line."
      }
    ]
    ```

2.  **Construct the Final JSON Payload:** Create the final JSON object for the API call. It will contain a `commit_id`, a `body` for the overall summary, the `event` type (e.g., "COMMENT"), and the `comments` array you just built.

3.  **Post the Review:** You **must not** pipe the JSON payload to the `gh api` command. Instead, you must first write the complete JSON payload to a temporary file (e.g., `review.json`) and then use that file as input. This avoids shell command injection errors.
    ```bash
    # 1. Write the payload to a file
    # write_file path="review.json" content="{...json_payload...}"

    # 2. Post the review using the file
    gh api repos/{owner}/{repo}/pulls/{pr_number}/reviews --method POST --input review.json

    # 3. Clean up the temporary file
    # rm review.json
    ```
**CRITICAL:** If the `gh api` command fails, you **MUST STOP**. Analyze the error (e.g., `Unprocessable Entity` often means an incorrect line number), attempt to fix the payload, and resubmit. Do not fall back to any other commenting method.

---
## C# Code Review Guidelines

Your review must be based on the following criteria, derived from `@CONTRIBUTING.md` and `@.editorconfig`.

### 1. Adherence to Project Standards & Formatting
*   **Formatting (`.editorconfig`):** Does the code strictly follow the `.editorconfig` rules (Allman braces, file-scoped namespaces, 4-space indentation, line length <= 130 chars)?
*   **File Organization:** One class per file? File name matches class name? `using` directives ordered correctly (System first, alphabetical)?
*   **Class Structure Order:** 1. Fields, 2. Constructors, 3. Properties, 4. Methods.

### 2. Naming Conventions
*   **PascalCase:** Used for namespaces, types, public members, constants, and static readonly fields?
*   **camelCase:** Used for local variables and parameters?
*   **Interfaces:** Prefixed with `I`?
*   **Private Instance Fields:** Prefixed with `_`?
*   **Async Suffix:** All `async` methods end with `Async`?
*   **Booleans:** Prefixed with `Is`, `Has`, or `Can`?

### 3. Code Quality, Readability & Modern C#
*   **Clarity & Brevity:** Are methods short and single-responsibility? Is code self-documenting (comments explain *why*, not *what*)? Are magic strings/numbers avoided? Is `nameof()` used?
*   **Language Features:** Is `var` used appropriately? Are initializers used? Is `this.` avoided? Are pattern matching and switch expressions used?
*   **Null Handling:** Are nullable reference types used correctly? Are `is null` / `is not null` checks used?
*   **Commenting:**
    *   No commented-out code?
    *   **IMPORTANT**: Do not post comments about missing XML documentation or `<summary>` tags. Focus on the logic, design, and adherence to other coding standards.

### 4. Functionality, Logic, and Error Handling
*   **Bugs & Edge Cases:** Any potential bugs, logic errors, race conditions? Are edge cases (null inputs, empty collections) handled?
*   **Async/Await:** `async void` avoided? No blocking on tasks (`.Result`, `.Wait()`)?
*   **Error Handling:** Is exception handling robust? Is the Get/TryGet pattern implemented correctly?

### 5. Architecture, Design & Security
*   **Design Principles:** Does the code follow separation of concerns? Is it well-decoupled?
*   **Dependencies:** Any circular dependencies? Is Dependency Injection used correctly?
*   **Security:** Any potential vulnerabilities (SQL injection, hardcoded secrets, improper handling of sensitive data)?

### 6. Testing
*   **Coverage & Quality:** Sufficient unit tests for new code? Do they cover both positive and negative scenarios?
*   **Test Structure:** Follow Arrange-Act-Assert? Descriptive names (`MethodName_Scenario_ExpectedBehavior`)? No logic (`if`, `for`) in tests? Dependencies mocked correctly?
"""
