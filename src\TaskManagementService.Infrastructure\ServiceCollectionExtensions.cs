using Microsoft.Kiota.Abstractions.Authentication;

using System.Text.Json.Nodes;

using DocuSign.AccountServer.TokenExchangeClient;

using Mapster;

using TaskManagementService.Core.Interfaces;
using TaskManagementService.Infrastructure.Implementations;
using TaskManagementService.Infrastructure.ServiceIntegrations.EnvelopeApi;
using TaskManagementService.Infrastructure.ServiceIntegrations.UnifiedRepositoryApi;
using TaskManagementService.Infrastructure.Services;
using TaskManagementService.Infrastructure.ServiceIntegrations.AccountServer;
using System.Net;
using Polly;
using Polly.Retry;

namespace TaskManagementService.Infrastructure;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, ILogger logger, IConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration, nameof(configuration));

        services
            .AddHttpClient<IUnifiedRepositoryService, UnifiedRepositoryService>()
            .AddStandardResilienceHandler(options => ConfigureStandardResilienceHandler(options, attemptTimeoutSeconds: 15));

        services
            .AddHttpClient<IEnvelopeApiService, EnvelopeApiService>()
            .AddStandardResilienceHandler(options => ConfigureStandardResilienceHandler(options, attemptTimeoutSeconds: 15));

        services.AddHttpClient<ITaskService, ClmTaskService>()
            .AddStandardResilienceHandler(options => ConfigureStandardResilienceHandler(options, attemptTimeoutSeconds: 30, circuitBreakerSamplingDurationSeconds: 60));

        services.AddScoped<ITaskService, ESignTaskService>();
        services.AddScoped<IESignTaskService, ESignTaskService>();
        services.AddScoped<IClmTaskService, ClmTaskService>();
        services.AddScoped<IUserService, ManagedUserService>();

        services.AddScoped<IAccessTokenProvider, AuthorizationHeaderAccessTokenProvider>();
        services.AddScoped<IAuthenticationProvider, BearerTokenAuthenticationProvider>();

        services.AddScoped<IAccountServerTokenExchangeClient, AccountServerTokenExchangeClient>();

        services.AddOptions<TokenExchangeClientSecretsManager>();

        services.Configure<TokenExchangeClientSecretsManager>(
            configuration.GetSection(TokenExchangeClientSecretsManager.Section));

        logger.LogInformation("{Project} services registered", "Infrastructure");

        TypeAdapterConfig<JsonNode, JsonNode>
            .NewConfig()
            .MapWith(jsonNode => jsonNode);

        return services;
    }

    private static void ConfigureStandardResilienceHandler(
        Microsoft.Extensions.Http.Resilience.HttpStandardResilienceOptions options,
        int attemptTimeoutSeconds,
        int totalRequestTimeoutSeconds = 120,
        int? circuitBreakerSamplingDurationSeconds = null)
    {
        options.Retry.ShouldHandle = static arguments => RetryShouldHandleAsync(arguments);
        options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(attemptTimeoutSeconds);
        options.TotalRequestTimeout.Timeout = TimeSpan.FromSeconds(totalRequestTimeoutSeconds);

        if (circuitBreakerSamplingDurationSeconds.HasValue)
        {
            options.CircuitBreaker.SamplingDuration = TimeSpan.FromSeconds(circuitBreakerSamplingDurationSeconds.Value);
        }
    }

    private static ValueTask<bool> RetryShouldHandleAsync(RetryPredicateArguments<HttpResponseMessage> args)
    {
        // Don't retry on success.
        if (args.Outcome.Exception is null && args.Outcome.Result?.IsSuccessStatusCode == true)
        {
            return PredicateResult.False();
        }

        // Don't retry on 4xx client errors, as they indicate a bad request that won't succeed on retry.
        if (args.Outcome.Result?.StatusCode is >= HttpStatusCode.BadRequest and < HttpStatusCode.InternalServerError)
        {
            return PredicateResult.False();
        }

        // Retry on all other failures (e.g., network exceptions or 5xx server errors).
        return PredicateResult.True();
    }
}
