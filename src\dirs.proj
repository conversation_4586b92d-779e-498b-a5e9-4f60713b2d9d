<Project Sdk="Microsoft.Build.Traversal">
  <ItemGroup>
    <ProjectReference Include="TaskManagementService/TaskManagementService.csproj" />
    <ProjectReference Include="TaskManagementService.Core/TaskManagementService.Core.csproj" />
    <ProjectReference Include="TaskManagementService.Infrastructure/TaskManagementService.Infrastructure.csproj" />
    <ProjectReference Include="TaskManagementService.Integration.Tests/TaskManagementService.Integration.Tests.csproj" />
    <ProjectReference Include="TaskManagementService.Periodic.Tests/TaskManagementService.Periodic.Tests.csproj" />
    <ProjectReference Include="TaskManagementService.ServiceIntegrations/TaskManagementService.ServiceIntegrations.csproj" />
    <ProjectReference Include="TaskManagementService.Tests/TaskManagementService.Tests.csproj" Pack="false" Publish="false" />
  </ItemGroup>
</Project>
