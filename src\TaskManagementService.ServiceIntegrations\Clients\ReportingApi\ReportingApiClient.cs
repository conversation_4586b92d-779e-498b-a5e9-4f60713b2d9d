// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Serialization.Json;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
using TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi
{
    /// <summary>
    /// The main entry point of the SDK, exposes the configuration and the fluent API.
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class ReportingApiClient : BaseRequestBuilder
    {
        /// <summary>The reportingAdhocQueriesIpr property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.ReportingAdhocQueriesIprRequestBuilder ReportingAdhocQueriesIpr
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.ReportingAdhocQueriesIprRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingApiClient"/> and sets the default values.
        /// </summary>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public ReportingApiClient(IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}", new Dictionary<string, object>())
        {
            ApiClientBuilder.RegisterDefaultSerializer<JsonSerializationWriterFactory>();
            ApiClientBuilder.RegisterDefaultDeserializer<JsonParseNodeFactory>();
        }
    }
}
#pragma warning restore CS0618
