using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

using Microsoft.Extensions.Logging;

using Moq;

using TaskManagementService.Core.Handlers;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

using Xunit;

namespace TaskManagementService.Tests.Handlers;

[Trait("TestType", "UnitTest")]
public class GetUserManagedUsersQueryHandlerTest
{
    [Fact]
    public void Handle()
    {
        var mockLogger = new Mock<ILogger<GetUserManagedUsersQueryHandler>>();
        var mockUserService = new Mock<IUserService>();

        var mockAccountId = new Guid("*************-0001-000b-************");

        var mockGetUserManagedUsersQuery =
            new GetUserManagedUsersQuery(mockAccountId);

        mockUserService
            .Setup(x => x.GetManagedUsersAsync(mockAccountId))
            .Returns(Task.FromResult(new List<ManagedUser>()));

        var getUserManagedUsersQueryHandler =
            new GetUserManagedUsersQueryHandler(mockLogger.Object, [mockUserService.Object]);

        var result = getUserManagedUsersQueryHandler.Handle(mockGetUserManagedUsersQuery, CancellationToken.None);

        Assert.NotNull(result);
    }

    [Fact]
    public async Task NullHandle()
    {
        var mockLogger = new Mock<ILogger<GetUserManagedUsersQueryHandler>>();
        var mockUserService = new Mock<IUserService>();

        var getUserManagedUsersQueryHandler =
            new GetUserManagedUsersQueryHandler(mockLogger.Object, [mockUserService.Object]);

        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            getUserManagedUsersQueryHandler.Handle(null, CancellationToken.None));
    }
}
