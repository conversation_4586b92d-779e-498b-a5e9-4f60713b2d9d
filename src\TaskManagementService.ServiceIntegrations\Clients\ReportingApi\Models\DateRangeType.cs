// <auto-generated/>
using System.Runtime.Serialization;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public enum DateRangeType
    #pragma warning restore CS1591
    {
        [EnumMember(Value = "None")]
        #pragma warning disable CS1591
        None,
        #pragma warning restore CS1591
        [EnumMember(Value = "LastTwelveMonths")]
        #pragma warning disable CS1591
        LastTwelveMonths,
        #pragma warning restore CS1591
        [EnumMember(Value = "LastThirtyDays")]
        #pragma warning disable CS1591
        LastThirtyDays,
        #pragma warning restore CS1591
        [EnumMember(Value = "LastThreeMonths")]
        #pragma warning disable CS1591
        LastThreeMonths,
        #pragma warning restore CS1591
        [EnumMember(Value = "LastSixMonths")]
        #pragma warning disable CS1591
        LastSixMonths,
        #pragma warning restore CS1591
        [EnumMember(Value = "LastNinetyDays")]
        #pragma warning disable CS1591
        LastNinetyDays,
        #pragma warning restore CS1591
        [EnumMember(Value = "LastMonth")]
        #pragma warning disable CS1591
        LastMonth,
        #pragma warning restore CS1591
        [EnumMember(Value = "LastYear")]
        #pragma warning disable CS1591
        LastYear,
        #pragma warning restore CS1591
        [EnumMember(Value = "NextTwelveMonths")]
        #pragma warning disable CS1591
        NextTwelveMonths,
        #pragma warning restore CS1591
        [EnumMember(Value = "NextFourteenDays")]
        #pragma warning disable CS1591
        NextFourteenDays,
        #pragma warning restore CS1591
        [EnumMember(Value = "NextTwentyFourHours")]
        #pragma warning disable CS1591
        NextTwentyFourHours,
        #pragma warning restore CS1591
        [EnumMember(Value = "NextThirtyDays")]
        #pragma warning disable CS1591
        NextThirtyDays,
        #pragma warning restore CS1591
        [EnumMember(Value = "NextThreeMonths")]
        #pragma warning disable CS1591
        NextThreeMonths,
        #pragma warning restore CS1591
        [EnumMember(Value = "NextSixMonths")]
        #pragma warning disable CS1591
        NextSixMonths,
        #pragma warning restore CS1591
        [EnumMember(Value = "NextSevenDays")]
        #pragma warning disable CS1591
        NextSevenDays,
        #pragma warning restore CS1591
        [EnumMember(Value = "NextSixtyDays")]
        #pragma warning disable CS1591
        NextSixtyDays,
        #pragma warning restore CS1591
        [EnumMember(Value = "NextNinetyDays")]
        #pragma warning disable CS1591
        NextNinetyDays,
        #pragma warning restore CS1591
        [EnumMember(Value = "NextOneTwentyDays")]
        #pragma warning disable CS1591
        NextOneTwentyDays,
        #pragma warning restore CS1591
        [EnumMember(Value = "NextMonth")]
        #pragma warning disable CS1591
        NextMonth,
        #pragma warning restore CS1591
        [EnumMember(Value = "NextYear")]
        #pragma warning disable CS1591
        NextYear,
        #pragma warning restore CS1591
        [EnumMember(Value = "ThisMonth")]
        #pragma warning disable CS1591
        ThisMonth,
        #pragma warning restore CS1591
        [EnumMember(Value = "ThisYear")]
        #pragma warning disable CS1591
        ThisYear,
        #pragma warning restore CS1591
        [EnumMember(Value = "Today")]
        #pragma warning disable CS1591
        Today,
        #pragma warning restore CS1591
        [EnumMember(Value = "Custom")]
        #pragma warning disable CS1591
        Custom,
        #pragma warning restore CS1591
    }
}
