// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class UpdateAssignmentRequest : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The description property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Description { get; set; }
#nullable restore
#else
        public string Description { get; set; }
#endif
        /// <summary>The dueDate property</summary>
        public DateTimeOffset? DueDate { get; set; }
        /// <summary>The expirationDate property</summary>
        public DateTimeOffset? ExpirationDate { get; set; }
        /// <summary>The metadata property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.UpdateAssignmentRequest_metadata? Metadata { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.UpdateAssignmentRequest_metadata Metadata { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.UpdateAssignmentRequest"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.UpdateAssignmentRequest CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.UpdateAssignmentRequest();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "description", n => { Description = n.GetStringValue(); } },
                { "dueDate", n => { DueDate = n.GetDateTimeOffsetValue(); } },
                { "expirationDate", n => { ExpirationDate = n.GetDateTimeOffsetValue(); } },
                { "metadata", n => { Metadata = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.UpdateAssignmentRequest_metadata>(global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.UpdateAssignmentRequest_metadata.CreateFromDiscriminatorValue); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("description", Description);
            writer.WriteDateTimeOffsetValue("dueDate", DueDate);
            writer.WriteDateTimeOffsetValue("expirationDate", ExpirationDate);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.UpdateAssignmentRequest_metadata>("metadata", Metadata);
        }
    }
}
#pragma warning restore CS0618
