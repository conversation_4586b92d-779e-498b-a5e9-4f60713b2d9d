// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using System;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Permissionsets;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Workflowqueues;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Workitems;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item
{
    /// <summary>
    /// Builds and executes requests for operations under \v2\{accountId}\members\{id}
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class MembersItemRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The groups property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups.GroupsRequestBuilder Groups
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups.GroupsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The permissionsets property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Permissionsets.PermissionsetsRequestBuilder Permissionsets
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Permissionsets.PermissionsetsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The workflowqueues property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Workflowqueues.WorkflowqueuesRequestBuilder Workflowqueues
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Workflowqueues.WorkflowqueuesRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The workitems property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Workitems.WorkitemsRequestBuilder Workitems
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Workitems.WorkitemsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.MembersItemRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public MembersItemRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/v2/{accountId}/members/{id}{?expand*}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.MembersItemRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public MembersItemRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/v2/{accountId}/members/{id}{?expand*}", rawUrl)
        {
        }
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member"/></returns>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member?> GetAsync(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.MembersItemRequestBuilder.MembersItemRequestBuilderGetQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member> GetAsync(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.MembersItemRequestBuilder.MembersItemRequestBuilderGetQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            var requestInfo = ToGetRequestInformation(requestConfiguration);
            return await RequestAdapter.SendAsync<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member>(requestInfo, global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member.CreateFromDiscriminatorValue, default, cancellationToken).ConfigureAwait(false);
        }
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member?> PatchAsync(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member> PatchAsync(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = ToPatchRequestInformation(body, requestConfiguration);
            return await RequestAdapter.SendAsync<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member>(requestInfo, global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member.CreateFromDiscriminatorValue, default, cancellationToken).ConfigureAwait(false);
        }
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member?> PutAsync(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member> PutAsync(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = ToPutRequestInformation(body, requestConfiguration);
            return await RequestAdapter.SendAsync<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member>(requestInfo, global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member.CreateFromDiscriminatorValue, default, cancellationToken).ConfigureAwait(false);
        }
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.MembersItemRequestBuilder.MembersItemRequestBuilderGetQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.MembersItemRequestBuilder.MembersItemRequestBuilderGetQueryParameters>> requestConfiguration = default)
        {
#endif
            var requestInfo = new RequestInformation(Method.GET, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json, application/scim+json");
            return requestInfo;
        }
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToPatchRequestInformation(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToPatchRequestInformation(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = new RequestInformation(Method.PATCH, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json, application/scim+json");
            requestInfo.SetContentFromParsable(RequestAdapter, "application/scim+json", body);
            return requestInfo;
        }
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToPutRequestInformation(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToPutRequestInformation(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = new RequestInformation(Method.PUT, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json, application/scim+json");
            requestInfo.SetContentFromParsable(RequestAdapter, "application/scim+json", body);
            return requestInfo;
        }
        /// <summary>
        /// Returns a request builder with the provided arbitrary URL. Using this method means any other path or query parameters are ignored.
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.MembersItemRequestBuilder"/></returns>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.MembersItemRequestBuilder WithUrl(string rawUrl)
        {
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.MembersItemRequestBuilder(rawUrl, RequestAdapter);
        }
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        #pragma warning disable CS1591
        public partial class MembersItemRequestBuilderGetQueryParameters 
        #pragma warning restore CS1591
        {
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            [QueryParameter("expand")]
            public string[]? Expand { get; set; }
#nullable restore
#else
            [QueryParameter("expand")]
            public string[] Expand { get; set; }
#endif
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class MembersItemRequestBuilderGetRequestConfiguration : RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.MembersItemRequestBuilder.MembersItemRequestBuilderGetQueryParameters>
        {
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class MembersItemRequestBuilderPatchRequestConfiguration : RequestConfiguration<DefaultQueryParameters>
        {
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class MembersItemRequestBuilderPutRequestConfiguration : RequestConfiguration<DefaultQueryParameters>
        {
        }
    }
}
#pragma warning restore CS0618
