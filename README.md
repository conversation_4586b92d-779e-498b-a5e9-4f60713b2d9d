# Task Management Service

Task Management Service aggregates tasks from various sources and provides an API to the [unified interface](https://github.docusignhq.com/FrontEndShared/task-management-widget).

## Local Development

### Build

```shell
# From the repository root
dotnet tool restore
dotnet build
```

### Run

```shell
cd src/TaskManagementService
dotnet run
```

### Test

```shell
# From the repository root
dotnet test
```

### Publish

```shell
cd src/TaskManagementService
dotnet publish -c Release -f net8.0 -r linux-x64 --no-self-contained --verbosity minimal /p:DebugType=embedded
```

### High Fidelity Development

There are 2 main options for testing the app with full functionality:

1. True Dev - Lets developers run their local changes directly in the MSF environment.
2. Docker Compose - Local Docker development.

#### True Dev

- [MSF Docs on True Dev](https://github.docusignhq.com/pages/Microservices/msf-docs/developer-productivity/msf-truedev-environment/)
- There is a known quirk where the local changes don't get picked up immediately. Touch the `Dockerfile` in `src/TaskManagmentService` to force a rebuild.

#### Docker Compose

The advantage of this method is that it runs [nginx](https://www.nginx.com/) as a reverse proxy and delegates SSL to it.

Run the following command from `/src/task-management-service`:

```shell
docker compose up --build
```

Connect to <https://localhost/health> to see version information.

To view logs of your service:

```shell
docker compose logs --follow --timestamps --no-log-prefix task-management-service
```

Stop containers and remove containers, networks, volumes, and images created by up:

```shell
docker compose down
```
