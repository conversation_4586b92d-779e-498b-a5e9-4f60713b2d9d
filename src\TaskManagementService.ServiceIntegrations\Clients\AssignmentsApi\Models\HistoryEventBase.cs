// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class HistoryEventBase : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The createdAt property</summary>
        public DateTimeOffset? CreatedAt { get; set; }
        /// <summary>The eventType property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? EventType { get; set; }
#nullable restore
#else
        public string EventType { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.HistoryEventBase"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.HistoryEventBase CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            var mappingValue = parseNode.GetChildNode("eventType")?.GetStringValue();
            return mappingValue switch
            {
                "AssigneeActionUpdatedHistoryEvent" => new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeActionUpdatedHistoryEvent(),
                "AssigneeAddedHistoryEvent" => new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeAddedHistoryEvent(),
                "AssigneeRemovedHistoryEvent" => new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeRemovedHistoryEvent(),
                "AssigneeReplacedHistoryEvent" => new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeReplacedHistoryEvent(),
                "AssignmentCancelledHistoryEvent" => new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCancelledHistoryEvent(),
                "AssignmentCompletedHistoryEvent" => new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCompletedHistoryEvent(),
                "AssignmentCreatedHistoryEvent" => new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentCreatedHistoryEvent(),
                "AssignmentExpiredHistoryEvent" => new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentExpiredHistoryEvent(),
                "AssignmentPropertiesUpdatedHistoryEvent" => new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentPropertiesUpdatedHistoryEvent(),
                "StageAddedHistoryEvent" => new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.StageAddedHistoryEvent(),
                _ => new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.HistoryEventBase(),
            };
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "createdAt", n => { CreatedAt = n.GetDateTimeOffsetValue(); } },
                { "eventType", n => { EventType = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteDateTimeOffsetValue("createdAt", CreatedAt);
            writer.WriteStringValue("eventType", EventType);
        }
    }
}
#pragma warning restore CS0618
