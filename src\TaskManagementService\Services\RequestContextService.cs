﻿using System;
using System.Collections.Generic;
using System.Security.Claims;

using TaskManagementService.Core.Interfaces;

namespace TaskManagementService.Services;

public class RequestContextService : IRequestContextService
{
    private readonly List<Claim> _claims = [];

    public Guid AccountId { get; set; }
    public Guid UserId { get; set; }
    public string AccountName { get; set; } = string.Empty;
    public string ShardId { get; set; } = string.Empty;
    public string Authorization { get; set; } = string.Empty;
    public string TraceParent { get; set; } = string.Empty;
    public string TraceToken { get; set; } = string.Empty;
    public string RequestPath { get; set; } = string.Empty;
    public IReadOnlyList<Claim> Claims => _claims;
    public Uri Issuer { get; set; } = null!;
    public bool IsClmAccount { get; set; }
    public Uri ESignApiBaseUrl { get; set; }
    public Uri ClmApiBaseUrl { get; set; }

    public void AddClaims(IEnumerable<Claim> claims)
    {
        _claims.AddRange(claims);
    }
}
