// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class AssigneeResponse : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The actingUserId property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? ActingUserId { get; set; }
#nullable restore
#else
        public string ActingUserId { get; set; }
#endif
        /// <summary>The action property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Action { get; set; }
#nullable restore
#else
        public string Action { get; set; }
#endif
        /// <summary>The assigneeId property</summary>
        public Guid? AssigneeId { get; set; }
        /// <summary>The group property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.GroupResponse? Group { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.GroupResponse Group { get; set; }
#endif
        /// <summary>The isGroup property</summary>
        public bool? IsGroup { get; set; }
        /// <summary>The weight property</summary>
        public int? Weight { get; set; }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeResponse"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeResponse CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssigneeResponse();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "actingUserId", n => { ActingUserId = n.GetStringValue(); } },
                { "action", n => { Action = n.GetStringValue(); } },
                { "assigneeId", n => { AssigneeId = n.GetGuidValue(); } },
                { "group", n => { Group = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.GroupResponse>(global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.GroupResponse.CreateFromDiscriminatorValue); } },
                { "isGroup", n => { IsGroup = n.GetBoolValue(); } },
                { "weight", n => { Weight = n.GetIntValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("actingUserId", ActingUserId);
            writer.WriteStringValue("action", Action);
            writer.WriteGuidValue("assigneeId", AssigneeId);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.GroupResponse>("group", Group);
            writer.WriteBoolValue("isGroup", IsGroup);
            writer.WriteIntValue("weight", Weight);
        }
    }
}
#pragma warning restore CS0618
