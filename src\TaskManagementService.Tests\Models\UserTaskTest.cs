using System;
using System.Linq;
using System.Threading.Tasks;

using TaskManagementService.Core.Handlers;
using TaskManagementService.Core.Models;

using Xunit;

namespace TaskManagementService.Tests.Models;

[Trait("TestType", "UnitTest")]
public class UserTaskTest
{
    [Fact]
    public void AddAssignment()
    {
        var mockTaskId = new Guid("*************-0001-000b-000000000003");
        var mockAgreementId = new Guid("*************-0001-000b-000000000003");

        var mockUserTaskAgreement = new UserTaskAgreement() { Id = mockAgreementId };

        var task = new UserTask() { Id = mockTaskId };

        task.AddAgreement(mockUserTaskAgreement);

        Assert.Single(task.Agreements);
    }

    [Fact]
    public void AddAssignmentNullExceptionAsync()
    {
        var mockTaskId = new Guid("*************-0001-000b-000000000003");

        var task = new UserTask() { Id = mockTaskId };

        var exception = Record.Exception(() => task.AddAgreement(null));

        Assert.NotNull(exception);
        Assert.IsType<ArgumentNullException>(exception);
    }
}
