{
    "cSpell.words": [
        "apiserver",
        "authz",
        "buildkit",
        "containerd",
        "crio",
        "docusign",
        "docusignhq",
        "healthz",
        "Hsts",
        "ingressgateway",
        "istio",
        "kubectl",
        "kubelet",
        "Kubernetes",
        "minikube",
        "OTEL",
        "Otlp",
        "Unencrypted",
        "Xunit"
    ],
    "editor.formatOnSave": true,
    "editor.insertSpaces": true,
    "files.trimTrailingWhitespace": true,
    "shellformat.useEditorConfig": true,
    "[sh]": {
        "editor.defaultFormatter": "foxundermoon.shell-format"
    },
    "[shellscript]": {
        "editor.defaultFormatter": "foxundermoon.shell-format"
    },
    "[markdown]": {
        "editor.defaultFormatter": "mervin.markdown-formatter",
        "editor.quickSuggestions": {
            "other": true,
            "comments": true,
            "strings": true
        },
        "editor.renderWhitespace": "all",
    },
    "dotnet.defaultSolution": "dirs.sln"
}
