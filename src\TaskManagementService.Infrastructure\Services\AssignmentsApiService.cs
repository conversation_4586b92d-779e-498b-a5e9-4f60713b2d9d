using Microsoft.Kiota.Abstractions.Authentication;
using Microsoft.Kiota.Http.HttpClientLibrary;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Infrastructure.Implementations;
using TaskManagementService.Infrastructure.ServiceIntegrations.AccountServer;
using TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi;

namespace TaskManagementService.Infrastructure.Services;

public class AssignmentsApiService(IAssignmentsApiConfig assignmentsApiConfig, HttpClient httpClient, IAccountServerTokenExchangeClient accountServerTokenExchangeClient, IRequestContextService requestContextService) : ITaskService
{
    private AssignmentsApiClient? GetAssignmentsApiClient()
    {
        if (requestContextService.ClmApiBaseUrl == null)
        {
            throw new InvalidOperationException("CLM API Base URL is null");
        }

        var authProvider = new BaseBearerTokenAuthenticationProvider(new ApplicationTokenProvider(accountServerTokenExchangeClient));

        using var requestAdapter = new HttpClientRequestAdapter(authProvider, httpClient: httpClient);
        var baseUrl = new Uri(requestContextService.ClmApiBaseUrl, "/assignments");
        requestAdapter.BaseUrl = baseUrl.ToString();

        var assignmentsClient = new AssignmentsApiClient(requestAdapter);
        return assignmentsClient;
    }

    public async Task<List<UserTask>> GetTasksAsync(Guid accountId, TaskFilter taskFilter)
    {
        ArgumentNullException.ThrowIfNull(taskFilter, nameof(taskFilter));

        if (!assignmentsApiConfig.Enabled)
        {
            return [];
        }

        var assignmentsClient = GetAssignmentsApiClient();
        if (assignmentsClient == null)
        {
            return [];
        }

        var response = await assignmentsClient.Api.V1[accountId.ToString()].Assignments.Assignees[requestContextService.UserId].GetAsync();

        if (response?.Items != null)
        {
            return response.Items.Select(item => new UserTask
            {
                Id = item.AssignmentId ?? Guid.Empty,
                Source = TaskSource.AssignmentsApi,
                Description = item.Description ?? string.Empty,
                CreatedDate = item.CreatedDate?.ToString("o") ?? string.Empty,
                DueDate = item.ExpirationDate?.ToString("o") ?? string.Empty,
                AssignedDate = item.CreatedDate?.ToString("o") ?? string.Empty,
                NavigationUrl = $"/atlas/Documents/View?Id={item.ContextId}&SubContextId={item.SubContextId}"
            })
             .Where(taskFilter.ExtraAssignmentTaskFilter).ToList();
        }

        return [];
    }

    public Task<List<TaskGroup>> GetTasksGroupsAsync(Guid accountId, bool includeMembers) => Task.FromResult(new List<TaskGroup>());
}
