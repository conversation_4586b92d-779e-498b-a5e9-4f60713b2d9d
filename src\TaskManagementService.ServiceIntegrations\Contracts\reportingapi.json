{"openapi": "3.0.1", "info": {"title": "ReportingAPI Swagger UI", "description": "Below are swagger endpoints", "version": "v1.0"}, "paths": {"/reporting-adhoc-queries-ipr/v1.0/query": {"post": {"tags": ["AdHocReporting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/QueryRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}}}}}}, "/reporting-adhoc-queries-ipr/v1.0/query/count": {"post": {"tags": ["AdHocReporting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/QueryRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}}}}}}, "/reporting-adhoc-queries-ipr/v1.0/query/models": {"get": {"tags": ["AdHocReporting"], "parameters": [{"name": "accountId", "in": "query", "schema": {"type": "string"}}, {"name": "modelType", "in": "query", "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AdHocModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AdHocModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AdHocModel"}}}}}}}}, "/reporting-adhoc-queries-ipr/v1.0/query/models/details": {"get": {"tags": ["AdHocReporting"], "parameters": [{"name": "accountId", "in": "query", "schema": {"type": "string"}}, {"name": "modelId", "in": "query", "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdHocModelDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdHocModelDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdHocModelDetails"}}}}}}}, "/reporting-adhoc-queries-ipr/v1.0/accounts/{accountId}/agreementReports/custom": {"post": {"tags": ["CustomAgreementReports"], "parameters": [{"name": "accountId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/URCustomReportRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/URCustomReportRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/URCustomReportRequest"}}}}, "responses": {"400": {"description": "Bad Request"}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}}}}}}, "/reporting-adhoc-queries-ipr/v1.0/accounts/{accountId}/agreementReports/custom/{entityId}": {"put": {"tags": ["CustomAgreementReports"], "parameters": [{"name": "entityId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "accountId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/URCustomReportRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/URCustomReportRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/URCustomReportRequest"}}}}, "responses": {"400": {"description": "Bad Request"}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}}}}}, "get": {"tags": ["CustomAgreementReports"], "parameters": [{"name": "entityId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "accountId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}}}}}, "delete": {"tags": ["CustomAgreementReports"], "parameters": [{"name": "entityId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "accountId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}}}}}}, "/reporting-adhoc-queries-ipr/v1.0/reports": {"post": {"tags": ["InProductReporting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/QueryRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}}}}}}, "/reporting-adhoc-queries-ipr/v1.0/unifiedRepositoryReports": {"post": {"tags": ["InProductReporting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}}}}}}, "/reporting-adhoc-queries-ipr/v1.0/agreementReports": {"post": {"tags": ["InProductReporting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}}}}}}, "/reporting-adhoc-queries-ipr/v3/accounts/{accountId}/agreementReports": {"post": {"tags": ["InProductReporting"], "parameters": [{"name": "accountId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}}}}}}, "/reporting-adhoc-queries-ipr/v1.0/accounts/{accountId}/agreementReports": {"post": {"tags": ["InProductReporting"], "parameters": [{"name": "accountId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}}}}}}, "/reporting-adhoc-queries-ipr/v1.0/unifiedRepositoryReports/csv": {"post": {"tags": ["InProductReporting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/URQueryRequest"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/QueryResponse"}}}}}}}, "/reporting-adhoc-queries-ipr/v1.0/version": {"get": {"tags": ["Version"], "responses": {"200": {"description": "Success"}}}}}, "components": {"schemas": {"AdHocModel": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "modelId": {"type": "string", "nullable": true}, "modelType": {"type": "string", "nullable": true}, "owner": {"type": "string", "nullable": true}, "ownerId": {"type": "string", "nullable": true}, "createdDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "AdHocModelDetails": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/AdHocModelField"}, "nullable": true}}, "additionalProperties": false}, "AdHocModelField": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "availableConditionals": {"type": "array", "items": {"type": "string"}, "nullable": true}, "values": {"type": "array", "items": {"$ref": "#/components/schemas/AdHocModelFieldValue"}, "nullable": true}, "multiSelect": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "AdHocModelFieldValue": {"type": "object", "properties": {"label": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AggregateFunction": {"enum": ["COUNT", "MIN", "MAX"], "type": "string"}, "Aggregation": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "multiSelect": {"type": "boolean"}, "function": {"$ref": "#/components/schemas/AggregateFunction"}, "values": {"type": "array", "items": {"$ref": "#/components/schemas/AdHocModelFieldValue"}, "nullable": true}}, "additionalProperties": false}, "Conditional": {"type": "object", "properties": {"condition": {"type": "string", "nullable": true}, "values": {"type": "array", "items": {}, "nullable": true}}, "additionalProperties": false}, "DateRangeType": {"enum": ["None", "LastTwelveMonths", "LastThirtyDays", "LastThreeMonths", "LastSixMonths", "LastNinetyDays", "LastMonth", "LastYear", "NextTwelveMonths", "NextFourteenDays", "NextTwentyFourHours", "NextThirtyDays", "NextThreeMonths", "NextSixMonths", "NextSevenDays", "NextSixtyDays", "NextNinetyDays", "NextOneTwentyDays", "NextMonth", "NextYear", "ThisMonth", "ThisYear", "Today", "Custom"], "type": "string"}, "Field": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "multiSelect": {"type": "boolean"}, "gate": {"type": "string", "nullable": true}, "conditionals": {"type": "array", "items": {"$ref": "#/components/schemas/Conditional"}, "nullable": true}}, "additionalProperties": false}, "Filter": {"type": "object", "properties": {"gate": {"type": "string", "nullable": true}, "fields": {"type": "array", "items": {"$ref": "#/components/schemas/Field"}, "nullable": true}, "userIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "includedEnvelopeStatuses": {"type": "array", "items": {"type": "string"}, "nullable": true}, "recipientNameSubstring": {"type": "string", "nullable": true}, "subjectSubstring": {"type": "string", "nullable": true}, "timeRanges": {"type": "array", "items": {"$ref": "#/components/schemas/TimeRange"}, "nullable": true}, "compareSentInitialDate": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "FilterValues": {"type": "object", "properties": {"operator": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "range": {"$ref": "#/components/schemas/RangeValue"}, "dateRange": {"$ref": "#/components/schemas/DateRangeType"}}, "additionalProperties": false}, "OrderBy": {"type": "object", "properties": {"column": {"type": "string", "nullable": true}, "direction": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "Projection": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "multiSelect": {"type": "boolean"}}, "additionalProperties": false}, "QueryRequest": {"type": "object", "properties": {"requestId": {"type": "string", "format": "uuid"}, "type": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "accountId": {"type": "string", "nullable": true}, "aggregation": {"$ref": "#/components/schemas/Aggregation"}, "projections": {"type": "array", "items": {"$ref": "#/components/schemas/Projection"}, "nullable": true}, "filters": {"$ref": "#/components/schemas/Filter"}, "startDate": {"type": "string", "nullable": true}, "endDate": {"type": "string", "nullable": true}, "count": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}, "getTotalCount": {"type": "boolean"}, "getTotalCountOnly": {"type": "boolean"}}, "additionalProperties": false}, "QueryResponse": {"type": "object", "properties": {"requestId": {"type": "string", "format": "uuid"}, "resultCount": {"type": "integer", "format": "int32"}, "totalRowCount": {"type": "integer", "format": "int32", "nullable": true}, "totalAggregateCount": {"type": "integer", "format": "int32", "nullable": true}, "calculatedStartDate": {"type": "string", "nullable": true}, "calculatedEndDate": {"type": "string", "nullable": true}, "groupBy": {"type": "string", "nullable": true}, "aggregate": {"type": "string", "nullable": true}, "timeBucket": {"$ref": "#/components/schemas/TimeBucket"}, "results": {"type": "array", "items": {"type": "object", "additionalProperties": {}}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "RangeValue": {"type": "object", "properties": {"min": {"type": "string", "nullable": true}, "max": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TimeBucket": {"enum": ["Daily", "Weekly", "Monthly", "Yearly", "All"], "type": "string"}, "TimeRange": {"type": "object", "properties": {"start": {"type": "string", "format": "date-time"}, "end": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "URCustomReportRequest": {"type": "object", "properties": {"requestId": {"type": "string", "format": "uuid"}, "accountId": {"type": "string", "nullable": true}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/URFilter"}, "nullable": true}, "startDate": {"type": "string", "nullable": true}, "endDate": {"type": "string", "nullable": true}, "dateRange": {"$ref": "#/components/schemas/DateRangeType"}, "timeBucket": {"$ref": "#/components/schemas/TimeBucket"}, "aggregate": {"type": "string", "nullable": true}, "groupBy": {"type": "string", "nullable": true}, "orderBy": {"$ref": "#/components/schemas/OrderBy"}, "type": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "csvDownload": {"type": "boolean"}, "count": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}, "reportName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "URFilter": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "values": {"$ref": "#/components/schemas/FilterValues"}, "anyOfValues": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "URQueryRequest": {"type": "object", "properties": {"requestId": {"type": "string", "format": "uuid"}, "accountId": {"type": "string", "nullable": true}, "filters": {"type": "array", "items": {"$ref": "#/components/schemas/URFilter"}, "nullable": true}, "startDate": {"type": "string", "nullable": true}, "endDate": {"type": "string", "nullable": true}, "dateRange": {"$ref": "#/components/schemas/DateRangeType"}, "timeBucket": {"$ref": "#/components/schemas/TimeBucket"}, "aggregate": {"type": "string", "nullable": true}, "groupBy": {"type": "string", "nullable": true}, "orderBy": {"$ref": "#/components/schemas/OrderBy"}, "type": {"type": "string", "nullable": true}, "userId": {"type": "string", "nullable": true}, "csvDownload": {"type": "boolean"}, "count": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}}, "additionalProperties": false}}}}