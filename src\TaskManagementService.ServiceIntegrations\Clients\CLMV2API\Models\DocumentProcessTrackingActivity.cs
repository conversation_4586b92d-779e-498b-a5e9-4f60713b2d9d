// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class DocumentProcessTrackingActivity : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The Document property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document? Document { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document Document { get; set; }
#endif
        /// <summary>The DueDate property</summary>
        public DateTimeOffset? DueDate { get; set; }
        /// <summary>The EndDate property</summary>
        public DateTimeOffset? EndDate { get; set; }
        /// <summary>The Href property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Href { get; set; }
#nullable restore
#else
        public string Href { get; set; }
#endif
        /// <summary>The Name property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Name { get; set; }
#nullable restore
#else
        public string Name { get; set; }
#endif
        /// <summary>The Output property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Output { get; set; }
#nullable restore
#else
        public string Output { get; set; }
#endif
        /// <summary>The ProcessName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? ProcessName { get; set; }
#nullable restore
#else
        public string ProcessName { get; set; }
#endif
        /// <summary>The ProcessUid property</summary>
        public Guid? ProcessUid { get; set; }
        /// <summary>The StageName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? StageName { get; set; }
#nullable restore
#else
        public string StageName { get; set; }
#endif
        /// <summary>The StartDate property</summary>
        public DateTimeOffset? StartDate { get; set; }
        /// <summary>The Status property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Status { get; set; }
#nullable restore
#else
        public string Status { get; set; }
#endif
        /// <summary>The TypeName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? TypeName { get; set; }
#nullable restore
#else
        public string TypeName { get; set; }
#endif
        /// <summary>The UserActions property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionUserAction? UserActions { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionUserAction UserActions { get; set; }
#endif
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.DocumentProcessTrackingActivity"/> and sets the default values.
        /// </summary>
        public DocumentProcessTrackingActivity()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.DocumentProcessTrackingActivity"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.DocumentProcessTrackingActivity CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.DocumentProcessTrackingActivity();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "Document", n => { Document = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document.CreateFromDiscriminatorValue); } },
                { "DueDate", n => { DueDate = n.GetDateTimeOffsetValue(); } },
                { "EndDate", n => { EndDate = n.GetDateTimeOffsetValue(); } },
                { "Href", n => { Href = n.GetStringValue(); } },
                { "Name", n => { Name = n.GetStringValue(); } },
                { "Output", n => { Output = n.GetStringValue(); } },
                { "ProcessName", n => { ProcessName = n.GetStringValue(); } },
                { "ProcessUid", n => { ProcessUid = n.GetGuidValue(); } },
                { "StageName", n => { StageName = n.GetStringValue(); } },
                { "StartDate", n => { StartDate = n.GetDateTimeOffsetValue(); } },
                { "Status", n => { Status = n.GetStringValue(); } },
                { "TypeName", n => { TypeName = n.GetStringValue(); } },
                { "UserActions", n => { UserActions = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionUserAction>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionUserAction.CreateFromDiscriminatorValue); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document>("Document", Document);
            writer.WriteDateTimeOffsetValue("DueDate", DueDate);
            writer.WriteDateTimeOffsetValue("EndDate", EndDate);
            writer.WriteStringValue("Href", Href);
            writer.WriteStringValue("Name", Name);
            writer.WriteStringValue("Output", Output);
            writer.WriteStringValue("ProcessName", ProcessName);
            writer.WriteGuidValue("ProcessUid", ProcessUid);
            writer.WriteStringValue("StageName", StageName);
            writer.WriteDateTimeOffsetValue("StartDate", StartDate);
            writer.WriteStringValue("Status", Status);
            writer.WriteStringValue("TypeName", TypeName);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionUserAction>("UserActions", UserActions);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
