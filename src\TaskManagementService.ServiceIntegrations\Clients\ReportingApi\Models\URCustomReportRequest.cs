// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class URCustomReportRequest : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The accountId property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? AccountId { get; set; }
#nullable restore
#else
        public string AccountId { get; set; }
#endif
        /// <summary>The aggregate property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Aggregate { get; set; }
#nullable restore
#else
        public string Aggregate { get; set; }
#endif
        /// <summary>The count property</summary>
        public int? Count { get; set; }
        /// <summary>The csvDownload property</summary>
        public bool? CsvDownload { get; set; }
        /// <summary>The dateRange property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.DateRangeType? DateRange { get; set; }
        /// <summary>The endDate property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? EndDate { get; set; }
#nullable restore
#else
        public string EndDate { get; set; }
#endif
        /// <summary>The filters property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.URFilter>? Filters { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.URFilter> Filters { get; set; }
#endif
        /// <summary>The groupBy property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? GroupBy { get; set; }
#nullable restore
#else
        public string GroupBy { get; set; }
#endif
        /// <summary>The offset property</summary>
        public int? Offset { get; set; }
        /// <summary>The orderBy property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.OrderBy? OrderBy { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.OrderBy OrderBy { get; set; }
#endif
        /// <summary>The reportName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? ReportName { get; set; }
#nullable restore
#else
        public string ReportName { get; set; }
#endif
        /// <summary>The requestId property</summary>
        public Guid? RequestId { get; set; }
        /// <summary>The startDate property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? StartDate { get; set; }
#nullable restore
#else
        public string StartDate { get; set; }
#endif
        /// <summary>The timeBucket property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.TimeBucket? TimeBucket { get; set; }
        /// <summary>The type property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Type { get; set; }
#nullable restore
#else
        public string Type { get; set; }
#endif
        /// <summary>The userId property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? UserId { get; set; }
#nullable restore
#else
        public string UserId { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.URCustomReportRequest"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.URCustomReportRequest CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.URCustomReportRequest();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "accountId", n => { AccountId = n.GetStringValue(); } },
                { "aggregate", n => { Aggregate = n.GetStringValue(); } },
                { "count", n => { Count = n.GetIntValue(); } },
                { "csvDownload", n => { CsvDownload = n.GetBoolValue(); } },
                { "dateRange", n => { DateRange = n.GetEnumValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.DateRangeType>(); } },
                { "endDate", n => { EndDate = n.GetStringValue(); } },
                { "filters", n => { Filters = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.URFilter>(global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.URFilter.CreateFromDiscriminatorValue)?.AsList(); } },
                { "groupBy", n => { GroupBy = n.GetStringValue(); } },
                { "offset", n => { Offset = n.GetIntValue(); } },
                { "orderBy", n => { OrderBy = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.OrderBy>(global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.OrderBy.CreateFromDiscriminatorValue); } },
                { "reportName", n => { ReportName = n.GetStringValue(); } },
                { "requestId", n => { RequestId = n.GetGuidValue(); } },
                { "startDate", n => { StartDate = n.GetStringValue(); } },
                { "timeBucket", n => { TimeBucket = n.GetEnumValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.TimeBucket>(); } },
                { "type", n => { Type = n.GetStringValue(); } },
                { "userId", n => { UserId = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("accountId", AccountId);
            writer.WriteStringValue("aggregate", Aggregate);
            writer.WriteIntValue("count", Count);
            writer.WriteBoolValue("csvDownload", CsvDownload);
            writer.WriteEnumValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.DateRangeType>("dateRange", DateRange);
            writer.WriteStringValue("endDate", EndDate);
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.URFilter>("filters", Filters);
            writer.WriteStringValue("groupBy", GroupBy);
            writer.WriteIntValue("offset", Offset);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.OrderBy>("orderBy", OrderBy);
            writer.WriteStringValue("reportName", ReportName);
            writer.WriteGuidValue("requestId", RequestId);
            writer.WriteStringValue("startDate", StartDate);
            writer.WriteEnumValue<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.TimeBucket>("timeBucket", TimeBucket);
            writer.WriteStringValue("type", Type);
            writer.WriteStringValue("userId", UserId);
        }
    }
}
#pragma warning restore CS0618
