// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class HistoryItem : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The Action property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Action { get; set; }
#nullable restore
#else
        public string Action { get; set; }
#endif
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The Comment property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Comment { get; set; }
#nullable restore
#else
        public string Comment { get; set; }
#endif
        /// <summary>The CreatedDate property</summary>
        public DateTimeOffset? CreatedDate { get; set; }
        /// <summary>The MoreInfo property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? MoreInfo { get; set; }
#nullable restore
#else
        public string MoreInfo { get; set; }
#endif
        /// <summary>The RelatedDocument property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document? RelatedDocument { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document RelatedDocument { get; set; }
#endif
        /// <summary>The User property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.User? User { get; set; }
#nullable restore
#else
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.User User { get; set; }
#endif
        /// <summary>The UserEmail property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? UserEmail { get; set; }
#nullable restore
#else
        public string UserEmail { get; set; }
#endif
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.HistoryItem"/> and sets the default values.
        /// </summary>
        public HistoryItem()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.HistoryItem"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.HistoryItem CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.HistoryItem();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "Action", n => { Action = n.GetStringValue(); } },
                { "Comment", n => { Comment = n.GetStringValue(); } },
                { "CreatedDate", n => { CreatedDate = n.GetDateTimeOffsetValue(); } },
                { "MoreInfo", n => { MoreInfo = n.GetStringValue(); } },
                { "RelatedDocument", n => { RelatedDocument = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document.CreateFromDiscriminatorValue); } },
                { "User", n => { User = n.GetObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.User>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.User.CreateFromDiscriminatorValue); } },
                { "UserEmail", n => { UserEmail = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("Action", Action);
            writer.WriteStringValue("Comment", Comment);
            writer.WriteDateTimeOffsetValue("CreatedDate", CreatedDate);
            writer.WriteStringValue("MoreInfo", MoreInfo);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document>("RelatedDocument", RelatedDocument);
            writer.WriteObjectValue<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.User>("User", User);
            writer.WriteStringValue("UserEmail", UserEmail);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
