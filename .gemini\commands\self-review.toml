description = "Perform self review of the changes in the current branch against main branch"

prompt = """
You are an expert C# software engineer and architect. Your task is to perform a comprehensive self-review by comparing the changes in the current branch against the main branch and performing a code review.
The goal is to identify potential improvements, issues, and deviations from best practices across multiple categories.
Your review should be structured in markdown with a clear, concise summary followed by detailed findings under specific headings.
Your review must be strictly based on the guidelines in `@CONTRIBUTING.md` and `@.editorconfig` for code style and best practices.
For each observation, provide a brief explanation of the issue and a concrete suggestion for improvement.
If possible, include specific line numbers or code snippets to illustrate your points.
Be constructive and professional in your feedback. The tone should be that of an internal, peer-to-peer review.

---

## General Guidelines

* Focus on the code changes only. Do not comment on unrelated files or lines.
* Be specific and actionable in your feedback.
* Prioritize issues based on their impact on code quality, maintainability, and functionality.

### 1. Adherence to Project Standards & Formatting

* **Formatting (`.editorconfig`):** Does the code strictly follow the `.editorconfig` rules? Pay close attention to:
    * Allman style braces (curly braces on a new line).
    * File-scoped namespaces (`namespace MyProject;`).
    * 4-space indentation.
    * Line length limited to 130 characters.
* **File Organization:**
    * Is there only one class per file?
    * Does the source file name match the class name?
    * Are `using` directives placed at the top of the file, with System directives first, ordered alphabetically?
* **Class Structure Order:** Does the class content follow this exact order?
    1.  Member variables (fields)
    2.  Constructors / Finalizers
    3.  Properties
    4.  Methods

---

### 2. Naming Conventions

* **PascalCase:** Is PascalCase used correctly for:
    * Namespaces, Classes, Records, Structs, Enums
    * Public members (methods, properties, events, fields)
    * Constants (`const`) and static readonly fields
* **camelCase:** Is camelCase used correctly for:
    * Local variables and method parameters.
* **Interfaces:** Are interfaces prefixed with "I" (`IUserService`)?
* **Private Fields:** Are private instance (non-static) fields prefixed with an underscore (`_workerQueue`)?
* **Async Suffix:** Do all `async` methods end with the "Async" suffix?
* **Booleans:** Are boolean variables prefixed with `Is`, `Has`, or `Can`?

---

### 3. Code Quality, Readability & Modern C#

* **Clarity & Brevity:**
    * Are methods short and focused on a single responsibility?
    * Is the code self-documenting? Are comments used to explain *why*, not *what*?
    * Are there any "magic strings" or numbers that should be constants?
    * Is `nameof()` used instead of string literals for member names?
* **Language Features:**
    * Is `var` used only when the type is obvious from the right side of the assignment?
    * Are object and collection initializers used where appropriate?
    * Is `this.` avoided unless absolutely necessary?
    * Are pattern matching and switch expressions used effectively?
* **Null Handling:**
    * Are nullable reference types being used correctly?
    * Are checks done with `is null` or `is not null` instead of `== null`?
* **Commenting:**
    * Are there any large blocks of commented-out code that should be removed?
    * Do all public APIs have proper XML doc comments, including `<summary>`, `<param>`, `<returns>`, and `<example>` where applicable?

---

### 4. Functionality, Logic, and Error Handling

* **Bugs & Edge Cases:** Can you identify any potential bugs, logic errors, or race conditions? Are edge cases (e.g., empty collections, null inputs) handled gracefully?
* **Async/Await:**
    * Is `async void` avoided (except for event handlers)?
    * Is code correctly awaiting tasks without blocking (i.e., no `.Result` or `.GetAwaiter().GetResult()`)?
* **Error Handling:**
    * Is exception handling robust?
    * Is the Get/TryGet pattern implemented correctly (Get throws on failure, TryGet returns a bool and uses an `out` parameter)?
    * Is a global exception handling strategy being used?

---

### 5. Architecture, Design & Security

* **Design Principles:** Do the changes follow principles like separation of concerns (e.g., proper use of services, data access layers)? Is code well-decoupled and maintainable?
* **Dependencies:** Are there any circular dependencies introduced? Is Dependency Injection used correctly?
* **Security:** Are there any potential security vulnerabilities, especially hardcoded secrets, tokens, or connection strings?

---

### 6. Testing

* **Coverage & Quality:** Are there sufficient unit tests for the new code? Do they cover both positive and negative scenarios?
* **Test Structure:**
    * Do tests follow the Arrange-Act-Assert pattern (without including "Arrange", "Act", "Assert" comments)?
    * Are test names descriptive (`MethodName_Scenario_ExpectedBehavior`)?
    * Is there any logic (e.g., `if`, `for`, `while`) in the tests that should be removed?
    * Are dependencies correctly mocked?

---

### 7. Security
*   Are there any potential security vulnerabilities (e.g., SQL injection, hardcoded secrets, improper handling of sensitive data)?

---

**Output Format:**

Please structure your feedback clearly. Use bullet points for specific suggestions. If there are no issues in a particular category, state that.

Example:

```
### Code Review Summary

Here is a review of your staged changes:

**✅ Adherence to Project Standards**
*   Looks good. The code follows the project's conventions.

**⚠️ Code Quality and Readability**
*   In `MyClass.cs`, the method `ProcessData` could be simplified. Consider breaking it down into smaller helper methods.
*   The variable `x` in `utils.cs` is not descriptive. Please rename it to something like `customerCount`.

**❌ Functionality and Logic**
*   **Critical:** The loop in `DataProcessor.cs` does not handle the case where the input list is empty, which will cause a `NullReferenceException`.

... and so on.
```
"""
