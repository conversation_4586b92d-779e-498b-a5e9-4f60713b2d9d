﻿using MediatR;

using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

namespace TaskManagementService.Core.Handlers;

public class GetUserManagedUsersQueryHandler(
    ILogger<GetUserManagedUsersQueryHandler> logger,
    IEnumerable<IUserService> userServices)
    : IRequestHandler<GetUserManagedUsersQuery, List<ManagedUser>>
{
    public async Task<List<ManagedUser>> Handle(GetUserManagedUsersQuery request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));

        var fetchManagedUsers = new List<Task<List<ManagedUser>>>();
        foreach (var userService in userServices)
        {
            fetchManagedUsers.Add(userService.GetManagedUsersAsync(request.AccountId));
        }

        await Task.WhenAll(fetchManagedUsers);
        var users = new List<ManagedUser>();

        foreach (var managedUsers in fetchManagedUsers)
        {
            users.AddRange(await managedUsers);
        }

        logger.LogInformation("Fetched {UserCount} users", users.Count);
        return users;
    }
}
