#!/bin/bash

if [ ! -z "$1" ]; then
    TEST_SERVER_URL=$1
fi
if [ ! -z "$2" ]; then
    TEST_STAGE=$2
fi
if [ ! -z "$3" ]; then
    PUBLIC_URL=$3
fi
if [ ! -z "$4" ]; then
    INTERNAL_URL=$4
fi

TEST_SERVER_URL="${TEST_SERVER_URL:-https://services.local:5001/}"
TEST_STAGE="${TEST_STAGE:-PreDeployment}"

echo "[Integration] ===== $TEST_STAGE ====="
echo "[Integration] Environment vars       : $(printenv)"
echo "[Integration] Current directory      : $(pwd)"
echo "[Integration] Test URL               : $TEST_SERVER_URL"
echo "[Integration] Public URL             : $PUBLIC_URL"
echo "[Integration] Internal URL           : $INTERNAL_URL"

dotnet TaskManagementService.Integration.Tests.dll -trait "Category=${TEST_STAGE}" -json -xml /var/tmp/report.xml
testExitCode=$? # Save the exit code
if (($testExitCode != 0)); then
    echo "[Integration] Tests failed."
else
    echo "[Integration] Tests passed."
fi
if (($testExitCode != 0)); then
    echo -e "\n----- Details -----\n"
    cat /var/tmp/report.xml
    echo -e "\n----- Results -----\n"
fi
curl -sf -XPOST http://127.0.0.1:15020/quitquitquit >/dev/null
echo "[Integration] Istio-proxy exit code  : $?"
echo "[Integration] Test exit code         : $testExitCode"
echo "[Integration] ===== $TEST_STAGE DONE ====="
exit $testExitCode
