﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;

using Mapster;

using Moq;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Exceptions;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Infrastructure.ServiceIntegrations.UnifiedRepositoryApi;
using TaskManagementService.Infrastructure.ServiceIntegrations.UnifiedRepositoryApi.Models;

using Xunit;

namespace TaskManagementService.Tests.ServiceIntegrations.UnifiedRepositoryApi;

[Trait("TestType", "UnitTest")]
public class UnifiedRepositoryApiTest
{
    private readonly Mock<IUnifiedRepositoryApiConfig> _unifiedRepositoryApiConfig = new();
    private readonly Mock<HttpClient> _mockedHttpClient = new();
    private readonly Mock<IRequestContextService> _mockedRequestContextService = new();

    public UnifiedRepositoryApiTest()
    {
        TypeAdapterConfig<JsonNode, JsonNode>
            .NewConfig()
            .MapWith(jsonNode => jsonNode);
        _unifiedRepositoryApiConfig.Setup(x => x.BaseUrl).Returns("https://test.com");
        _mockedRequestContextService.Setup(x => x.ShardId).Returns("shardId");
    }

    [Fact]
    public async Task GetAgreementsAsync()
    {
        var guidSet = new HashSet<Guid>([Guid.NewGuid(), Guid.NewGuid()]);
        var agreementRequest = new GetAgreementsRequest(guidSet, [], [], false, false);
        var agreementGuid = Guid.NewGuid();

        var agreementsApiDto = new AgreementsApiDto()
        {
            Agreements = [
            new AgreementApiDto()
            {
                Id = agreementGuid,
                Model = "model",
                Source = "source",
                Version = "version",
                Type = "type",
                CreatedAt = DateTimeOffset.Now,
                ModifiedAt = DateTimeOffset.Now.AddDays(1),
                Etag = 123,
                Data = new AgreementDataApiDto()
                 {
                     Parties = [],
                     AgreementDocumentData = JsonNode.Parse("{ \"key1\": \"value1\" }"),
                     Name = "test name",
                     SourceId = Guid.NewGuid(),
                     Languages = [],
                     SourceName = "source name",
                     TotalValue = new ValueApiDto()
                     {
                         DoubleValue = 10,
                         CurrencyCode = "USD"
                     },
                     EffectiveDate = DateTimeOffset.Now,
                     AgreementStatus = "Completed",
                     SourceAccountId = Guid.NewGuid(),
                     ExtractionStatus = "Extraction status",
                     DocumentStorageId = agreementGuid,
                     SourceIngestionId = "source ingestion ID",
                     ExtractionReviews = JsonNode.Parse("{ \"key2\": \"value2\" }"),
                     PendingExtractionReviewCount = 2,
                     Id = Guid.NewGuid()
                 }
            }
        ],
            CToken = string.Empty
        };
        var serializedAgreementsApiDto = JsonSerializer.Serialize(agreementsApiDto);
        var httpResponseMessage = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(serializedAgreementsApiDto),
        };
        var responseMessage = Task.FromResult(httpResponseMessage);

        _mockedHttpClient.Setup(x => x.SendAsync(It.IsAny<HttpRequestMessage>(), It.IsAny<CancellationToken>()))
            .Returns(responseMessage);

        var unifiedRepositoryService = new UnifiedRepositoryService(_unifiedRepositoryApiConfig.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object);
        var result = await unifiedRepositoryService.GetAgreementsAsync(agreementRequest);

        Assert.NotNull(result);
        Assert.Equal(123, result[agreementGuid].Etag);
        Assert.Equal("model", result[agreementGuid].Model);
        Assert.Equal(2, result[agreementGuid].Data.PendingExtractionReviewCount);
        Assert.Single(result);
        httpResponseMessage.Dispose();
    }

    [Fact]
    public async Task GetAgreementsAsyncNotFound()
    {
        var guidSet = new HashSet<Guid>([Guid.NewGuid(), Guid.NewGuid()]);
        var agreementRequest = new GetAgreementsRequest(guidSet, [], [], false, false);

        var httpResponseMessage = new HttpResponseMessage(HttpStatusCode.NotFound)
        {
            Content = new StringContent("{}"),
        };
        var responseMessage = Task.FromResult(httpResponseMessage);

        _mockedHttpClient.Setup(x => x.SendAsync(It.IsAny<HttpRequestMessage>(), It.IsAny<CancellationToken>()))
            .Returns(responseMessage);

        var unifiedRepositoryService = new UnifiedRepositoryService(_unifiedRepositoryApiConfig.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object);
        var result = await unifiedRepositoryService.GetAgreementsAsync(agreementRequest);

        Assert.NotNull(result);
        Assert.Empty(result);
        httpResponseMessage.Dispose();
    }

    [Fact]
    public async Task GetAgreementsAsyncException()
    {
        var guidSet = new HashSet<Guid>([Guid.NewGuid(), Guid.NewGuid()]);
        var agreementRequest = new GetAgreementsRequest(guidSet, [], [], false, false);

        var httpResponseMessage = new HttpResponseMessage(HttpStatusCode.BadRequest)
        {
            Content = new StringContent("{}"),
        };
        var responseMessage = Task.FromResult(httpResponseMessage);

        _mockedHttpClient.Setup(x => x.SendAsync(It.IsAny<HttpRequestMessage>(), It.IsAny<CancellationToken>()))
            .Returns(responseMessage);

        var unifiedRepositoryService = new UnifiedRepositoryService(_unifiedRepositoryApiConfig.Object, _mockedHttpClient.Object, _mockedRequestContextService.Object);
        try
        {
            var result = await unifiedRepositoryService.GetAgreementsAsync(agreementRequest);
        }
        catch (TaskManagementServiceException e)
        {
            Assert.NotNull(e);
        }

        httpResponseMessage.Dispose();
    }
}
