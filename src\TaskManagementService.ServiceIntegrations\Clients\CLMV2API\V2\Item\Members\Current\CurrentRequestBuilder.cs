// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using System;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Groups;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Managedusers;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Permissionsets;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Recentdocuments;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Watcheddocuments;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Watcheddocumentsprocesstrackingactivities;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Workflowqueues;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Workitems;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current
{
    /// <summary>
    /// Builds and executes requests for operations under \v2\{accountId}\members\current
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class CurrentRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The groups property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Groups.GroupsRequestBuilder Groups
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Groups.GroupsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The managedusers property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Managedusers.ManagedusersRequestBuilder Managedusers
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Managedusers.ManagedusersRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The permissionsets property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Permissionsets.PermissionsetsRequestBuilder Permissionsets
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Permissionsets.PermissionsetsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The recentdocuments property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Recentdocuments.RecentdocumentsRequestBuilder Recentdocuments
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Recentdocuments.RecentdocumentsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The watcheddocuments property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Watcheddocuments.WatcheddocumentsRequestBuilder Watcheddocuments
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Watcheddocuments.WatcheddocumentsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The watcheddocumentsprocesstrackingactivities property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Watcheddocumentsprocesstrackingactivities.WatcheddocumentsprocesstrackingactivitiesRequestBuilder Watcheddocumentsprocesstrackingactivities
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Watcheddocumentsprocesstrackingactivities.WatcheddocumentsprocesstrackingactivitiesRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The workflowqueues property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Workflowqueues.WorkflowqueuesRequestBuilder Workflowqueues
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Workflowqueues.WorkflowqueuesRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The workitems property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Workitems.WorkitemsRequestBuilder Workitems
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.Workitems.WorkitemsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.CurrentRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public CurrentRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/v2/{accountId}/members/current{?expand*}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.CurrentRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public CurrentRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/v2/{accountId}/members/current{?expand*}", rawUrl)
        {
        }
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member"/></returns>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member?> GetAsync(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.CurrentRequestBuilder.CurrentRequestBuilderGetQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member> GetAsync(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.CurrentRequestBuilder.CurrentRequestBuilderGetQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            var requestInfo = ToGetRequestInformation(requestConfiguration);
            return await RequestAdapter.SendAsync<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member>(requestInfo, global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member.CreateFromDiscriminatorValue, default, cancellationToken).ConfigureAwait(false);
        }
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member?> PatchAsync(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member> PatchAsync(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = ToPatchRequestInformation(body, requestConfiguration);
            return await RequestAdapter.SendAsync<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member>(requestInfo, global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member.CreateFromDiscriminatorValue, default, cancellationToken).ConfigureAwait(false);
        }
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member?> PutAsync(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member> PutAsync(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = ToPutRequestInformation(body, requestConfiguration);
            return await RequestAdapter.SendAsync<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member>(requestInfo, global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member.CreateFromDiscriminatorValue, default, cancellationToken).ConfigureAwait(false);
        }
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.CurrentRequestBuilder.CurrentRequestBuilderGetQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.CurrentRequestBuilder.CurrentRequestBuilderGetQueryParameters>> requestConfiguration = default)
        {
#endif
            var requestInfo = new RequestInformation(Method.GET, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json, application/scim+json");
            return requestInfo;
        }
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToPatchRequestInformation(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToPatchRequestInformation(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = new RequestInformation(Method.PATCH, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json, application/scim+json");
            requestInfo.SetContentFromParsable(RequestAdapter, "application/scim+json", body);
            return requestInfo;
        }
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToPutRequestInformation(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToPutRequestInformation(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Member body, Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = new RequestInformation(Method.PUT, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json, application/scim+json");
            requestInfo.SetContentFromParsable(RequestAdapter, "application/scim+json", body);
            return requestInfo;
        }
        /// <summary>
        /// Returns a request builder with the provided arbitrary URL. Using this method means any other path or query parameters are ignored.
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.CurrentRequestBuilder"/></returns>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.CurrentRequestBuilder WithUrl(string rawUrl)
        {
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.CurrentRequestBuilder(rawUrl, RequestAdapter);
        }
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        #pragma warning disable CS1591
        public partial class CurrentRequestBuilderGetQueryParameters 
        #pragma warning restore CS1591
        {
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            [QueryParameter("expand")]
            public string[]? Expand { get; set; }
#nullable restore
#else
            [QueryParameter("expand")]
            public string[] Expand { get; set; }
#endif
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class CurrentRequestBuilderGetRequestConfiguration : RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Current.CurrentRequestBuilder.CurrentRequestBuilderGetQueryParameters>
        {
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class CurrentRequestBuilderPatchRequestConfiguration : RequestConfiguration<DefaultQueryParameters>
        {
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class CurrentRequestBuilderPutRequestConfiguration : RequestConfiguration<DefaultQueryParameters>
        {
        }
    }
}
#pragma warning restore CS0618
