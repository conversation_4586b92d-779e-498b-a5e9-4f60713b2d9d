# Gemini CLI Instructions - Task Management Service

## Project Overview

This is a . NET 8.0 ASP. NET Core web API service that aggregates tasks from various sources and provides an API to the unified task management interface. The service is part of DocuSign's microservices architecture and uses MSF (Microservices Framework) components.

## Architecture

* **Language**: C# (. NET 8.0)
* **Framework**: ASP. NET Core Web API
* **Architecture**: Clean Architecture with separate layers:
  + `TaskManagementService` - Web API layer (controllers, middleware, configuration)
  + `TaskManagementService.Core` - Core business logic (handlers, models, interfaces)
  + `TaskManagementService.Infrastructure` - Infrastructure layer (external services, repositories)
  + `TaskManagementService.ServiceIntegrations` - Service integration layer
* **Container**: Docker with <PERSON>er Compose for local development
* **Proxy**: Nginx as reverse proxy for SSL termination
* **Testing**: Multiple test projects (Unit, Integration, Performance, Periodic)

## Key Components

### Controllers

* `TasksController.cs` - Main tasks API endpoints
* `UserController.cs` - User-related operations
* `RedisCacheController.cs` - Redis cache testing functionality (available in Development/Integration/Stage only)
* `RateLimitController.cs` - Rate limiting functionality (available in Development/Integration/Stage only)

### Dependencies

* DocuSign MSF components for microservices infrastructure
* Redis for caching and health checks
* OpenTelemetry for observability
* JWT Bearer authentication
* OneConfig for configuration management
* Kiota for API client generation

## Development Workflow

### Prerequisites

* . NET 8.0 SDK (version 8.0.414 as specified in global.json)
* Docker and Docker Compose
* MSF development environment (for True Dev)

### Local Development Commands

#### Build and Restore

```bash
# From repository root
dotnet tool restore
dotnet build

# Using global.json scripts
dotnet r build  # Equivalent to ado:build script
```

#### Run Locally

```bash
# Direct run
cd src/TaskManagementService
dotnet run

# Docker Compose (recommended for full functionality)
docker compose up --build
```

#### Testing

```bash
# Run all tests
dotnet test

# Run specific test projects
dotnet test src/TaskManagementService.Tests/
dotnet test src/TaskManagementService.Integration.Tests/
dotnet test src/TaskManagementService.Perf.Tests/
```

#### Publishing

```bash
cd src/TaskManagementService
dotnet publish -c Release -f net8.0 -r linux-x64 --no-self-contained --verbosity minimal /p:DebugType=embedded
```

### Docker Development

#### Start Services

```bash
docker compose up --build
```

#### View Logs

```bash
# Follow service logs
docker compose logs --follow --timestamps --no-log-prefix task-management-service

# All services
docker compose logs --follow
```

#### Health Check

```bash
# Access health endpoint
curl https://localhost/health
```

#### Cleanup

```bash
docker compose down
```

### Configuration

#### Environment-Specific Configs

* `config/local/` - Local development configuration
* `config/demo/` - Demo environment
* `config/integration/` - Integration testing
* `config/stage/` - Staging environment
* `config/production/` - Production environment

#### App Settings

* `appsettings.json` - Base configuration
* `appsettings.Development.json` - Development overrides
* `appsettings.Demo.json` - Demo environment
* `appsettings.Integration.json` - Integration testing
* `appsettings.Stage.json` - Staging environment

### MSF True Dev Environment

For testing with full MSF functionality:
1. Follow MSF True Dev documentation
2. Touch `src/TaskManagementService/Dockerfile` to force rebuild if changes aren't picked up

### Code Quality

#### Style and Analysis

* Uses StyleCop for code analysis (`build/stylecop.ruleset`,   `build/stylecop.json`)
* Global suppressions in `GlobalSuppressions.cs` files
* Code formatting script: `tools/format-code.sh`

#### Linting Commands

```bash
# Format code
./tools/format-code.sh

# Build with analysis
dotnet build --verbosity normal
```

## File Organization

### Source Code Structure

```
src/
├── TaskManagementService/          # Web API layer
│   ├── Controllers/               # API controllers
│   ├── Config/                    # Configuration
│   ├── Extensions/                # Extension methods
│   ├── Middleware/                # Custom middleware
│   ├── Properties/                # Assembly properties
│   ├── Services/                  # Application services
│   └── Swagger/                   # API documentation
├── TaskManagementService.Core/     # Business logic
│   ├── Authorization/             # Auth logic
│   ├── Config/                    # Core configuration
│   ├── Enums/                     # Enumerations
│   ├── Exceptions/                # Custom exceptions
│   ├── Handlers/                  # Command/Query handlers
│   ├── Interfaces/                # Abstractions
│   ├── Models/                    # Domain models
│   └── Queries/                   # Query definitions
├── TaskManagementService.Infrastructure/ # Infrastructure
│   ├── Factories/                 # Factory patterns
│   ├── Implementations/           # Service implementations
│   ├── Interfaces/                # Infrastructure interfaces
│   ├── Resources/                 # Static resources
│   ├── Serialization/             # Serialization logic
│   ├── ServiceIntegrations/       # External service clients
│   └── Services/                  # Infrastructure services
└── [Test Projects]/               # Various test suites
```

### Configuration Files

* `Directory.Build.props` & `Directory.Build.targets` - MSBuild configuration
* `Directory.Packages.props` - Package version management
* `global.json` - SDK version and build scripts
* `nuget.config` - NuGet package sources
* `docker-compose.yml` - Local development orchestration

## Common Tasks

### Adding New Features

1. Add models in `TaskManagementService.Core/Models/`
2. Define interfaces in `TaskManagementService.Core/Interfaces/`
3. Implement handlers in `TaskManagementService.Core/Handlers/`
4. Add infrastructure in `TaskManagementService.Infrastructure/`
5. Create controllers in `TaskManagementService/Controllers/`
6. Write tests in appropriate test projects

### Adding Dependencies

1. Update `Directory.Packages.props` for version management
2. Add PackageReference in relevant `.csproj` files
3. Register services in `ServiceCollectionExtensions.cs`

### Debugging

1. Use Docker Compose for realistic environment
2. Check logs via `docker compose logs`
3. Use MSF True Dev for integration debugging
4. Health checks available at `/health` endpoint

### Deployment

1. Uses Skaffold for Kubernetes deployment (`skaffold.yaml`)
2. Azure Pipelines for CI/CD (`azure-pipelines.yml`)
3. Environment-specific configurations in `config/` directory

## API Documentation

The service includes Swagger documentation accessible when running locally. The API provides task aggregation functionality with the following main endpoints:
* Tasks management via `TasksController`
* User operations via `UserController`
* Rate limiting controls via `RateLimitController` (Development/Integration/Stage environments only)
* Redis cache testing via `RedisCacheController` (Development/Integration/Stage environments only)

## Notes for AI Assistant

* This is a DocuSign enterprise service following MSF patterns
* Always consider the layered architecture when making changes
* Docker Compose is the preferred local development method
* Pay attention to configuration management via OneConfig
* Consider observability and telemetry when adding features
* Follow existing patterns for dependency injection and service registration
* Respect the existing code style and analysis rules
