﻿using System.Text.Json.Serialization;

namespace TaskManagementService.Core.Exceptions;

/// <summary>
/// Error Details.
/// </summary>
public class ErrorDetails
{
    /// <summary>
    /// Gets or Sets the http status code.
    /// </summary>
    /// <value>The http status code.</value>
    [JsonIgnore]
    public int? HttpStatusCode { get; set; }

    /// <summary>
    /// Gets or Sets the error code.
    /// </summary>
    /// <value>The error code.</value>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string ErrorCode { get; set; } = string.Empty;

    /// <summary>
    /// Gets or Sets the user message.
    /// </summary>
    /// <value>The user message.</value>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? UserMessage { get; set; }

    /// <summary>
    /// Gets or Sets the developer message (exception message).
    /// </summary>
    /// <value>The developer message (exception message).</value>
    [JsonIgnore]
    public string? DeveloperMessage { get; set; }

    /// <summary>
    /// Gets the extensions.
    /// </summary>
    /// <value>The extensions.</value>
    [JsonExtensionData]
    public IDictionary<string, object> Extensions { get; } = new Dictionary<string, object>(StringComparer.Ordinal);
}
