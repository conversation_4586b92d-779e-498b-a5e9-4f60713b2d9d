# restore the Kiota tool
dotnet tool restore

# list the installed dotnet tools
dotnet tool list --local

# TODO: Uncomment the following lines once the reporting team enables the Swagger endpoint.

# Define the URI of the Swagger file
# $swaggerUri = 'https://services.dev.docusign.net/reporting-adhoc-queries-ipr/v1.0/swagger/v1/swagger.json'

# Informative message about downloading the Swagger file
# Write-Host "Downloading Swagger file from the specified URL: $swaggerUri"

# Using Invoke-WebRequest to fetch the file and save it to the Contracts sub-folder
# Invoke-WebRequest -Uri $swaggerUri -OutFile '.\Contracts\reportingapi.json'

# Informative message about the location of the downloaded file
# Write-Host '✅ Swagger file downloaded successfully and saved the file to .\Contracts\reportingapi.json'

# Generate the client using the Kiota tool
Write-Host '⏳ Generating the client using the Kiota tool...'
dotnet kiota generate `
    --language csharp `
    --class-name ReportingApiClient `
    --namespace-name TaskManagementService.ServiceIntegrations.Clients.ReportingApi `
    --serializer Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory `
    --deserializer Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory `
    --structured-mime-types application/json `
    --openapi ./contracts/reportingapi.json `
    --output ./Clients/ReportingApi `
    --clean-output `

Write-Host '✅ Generated the reporting api client using the Kiota tool.'
