using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

using Azure.Identity;

using DocuSign.Adm.Common.AspNet.OAuth;
using DocuSign.Adm.Common.AspNet.OAuth.Authentication;
using DocuSign.Adm.Common.AspNet.OAuth.Authorization;
using DocuSign.Adm.Common.AspNet.OAuth.Claims;
using DocuSign.AspNet.Xfcc;
using DocuSign.Msf.AspNet.HeaderPropagation.Extensions;
using DocuSign.Msf.AspNet.OpenTelemetry;
using DocuSign.Msf.AspNet.VersionEndpoint.Extensions;
using DocuSign.Msf.AspNet.VersionEndpoint.Options;
using DocuSign.OneConfig.Extensions;
using DocuSign.OneConfig.Extensions.DotNet;
using DocuSign.OneConfig.Extensions.Msf;
using DocuSign.OneConfig.Extensions.Msf.ConstraintsProviders;
using DocuSign.Platform.Extensions.Configuration.FileExtensions;

using HealthChecks.UI.Client;

using Mapster;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors.Infrastructure;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;

using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Trace;

using Serilog;
using Serilog.Core;
using Serilog.Events;
using Serilog.Extensions.Logging;

using StackExchange.Redis;

using TaskManagementService;
using TaskManagementService.Config;
using TaskManagementService.Config.ConstraintsProviders;
using TaskManagementService.Configurations;
using TaskManagementService.Optimizely;
using TaskManagementService.Core;
using TaskManagementService.Core.Authorization;
using TaskManagementService.Core.Config;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Extensions;
using TaskManagementService.Infrastructure;
using TaskManagementService.Infrastructure.Factories;
using TaskManagementService.Infrastructure.Interfaces;
using TaskManagementService.Middleware;
using TaskManagementService.Services;
using TaskManagementService.Swagger;

#pragma warning disable EXTEXP0018

const string serviceName = "task-management-service";
const string serviceDisplayName = "Task Management Service";
const string userTokenType = "5";

Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .Enrich.FromLogContext()
    .WriteTo.Console(formatProvider: CultureInfo.InvariantCulture)
    .CreateBootstrapLogger();

try
{
    Log.Information($"Starting {serviceDisplayName}");

    var builder = WebApplication.CreateBuilder(args);

    builder.Configuration
        .AddKeyPerFile(directoryPath: "/etc/task-management-service/secrets", optional: true)
        .AddUserSecrets(Assembly.GetExecutingAssembly(), optional: true);

    // Extract dir location for ConnectionInfo Files
    var connectionInfoPath = Environment.GetEnvironmentVariable("APP_CONFIG_storageconnectioninfo");
    if (connectionInfoPath != null)
    {
        // Use extension methods to load each file into configuration.
        builder.Configuration.AddSubsectionPerFile(connectionInfoPath, optional: false, reloadOnChange: true);
    }

    builder.WebHost.ConfigureKestrel(options => options.AllowAlternateSchemes = true);

    var services = builder.Services;
    var configuration = builder.Configuration;
    var env = builder.Environment;

    using var loggerFactory = new SerilogLoggerFactory();
    var logger = loggerFactory.CreateLogger("Startup");

    services.AddSerilog((serviceProvider, lc) => lc
            .ReadFrom.Configuration(configuration)
            .ReadFrom.Services(serviceProvider)
            .Enrich.FromLogContext()
            .WriteTo.Console(formatProvider: CultureInfo.InvariantCulture));

    services.AddMapster();

    var healthChecksBuilder = services.AddHealthChecks();
    healthChecksBuilder.AddTypeActivatedCheck<OptimizelyHealthCheck>("optimizely");

    services.AddHttpContextAccessor();
    services.AddControllers()
        .ConfigureApplicationPartManager(apm =>
        {
            // Replace the default controller feature provider with our environment-aware one
            var controllerFeatureProviders = apm.FeatureProviders.OfType<ControllerFeatureProvider>().ToList();
            foreach (var provider in controllerFeatureProviders)
            {
                apm.FeatureProviders.Remove(provider);
            }

            apm.FeatureProviders.Add(new EnvironmentControllerFeatureProvider(env));
        })
        .AddJsonOptions(options =>
            options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter()));

    services.AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = serviceDisplayName,
            Version = "v1"
        });
        c.UseAllOfForInheritance();
        c.EnableAnnotations(enableAnnotationsForInheritance: true, enableAnnotationsForPolymorphism: true);
        c.SelectSubTypesUsing(baseType => typeof(Program).Assembly.GetTypes().Where(type => type.IsSubclassOf(baseType)));

        var securityScheme = new OpenApiSecurityScheme
        {
            Type = SecuritySchemeType.Http,
            Scheme = "bearer",
            BearerFormat = "JWT",
            Description = "JWT Authorization header using the Bearer scheme.",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Reference = new OpenApiReference
            {
                Id = JwtBearerDefaults.AuthenticationScheme,
                Type = ReferenceType.SecurityScheme,
            },
        };

        c.OperationFilter<RequiredHeadersOperationFilter>();

        c.AddSecurityDefinition(securityScheme.Reference.Id, securityScheme);
        c.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            { securityScheme, [] },
        });
    });

    var securityConfiguration = configuration.GetSection("Security").Get<AdmSecurityConfiguration>()!;
    services
        .AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        .AddDocuSignAccountServer(securityConfiguration.Authorities)
        .AddXForwardedClientCert();

    services
        .AddDocuSignAuthorization(securityConfiguration);

    services.Configure<AuthorizationOptions>(options =>
    {
        const string requireUserToken = nameof(requireUserToken);
        options.AddPolicy(requireUserToken, b => b.RequireClaim(DocuSignClaimTypes.TokenType, userTokenType));
        options.AddPolicy(AuthPolicy.JwtToken, b => b.Or(requireUserToken));
        options.AddPolicy(nameof(AuthPolicy.ClmRead), b => b.RequireAdmScope(AuthPolicy.ClmRead));
        options.AddPolicy(nameof(AuthPolicy.Signature), b => b.RequireAdmScope(AuthPolicy.Signature));
        options.AddPolicy(nameof(AuthPolicy.ClmWrite), b => b.RequireAdmScope(AuthPolicy.ClmWrite));
        options.AddPolicy(AuthPolicy.SignatureTasks, b => b.Or(nameof(AuthPolicy.Signature)));
    });

    services.AddVersionEndpoint();
    services.Configure<VersionEndpointOptions>(configuration.GetSection(VersionEndpointOptions.Section));
    services.Configure<RedisCacheConfiguration>(configuration.GetSection(nameof(RedisCacheConfiguration)));

    services.AddHeaderPropagation(options =>
    {
        options.ConfigureMicroservices();
    });

    services.AddHttpClient();
    services.AddHttpClient("UserAccount").AddStandardResilienceHandler();

    services.AddLogging(loggingBuilder => loggingBuilder
        .AddOpenTelemetry(options =>
            options.AddConsoleExporter()));

    services.AddComponentsInstrumentation();
    services
        .AddServiceTelemetry(
            serviceName,
            configureTracer: tracerProviderBuilder => tracerProviderBuilder
                .AddAspNetCoreInstrumentation(options =>
                {
                    options.EnrichWithHttpRequest = (activity, request) => AspNetCoreExtensions.EnrichHttpRequest(activity, request);
                    options.EnrichWithHttpResponse = (activity, response) => AspNetCoreExtensions.EnrichHttpResponse(activity, response);
                    options.EnrichWithException = (activity, exception) => AspNetCoreExtensions.EnrichHttpException(activity, exception);
                })
                .AddHttpClientInstrumentation()
                .AddGrpcClientInstrumentation(),
            configureMeter: meterProviderBuilder => meterProviderBuilder
                .AddHttpClientInstrumentation()
                .AddAspNetCoreInstrumentation()
                .AddRuntimeInstrumentation());

    services.Configure<ForwardedHeadersOptions>(configuration.GetSection("ForwardedHeaders"));
    services.Configure<ForwardedHeadersOptions>(options =>
    {
        options.KnownProxies.Clear();
        options.KnownNetworks.Clear();
    });

    services.AddOptions<CorsOptions>().Configure<IWatchedSingletonConfig<IAPIConfig>>((options, apiConfig) =>
    {
        options.AddDefaultPolicy(
            policy =>
            {
                var allowedOrigins = apiConfig.Value.AllowedOrigins;
                if (allowedOrigins is { Count: > 0 })
                {
                    policy.SetIsOriginAllowedToAllowWildcardSubdomains()
                        .WithOrigins(allowedOrigins.ToArray())
                        .AllowAnyHeader()
                        .AllowAnyMethod()
                        .AllowCredentials();
                }
                else
                {
                    policy.AllowAnyOrigin()
                        .AllowAnyMethod()
                        .AllowAnyHeader();
                }
            });
    });
    services.AddCors();

    if (configuration.GetSection("OptionalFeatures").GetValue<bool>("EnableRedis"))
    {
        // use ConnectionInfo if available to set up Redis connection
        var redisCacheConfig = configuration.GetSection(nameof(RedisCacheConfiguration)).Get<RedisCacheConfiguration>();
        if (redisCacheConfig != null)
        {
            logger.LogInformation("Attempting to set up Redis Connection with ConnectionInfo from WLI");
            var redisConnection = redisCacheConfig.ConnectionInfo[string.Empty].Database[0].Endpoint;
            var redisSshPort = redisCacheConfig.ConnectionInfo[string.Empty].Database[0].SslPort;

            var configurationOptions = await ConfigurationOptions
                .Parse($"{redisConnection}:{redisSshPort}")
                .ConfigureForAzureWithTokenCredentialAsync(new WorkloadIdentityCredential());

            // Disable SSL for local development
            if (string.Equals("local", configuration["ENVIRONMENT_NAME"], StringComparison.OrdinalIgnoreCase))
            {
                configurationOptions.Ssl = false;
            }

            IConnectionMultiplexer redisConnectionMultiplexer = await ConnectionMultiplexer.ConnectAsync(configurationOptions);

            healthChecksBuilder.AddRedis(redisConnectionMultiplexer);
            services.AddStackExchangeRedisCache(options =>
            {
                options.ConnectionMultiplexerFactory = () => Task.FromResult(redisConnectionMultiplexer);
            });
        }
        else
        {
            logger.LogInformation("Attempting to set up Redis Connection without WLI");
            var redisConnectionString = configuration.GetConnectionString("RedisConnectionString");
            ArgumentNullException.ThrowIfNull(redisConnectionString, nameof(redisConnectionString));

            healthChecksBuilder.AddRedis(redisConnectionString);
            services.AddStackExchangeRedisCache(options => options.Configuration = redisConnectionString);
        }
    }

    services.AddHybridCache(options =>
    {
        options.MaximumPayloadBytes = 1 << 20; // 1MiB
        options.MaximumKeyLength = 1024;
        options.DefaultEntryOptions = new HybridCacheEntryOptions
        {
            Expiration = TimeSpan.FromMinutes(15d),
            LocalCacheExpiration = TimeSpan.FromMinutes(15d),
        };
    });

    services
        .AddOptimizelyOneConfigIntegration(option =>
        {
            option.UseAccountIdOrUserIdAsUserAttributesForFlagEvaluation = true;
        })
        .WithGlobalProjectDisabled()
        .WithScopedUserContextProvider<UserContextProvider>();

    services.Configure<OneConfigOptimizelyOptions>(configuration.GetSection("OneConfig:Optimizely"));

    // Configure health check options
    services.Configure<HealthCheckSettings>(configuration.GetSection(HealthCheckSettings.SectionName));

    services.AddOneConfig()
        .WithSingletonConstraintsProvider<EnvironmentConstraintsProvider>()
        .WithSingletonConstraintsProvider<AdditionalEnvironmentConstraintsProvider>()
        .WithSingletonConstraintsProvider<DeviceConstraintsProvider>();

    services
        .AddCoreServices(logger)
        .AddInfrastructureServices(logger, configuration);

    services.AddMediatR(serviceConfiguration =>
    {
        var assemblies = AppDomain.CurrentDomain.GetAssemblies().Where(x =>
            x.FullName != null &&
            x.FullName.StartsWith("TaskManagement", StringComparison.OrdinalIgnoreCase))
            .ToArray();
        logger.LogInformation("Registering Mediatr with assemblies: {Assemblies}", assemblies.Select(x => x.FullName));
        serviceConfiguration.RegisterServicesFromAssemblies(assemblies);
    });

    services
        .AddScoped<RequestContextService>()
        .AddScoped<IRequestContextService>(provider => provider.GetRequiredService<RequestContextService>())
        .AddScoped<ESignApiClientFactory>()
        .AddScoped<IEsignApiClient>(provider => provider.GetRequiredService<ESignApiClientFactory>());

    AddConfigs(services);

    var app = builder.Build();

    if (env.IsDevelopment())
    {
        app.UseSerilogRequestLogging();
        app.UseCors();
        app.UseDeveloperExceptionPage();
    }

    var serverBaseUrl = "/task-management-service/v1.0/";
    app.UsePathBase(new PathString(serverBaseUrl));

    app.UseHeaderPropagation();
    app.UseForwardedHeaders();

    if (!env.IsDevelopment())
    {
        app.UseHttpsRedirection();
    }

    app.UseRouting();

    app.UseSwagger(options =>
    {
        options.PreSerializeFilters.Add((swagger, _) =>
        {
            swagger.Servers =
            [
                new OpenApiServer
                {
                    Url = serverBaseUrl,
                },
            ];
        });
    });

    app.UseAuthentication();
    app.UseAuthorization();
    app.UseMiddleware<ExceptionHandlerMiddleware>();
    app.UseMiddleware<RequestContextMiddleware>();

    app.MapHealthChecks("/health", new HealthCheckOptions
    {
        ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
    });
    app.MapVersionEndpoint("/version");

    if (env.IsDevelopment())
    {
        app.MapGet("/", () => Results.Redirect("/health"));
    }

    app.MapControllers();
    app.MapSwagger();

    if (!env.IsProduction())
    {
        app.UseSwaggerUI(c =>
        {
            c.EnableTryItOutByDefault();
            c.SwaggerEndpoint("v1/swagger.json", serviceDisplayName);
        });
    }

    var constraintContextProvider = app.Services.GetRequiredService<ISingletonConstraintContextProvider>();
    var constraints = string.Join(", ", (constraintContextProvider?.ConstraintContext ?? new Dictionary<string, string>()).Select(s => $"{s.Key}={s.Value}"));
    logger.LogInformation("Constraints: {Constraints}", constraints);

    // Fetch and print OneConfigOptimizelyOptions with masked SdkKey values
    var optimizelyOptions = app.Services.GetRequiredService<IOptions<OneConfigOptimizelyOptions>>();
    var optionsValue = optimizelyOptions.Value;

    // Create a safe representation for logging that masks SdkKey values
    var safeOptions = new
    {
        optionsValue.EnableGlobalProject,
        GlobalProjectSdkKey = optionsValue.GlobalProjectSdkKey != null ? $"[{optionsValue.GlobalProjectSdkKey.Length} characters]" : null,
        optionsValue.GlobalProjectDefaultSecretSection,
        Projects = optionsValue.Projects?.ToDictionary(
            p => p.Key,
            p => (object)new { SdkKey = p.Value.SdkKey != null ? $"[{p.Value.SdkKey.Length} characters]" : null }),
        OptimizelyClientFactory = optionsValue.OptimizelyClientFactory?.GetType().Name,
        optionsValue.UseAccountIdOrUserIdAsUserAttributesForFlagEvaluation,
        optionsValue.EnableRingNameAsUserAttributesForFlagEvaluation
    };

    logger.LogInformation("OneConfigOptimizelyOptions: {@OptimizelyOptions}", safeOptions);

    await app.RunAsync();
    return 0;
}
#pragma warning disable CA1031 // Do not catch general exception types
catch (Exception ex)
#pragma warning restore CA1031 // App startup
{
    Log.Fatal(ex, $"{serviceDisplayName} terminated unexpectedly");
    return 1;
}
finally
{
    await Log.CloseAndFlushAsync();
}

static void AddConfigs(IServiceCollection services)
{
    const string configFilePath = "Config/Files/{0}.ini";
    const string defaultConfig = "TaskManagementServiceConfig";

    services.AddSingletonConfigType<IAPIConfig>(BuildConfigFilePath(defaultConfig));
    services.AddPerRequestConfigType<IUnifiedRepositoryApiConfig>(BuildConfigFilePath("UnifiedRepositoryApiConfig"));
    services.AddPerRequestConfigType<IAgreementAttributesConfig>(BuildConfigFilePath("AgreementAttributesConfig"));
    services.AddPerRequestConfigType<IEnvelopeApiConfig>(BuildConfigFilePath("EnvelopeApiConfig"));
    services.AddPerRequestConfigType<IClmApiConfig>(BuildConfigFilePath("ClmApiConfig"));
    services.AddPerRequestConfigType<IAccountServerConfig>(BuildConfigFilePath("AccountServerConfig"));
    services.AddPerRequestConfigType<IAssignmentsApiConfig>(BuildConfigFilePath("AssignmentsApiConfig"));
    return;

    static string BuildConfigFilePath(string fileName) => string.Format(CultureInfo.InvariantCulture, configFilePath, fileName);
}

// Make the implicit Program class public for testing
[ExcludeFromCodeCoverage]
public partial class Program;
