{"descriptionHash": "81C01BA18367DCB204A69415ECBC5218E1A95811BA9AF258FDD450062AE6FE6F7CBA3C0159AB4FC4561387701BBB0F5B96C6CC06188C99109F82FCAC9EF6B70B", "descriptionLocation": "../../contracts/CLMV2API.yml", "lockFileVersion": "1.0.0", "kiotaVersion": "1.26.1", "clientClassName": "CLMV2APIClient", "typeAccessModifier": "Public", "clientNamespaceName": "TaskManagementService.ServiceIntegrations.Clients.CLMV2API", "language": "CSharp", "usesBackingStore": false, "excludeBackwardCompatible": false, "includeAdditionalData": true, "disableSSLValidation": false, "serializers": ["Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory"], "deserializers": ["Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory"], "structuredMimeTypes": ["application/json"], "includePatterns": ["**/workitems/**/", "**/members/**/"], "excludePatterns": ["**/v201411/**"], "disabledValidationRules": []}