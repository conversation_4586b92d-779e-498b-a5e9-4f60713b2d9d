# Define Service Details
service:
  # Mandatory: Name of the microservice
  name: task-management-service
  port: 80
  targetPort: 5000

image:
  name: task-management-service
  tag: latest # This will be overriden during deployment to use the actual latest tag value
  pullPolicy: IfNotPresent

# Configure virtual service
virtualService:
  enabled: true
  spec:
    http: # Configure match and routes here. Everything under this http will be replaced in virtual service http spec.
      - match:
          - uri:
              prefix: "/task-management-service/v1.0/health"
        name: "{{ .Release.Name }}-health"
        rewrite:
          uri: "/health"
      - match:
          - uri:
              prefix: "/task-management-service/v1.0/swagger"
        name: "{{ .Release.Name }}-swagger"
        rewrite:
          uri: "/swagger"
      - match:
          - uri:
              prefix: "/task-management-service/v1.0/version"
        name: "{{ .Release.Name }}-version"
        rewrite:
          uri: "/version"
      - match:
          - uri:
              prefix: "/task-management-service/v1.0/"
        name: "{{ .Release.Name }}"
        retries:
          attempts: 2
          perTryTimeout: 60s
          retryOn: connect-failure,retriable-status-codes,503

# Enable Authorization
authorization:
  allow:
    enabled: true
    name: task-management-service-authz
    rules:
      - from:
          - source:
              principals:
                - cluster.local/ns/istio-system/sa/istio-ingressgateway-service-account
          - source:
              namespaces: ["{{ .Release.Namespace }}"]

# Mount secrets from Azure Key Vault
secretProvider:
  enabled: true
  providers:
    default:
      name: key-vault-secrets
      enabled: true
      objectAliasOverride:
        task-management--task-management-service--accountserver-tokenexchangeclient-secret: AccountServer__TokenExchangeClientSecret
      mount:
        enabled: true
        mountPath: /etc/task-management-service/secrets
      objects: |
        array:
          - |
            objectName: task-management--tms-dc-redis-cache--CONNECTION-STRING
            objectAlias: ConnectionStrings__RedisConnectionString
            objectType: secret
            objectVersion: ""
          - |
            objectName: task-management--task-management-service--accountserver-tokenexchangeclient-secret
            objectAlias: AccountServer__TokenExchangeClientSecret
          - |
            objectName: task-management--Optimizely--SDK-KEY
            objectAlias: ONECONFIG__OPTIMIZELY__PROJECTS__TaskManagement__SDKKEY
            objectType: secret
            objectVersion: ""

# Define Service Account
serviceaccount:
  enabled: true

grpc:
  enabled: false

# Define public API endpoint. Change to internalApi for needed internal gRPC.
#
# For local development, public API endpoint is https://services.local/{service}/v1.0/health or http://services.local/{service}/v1.0/Health
# private API endpoint is https://internal-services.local:444/{service}/v1.0/health or http://internal-services.local:81/{service}/v1.0/health
#
# Note: When you change this, please also update authorization.allow.rules.from.source.principals, otherwise you will get "RBAC: access denied" error
# For public API:   cluster.local/ns/istio-system/sa/istio-ingressgateway-service-account
# For internal API: cluster.local/ns/istio-system/sa/istio-ingressgateway-internal-service-account
# ⚠️ publicApi and internalApi are mutually exclusive ‼️
publicApi:
  enabled: true

internalApi:
  enabled: false

analysisTemplate:
  spec:
    postDeploymentAnalysis:
      activeDeadlineSeconds: 300
      containers:
        - name: task-management-service-performance-tests
          image: task-management-service-performance-tests
          command: ["/home/<USER>/app/run_perf_tests.sh"]
          args: ["http://$(RELEASE_NAME)-canary:80", "PostDeployment"]

# periodicTests run on a schedule as part of the primary deploy and not alongside experiments or other testing
periodicTests:
  enabled: true
  schedule: "*/2 * * * *"
  analysisTemplate:
    activeDeadlineSeconds: 300
    metadata:
      annotations:
        proxy.istio.io/config: '{ "holdApplicationUntilProxyStarts": true }'
    containers:
      - name: task-management-service-periodic-tests
        image: task-management-service-periodic-tests
        imagePullPolicy: IfNotPresent
        command: ["/app/run-periodic-tests.sh"]
        args:
          - $(TEST_SERVER_URL)
          - $(TEST_STAGE)
          - $(ISTIO_WAF_ENABLED)
        env:
          - name: TEST_SERVER_URL
            value: "http://{{ .Release.Name }}"
          - name: TEST_STAGE
            value: "Periodic"
          - name: ISTIO_WAF_ENABLED
            value: "false"

# Send telemetry directly to Grafana (new metrics pipeline)
openTelemetry:
  enabled: true

resources:
  cpu: 4
  memory: 2Gi

replicaCount: 1

# Argo rollout to Integration and other envs (Stage, Demo) strategy
rollout:
  enabled: true
  progressDeadlineSeconds: 900
  revisionHistoryLimit: 2
  successfulRunHistoryLimit: 2
  unsuccessfulRunHistoryLimit: 2
  canary:
    strategy:
      steps:
        - setCanaryScale:
            replicas: 1
        - setWeight: 0
        - analysis:
            templates:
              - templateName: pre-deployment
        - setCanaryScale:
            matchTrafficWeight: true
        - setWeight: 50
        - analysis:
            templates:
              - templateName: inflight-deployment
        - pause: { duration: 60s }
        - setWeight: 100
        - analysis:
            templates:
              - templateName: post-deployment
  analysisTemplate:
    spec:
      preDeploymentAnalysis:
        backoffLimit: 2
        activeDeadlineSeconds: 300
        metadata:
          annotations:
            proxy.istio.io/config: '{ "holdApplicationUntilProxyStarts": true }'
        containers:
          - &analysisTemplateRunIntegrationTests
            name: task-management-service-integration-tests
            image: task-management-service-integration-tests
            imagePullPolicy: IfNotPresent
            command: ["/bin/bash"]
            args: ["/app/run-analysis.sh"]
            env:
              # This is the experiment endpoint.
              - name: TEST_SERVER_URL
                value: "http://{{ .Release.Name }}-canary:80"
              - name: TEST_STAGE
                value: "PreDeployment"
              # This is the official endpoint. 'subDomain' is used for stage and demo only.
              - name: PUBLIC_URL
                value: "https://{{ if .Values.ingress.subDomain }}{{ .Values.ingress.subDomain }}.{{ end }}{{ .Values.publicApi.name }}.{{ .Values.ingress.domain }}/"
      inflightDeploymentAnalysis:
        activeDeadlineSeconds: 300
        containers:
          - <<: *analysisTemplateRunIntegrationTests
            env:
              - name: TEST_SERVER_URL
                value: "http://{{ .Release.Name }}-canary:80"
              - name: TEST_STAGE
                value: "InflightDeployment"
              - name: PUBLIC_URL
                value: "https://{{ if .Values.ingress.subDomain }}{{ .Values.ingress.subDomain }}.{{ end }}{{ .Values.publicApi.name }}.{{ .Values.ingress.domain }}/"
      postDeploymentAnalysis:
        activeDeadlineSeconds: 300
        containers:
          - <<: *analysisTemplateRunIntegrationTests
            env:
              - name: TEST_SERVER_URL
                value: "http://{{ .Release.Name }}-canary:80"
              - name: TEST_STAGE
                value: "PostDeployment"
              - name: PUBLIC_URL
                value: "https://{{ if .Values.ingress.subDomain }}{{ .Values.ingress.subDomain }}.{{ end }}{{ .Values.publicApi.name }}.{{ .Values.ingress.domain }}/"

experiment:
  mergeVirtualServices:
    enabled: true
    autoDetectExperiments:
      enabled: true

rateLimiting:
  local:
    enabled: true
    config:
      filterEnabledNumerator: 100 # Enable the rate limit on 100% of requests
      filterEnforcedNumerator: 100 # Enforce the rate limit on 100% of requests
    default: # Defines the default limit for all endpoints in service
      maxTokens: 25
      tokensPerFill: 25
      fillInterval: 1s
    routes:
    - path: /*/v1.0/health*
      maxTokens: 100
      tokensPerFill: 100
      fillInterval: 1s
    - path: /*/v1.0/ratelimit*
      maxTokens: 1
      tokensPerFill: 1
      fillInterval: 1s
