﻿using System;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

using Flurl;

using Microsoft.Extensions.Logging;

using Moq;
using Moq.Protected;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Infrastructure.ServiceIntegrations.EnvelopeApi;
using TaskManagementService.Infrastructure.ServiceIntegrations.EnvelopeApi.Models;

using Xunit;
#pragma warning disable CA2000

namespace TaskManagementService.Tests.Services;

[Trait("TestType", "UnitTest")]
public sealed class EnvelopeApiServiceTests : IDisposable
{
    private readonly Mock<IRequestContextService> _requestContextServiceMock;
    private readonly Mock<HttpMessageHandler> _httpMessageHandlerMock;
    private readonly HttpClient _httpClient;
    private readonly EnvelopeApiService _service;

    public EnvelopeApiServiceTests()
    {
        var loggerMock = new Mock<ILogger<EnvelopeApiService>>();
        _requestContextServiceMock = new Mock<IRequestContextService>();
        _httpMessageHandlerMock = new Mock<HttpMessageHandler>();
        _httpClient = new HttpClient(_httpMessageHandlerMock.Object);
        _service = new EnvelopeApiService(
            loggerMock.Object,
            _httpClient,
            _requestContextServiceMock.Object);
    }

    [Fact]
    public async Task GetTasksCountAsyncReturnsTasksCountWhenApiResponseIsSuccessful()
    {
        // Arrange
        var apiResponse = new ESignTaskReportApiDto { AwaitingMySignatureCount = 5 };
        var responseContent = JsonSerializer.Serialize(apiResponse);

        _requestContextServiceMock.Setup(m => m.ESignApiBaseUrl).Returns(new Uri("https://docusign.com"));
        _requestContextServiceMock.SetupGet(r => r.AccountId).Returns(Guid.NewGuid());
        _requestContextServiceMock.SetupGet(r => r.UserId).Returns(Guid.NewGuid());
        _requestContextServiceMock.SetupGet(r => r.Authorization).Returns("Bearer token");

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent)
            });

        // Act
        var result = await _service.GetTasksCountAsync();

        // Assert
        Assert.Single(result);
        Assert.Equal(TaskSource.ESignature, result[0].Source);
        Assert.Equal(5, result[0].Count);
    }

    [Fact]
    public async Task GetTasksCountAsyncReturnsZeroTasksCountWhenApiResponseIsUnsuccessful()
    {
        // Arrange
        _requestContextServiceMock.Setup(m => m.ESignApiBaseUrl).Returns(new Uri("https://docusign.com"));
        _requestContextServiceMock.SetupGet(r => r.AccountId).Returns(Guid.NewGuid());
        _requestContextServiceMock.SetupGet(r => r.UserId).Returns(Guid.NewGuid());
        _requestContextServiceMock.SetupGet(r => r.Authorization).Returns("Bearer token");

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest
            });

        // Act
        var result = await _service.GetTasksCountAsync();

        // Assert
        Assert.Single(result);
        Assert.Equal(TaskSource.ESignature, result[0].Source);
        Assert.Equal(0, result[0].Count);
    }

    [Fact]
    public async Task GetTasksCountAsyncCheckUrl()
    {
        // Arrange
        var apiResponse = new ESignTaskReportApiDto { AwaitingMySignatureCount = 5 };
        var responseContent = JsonSerializer.Serialize(apiResponse);
        var requestUri = new Uri("https://api.example.com/");

        _requestContextServiceMock.SetupGet(r => r.AccountId).Returns(Guid.NewGuid());
        _requestContextServiceMock.SetupGet(r => r.UserId).Returns(Guid.NewGuid());
        _requestContextServiceMock.SetupGet(r => r.Authorization).Returns("Bearer token");
        _requestContextServiceMock.SetupGet(r => r.ESignApiBaseUrl).Returns(requestUri);

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.Is<HttpRequestMessage>(m => m.RequestUri.ToString().Contains(requestUri.ToString())),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseContent)
            });

        // Act
        var result = await _service.GetTasksCountAsync();

        // Assert
        Assert.Single(result);
        Assert.Equal(TaskSource.ESignature, result[0].Source);
        Assert.Equal(5, result[0].Count);
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}
