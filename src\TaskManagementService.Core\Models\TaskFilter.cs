﻿using System.Globalization;

using TaskManagementService.Core.Enums;

namespace TaskManagementService.Core.Models;

public class TaskFilter
{
    public int Offset { get; set; }
    public int Limit { get; set; }
    public string SearchText { get; set; } = string.Empty;
    public IReadOnlyList<Guid> AssignedUserIds { get; set; } = [];
    public IReadOnlyList<string> GroupAssignees { get; set; } = [];
    public IReadOnlyList<AgreementFilter> AgreementFilters { get; set; } = [];
    public bool OnlyUnassigned { get; set; }
    public IReadOnlyList<TaskType> TaskTypes { get; set; } = [];
    public DateTimeOffset? DueDateFrom { get; set; }
    public DateTimeOffset? DueDateTo { get; set; }
    public DateTimeOffset? AssignedDateFrom { get; set; }
    public DateTimeOffset? AssignedDateTo { get; set; }
    public TaskSort? TaskSort { get; set; }

    public bool ExtraClmTaskFilter(UserTask t)
    {
        return IsMatchedTaskTypes(t) && IsMatchedDueDate(t) && IsMatchedAssignedDate(t);
    }

    public bool ExtraESignTaskFilter(UserTask t)
    {
        return IsMatchedAssignedUser(t) && IsMatchedTaskTypes(t) && IsMatchedDueDate(t) && IsMatchedAssignedDate(t);
    }

    public bool ExtraAssignmentTaskFilter(UserTask t)
    {
        return IsMatchedDueDate(t) && IsMatchedAssignedDate(t);
    }

    public bool IsMatchedTaskTypes(UserTask task)
    {
        _ = task ?? throw new ArgumentNullException(nameof(task));
        if (!TaskTypes.Any())
        {
            return true;
        }

        if (!task.Type.HasValue)
        {
            return false;
        }

        return TaskTypes.Any(t => t == task.Type);
    }

    public bool IsMatchedDueDate(UserTask task)
    {
        _ = task ?? throw new ArgumentNullException(nameof(task));

        if (!DueDateFrom.HasValue && !DueDateTo.HasValue)
        {
            return true;
        }

        if (string.IsNullOrEmpty(task.DueDate))
        {
            return false;
        }

        var dueDate = DateTimeOffset.Parse(task.DueDate, CultureInfo.InvariantCulture);
        return (!DueDateFrom.HasValue || dueDate > DueDateFrom.Value) && (!DueDateTo.HasValue || dueDate < DueDateTo.Value);
    }

    public bool IsMatchedAssignedDate(UserTask task)
    {
        _ = task ?? throw new ArgumentNullException(nameof(task));

        if (!AssignedDateFrom.HasValue && !AssignedDateTo.HasValue)
        {
            return true;
        }

        if (string.IsNullOrEmpty(task.AssignedDate))
        {
            return false;
        }

        var assignedDate = DateTimeOffset.Parse(task.AssignedDate, CultureInfo.InvariantCulture);
        return (!AssignedDateFrom.HasValue || assignedDate > AssignedDateFrom.Value) && (!AssignedDateTo.HasValue || assignedDate < AssignedDateTo.Value);
    }

    public bool IsMatchedAssignedUser(UserTask task)
    {
        _ = task ?? throw new ArgumentNullException(nameof(task));
        if (OnlyUnassigned)
        {
            return task.Assignees == null || !task.Assignees.Any();
        }

        return task.Assignees != null && task.Assignees.Any(IsValidAssignee) && IsValidGroupAssignee(task.TaskGroupAssignee);
    }

    private bool IsValidGroupAssignee(TaskGroup? taskGroupAssignee)
    {
        if (GroupAssignees.Count == 0)
        {
            return true;
        }

        if (GroupAssignees.Count > 0 && taskGroupAssignee != null)
        {
            return GroupAssignees.Contains(taskGroupAssignee.Id);
        }

        return false;
    }

    private bool IsValidAssignee(TaskCollaborator assignee)
    {
        if (AssignedUserIds.Count == 0)
        {
            return true;
        }

        if (AssignedUserIds.Any())
        {
            if (assignee is TaskUser userAssignee && userAssignee.UserId != Guid.Empty)
            {
                return AssignedUserIds.Contains(userAssignee.UserId);
            }
            else if (assignee is TaskSigningGroup signingGroupAssignee && !string.IsNullOrWhiteSpace(signingGroupAssignee.Id))
            {
                var userIdStringList = AssignedUserIds.Select(id => id.ToString()).ToList();
                return signingGroupAssignee.SigningGroupUsers.Any(user => userIdStringList.Contains(user.UserId));
            }
        }

        return false;
    }
}
