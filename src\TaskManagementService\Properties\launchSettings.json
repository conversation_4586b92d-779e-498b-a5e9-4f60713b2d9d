{"profiles": {"Widget": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "http://127.0.0.1:5000", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Widget (Local CLM)": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "http://127.0.0.1:5000", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "CLM_ENVIRONMENT": "local"}}, "Console": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "https://localhost:5001;http://localhost:5000", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}