﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>TaskManagementService.Tests</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DocuSign.AccountServer.TokenExchangeClient" />
    <PackageReference Include="DocuSign.eSign.dll" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Microsoft.AspNetCore.HeaderPropagation" />
    <PackageReference Include="Microsoft.Extensions.Http.Resilience" />
    <PackageReference Include="Polly" />
    <PackageReference Include="Polly.Extensions.Http" />
    <PackageReference Include="Polly.Contrib.WaitAndRetry" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TaskManagementService.Core\TaskManagementService.Core.csproj" />
    <ProjectReference Include="..\TaskManagementService.ServiceIntegrations\TaskManagementService.ServiceIntegrations.csproj" />
  </ItemGroup>

</Project>
