ARG REPO=docker.docusignhq.com
FROM $REPO/alpine/jmeter as common-build-stage

RUN apk update && \
  apk upgrade && \
  apk add --no-cache curl

#Create user
ARG user=perfuser
RUN adduser -D ${user}
USER ${user}

# Create user app directory
RUN mkdir -p /home/<USER>/app
WORKDIR /home/<USER>/app

# Bundle app source code
COPY --chown=${user} . .
RUN chmod +x ./run_perf_tests.sh

# Update security patches for jmeter
USER root
RUN cd $(dirname $(which jmeter))/../lib && rm neo4j-java-driver-*.jar batik-bridge-*.jar batik-script-*.jar batik-transcoder-*.jar lets-plot-batik-*.jar
RUN mv patches/* $(dirname $(which jmeter))/../lib

USER ${user}
ENTRYPOINT ["./run_perf_tests.sh"]
