// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
using TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Accounts;
using TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.AgreementReports;
using TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query;
using TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Reports;
using TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.UnifiedRepositoryReports;
using TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.VersionNamespace;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10
{
    /// <summary>
    /// Builds and executes requests for operations under \reporting-adhoc-queries-ipr\v1.0
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class V10RequestBuilder : BaseRequestBuilder
    {
        /// <summary>The accounts property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Accounts.AccountsRequestBuilder Accounts
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Accounts.AccountsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The agreementReports property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.AgreementReports.AgreementReportsRequestBuilder AgreementReports
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.AgreementReports.AgreementReportsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The query property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.QueryRequestBuilder Query
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Query.QueryRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The reports property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Reports.ReportsRequestBuilder Reports
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.Reports.ReportsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The unifiedRepositoryReports property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.UnifiedRepositoryReports.UnifiedRepositoryReportsRequestBuilder UnifiedRepositoryReports
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.UnifiedRepositoryReports.UnifiedRepositoryReportsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The version property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.VersionNamespace.VersionRequestBuilder Version
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.VersionNamespace.VersionRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.V10RequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public V10RequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/reporting-adhoc-queries-ipr/v1.0", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V10.V10RequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public V10RequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/reporting-adhoc-queries-ipr/v1.0", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
