﻿using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;

using Moq;
using Moq.Protected;

namespace TaskManagementService.Tests;

public class CustomWebApplicationFactory<T> : WebApplicationFactory<T>
    where T : class
{
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureTestServices(ConfigureServices);
    }

    private void ConfigureServices(IServiceCollection services)
    {
        var mockHttpMessageHandler = new Mock<HttpMessageHandler>();

        using var httpResponse = new HttpResponseMessage();

        mockHttpMessageHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse)
            .Verifiable();

        services.RemoveServices(typeof(IHttpClientFactory));

        var mockHttpClientFactory = new Mock<IHttpClientFactory>();
#pragma warning disable CA2000 // Dispose objects before losing scope
        var httpClient = new HttpClient(mockHttpMessageHandler.Object);
#pragma warning restore CA2000 // Dispose objects before losing scope
        mockHttpClientFactory.Setup(_ => _.CreateClient(It.IsAny<string>())).Returns(httpClient);

        services.AddSingleton(x => mockHttpClientFactory.Object);
    }
}
