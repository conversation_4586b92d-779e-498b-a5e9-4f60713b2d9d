// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
using TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V3.Accounts.Item.AgreementReports;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V3.Accounts.Item
{
    /// <summary>
    /// Builds and executes requests for operations under \reporting-adhoc-queries-ipr\v3\accounts\{accountId}
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class WithAccountItemRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The agreementReports property</summary>
        public global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V3.Accounts.Item.AgreementReports.AgreementReportsRequestBuilder AgreementReports
        {
            get => new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V3.Accounts.Item.AgreementReports.AgreementReportsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V3.Accounts.Item.WithAccountItemRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public WithAccountItemRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/reporting-adhoc-queries-ipr/v3/accounts/{accountId}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.ReportingAdhocQueriesIpr.V3.Accounts.Item.WithAccountItemRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public WithAccountItemRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/reporting-adhoc-queries-ipr/v3/accounts/{accountId}", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
