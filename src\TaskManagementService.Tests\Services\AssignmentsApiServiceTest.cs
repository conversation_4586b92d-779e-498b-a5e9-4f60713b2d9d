using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

using Moq;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Infrastructure.Services;
using TaskManagementService.Infrastructure.ServiceIntegrations.AccountServer;

using Xunit;

namespace TaskManagementService.Tests.Services;

[Trait("TestType", "UnitTest")]
public sealed class AssignmentsApiServiceTest : IDisposable
{
    private readonly Mock<IAssignmentsApiConfig> _mockAssignmentsApiConfig;
    private readonly Mock<IAccountServerTokenExchangeClient> _mockAccountServerTokenExchangeClient;
    private readonly Mock<IRequestContextService> _mockRequestContextService;
    private readonly HttpClient _httpClient;
    private readonly AssignmentsApiService _assignmentsApiService;

    public AssignmentsApiServiceTest()
    {
        _mockAssignmentsApiConfig = new Mock<IAssignmentsApiConfig>();
        _mockAccountServerTokenExchangeClient = new Mock<IAccountServerTokenExchangeClient>();
        _mockRequestContextService = new Mock<IRequestContextService>();
        _httpClient = new HttpClient();

        // Default setup
        _mockAssignmentsApiConfig.Setup(x => x.Enabled).Returns(true);
        _mockRequestContextService.Setup(x => x.ClmApiBaseUrl).Returns(new Uri("https://test-assignments-api.com"));
        _mockRequestContextService.Setup(x => x.UserId).Returns(Guid.NewGuid());

        _assignmentsApiService = new AssignmentsApiService(
            _mockAssignmentsApiConfig.Object,
            _httpClient,
            _mockAccountServerTokenExchangeClient.Object,
            _mockRequestContextService.Object);
    }

    [Fact]
    public async Task GetTasksAsyncReturnsEmptyListWhenAssignmentsApiIsDisabled()
    {
        // Arrange
        var accountId = Guid.NewGuid();
        var taskFilter = new TaskFilter();
        _mockAssignmentsApiConfig.Setup(x => x.Enabled).Returns(false);

        // Act
        var result = await _assignmentsApiService.GetTasksAsync(accountId, taskFilter);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public void UserTaskConversionMapsAssignmentResponseCorrectly()
    {
        // This test verifies the mapping logic from AssignmentResponse to UserTask
        // by testing the conversion that happens in the GetTasksAsync method

        // Arrange
        var assignmentId = Guid.NewGuid();
        var contextId = Guid.NewGuid();
        var subContextId = Guid.NewGuid();
        var createdDate = DateTimeOffset.UtcNow.AddDays(-2);
        var expirationDate = DateTimeOffset.UtcNow.AddDays(7);
        var description = "Test Assignment Description";

        // Act - Simulate the UserTask creation logic from the service
        var userTask = new UserTask
        {
            Id = assignmentId,
            Source = TaskSource.AssignmentsApi,
            Description = description,
            CreatedDate = createdDate.ToString("o"),
            DueDate = expirationDate.ToString("o"),
            AssignedDate = createdDate.ToString("o"),
            NavigationUrl = $"/atlas/Documents/View?Id={contextId}&SubContextId={subContextId}"
        };

        // Assert
        Assert.Equal(assignmentId, userTask.Id);
        Assert.Equal(TaskSource.AssignmentsApi, userTask.Source);
        Assert.Equal(description, userTask.Description);
        Assert.Equal(createdDate.ToString("o"), userTask.CreatedDate);
        Assert.Equal(expirationDate.ToString("o"), userTask.DueDate);
        Assert.Equal(createdDate.ToString("o"), userTask.AssignedDate);
        Assert.Equal($"/atlas/Documents/View?Id={contextId}&SubContextId={subContextId}", userTask.NavigationUrl);
    }

    [Fact]
    public async Task GetTasksAsyncReturnsEmptyListWhenApiReturnsEmptyResponse()
    {
        // Arrange
        var accountId = Guid.NewGuid();
        var taskFilter = new TaskFilter();

        // Act
        var exception = await Record.ExceptionAsync(() =>
           _assignmentsApiService.GetTasksAsync(accountId, taskFilter));

        // Assert
        Assert.NotNull(exception);
        Assert.IsType<InvalidOperationException>(exception);
        Assert.Contains("Failed to get application token", exception.Message, StringComparison.Ordinal);
    }

    [Fact]
    public async Task GetTasksAsyncHandlesTokenAcquisitionFailure()
    {
        // Arrange
        var accountId = Guid.NewGuid();
        var taskFilter = new TaskFilter();

        // Setup the account server token exchange client to throw an exception
        _mockAccountServerTokenExchangeClient
            .Setup(x => x.GetAppTokenAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Token acquisition failed"));

        // Act & Assert
        var exception = await Record.ExceptionAsync(() =>
            _assignmentsApiService.GetTasksAsync(accountId, taskFilter));

        Assert.IsType<InvalidOperationException>(exception);
        Assert.Contains("Token acquisition failed", exception.Message, StringComparison.Ordinal);
    }

    [Fact]
    public async Task GetTasksGroupsAsyncReturnsEmptyList()
    {
        // Arrange
        var accountId = Guid.NewGuid();
        var includeMembers = false;

        // Act
        var result = await _assignmentsApiService.GetTasksGroupsAsync(accountId, includeMembers);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}
