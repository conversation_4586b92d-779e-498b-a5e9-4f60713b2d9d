version: '3.7'

services:
  ##############################################
  ## Ingress
  ##############################################
  ingress:
    image: nginx:alpine
    container_name: task-management-service-ingress
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./config/local/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl/server.crt:/etc/nginx/server.crt
      - ./ssl/server.key:/etc/nginx/server.key
    restart: 'unless-stopped'
    networks:
      - backend
  ##############################################
  ## Task Management Service
  ##############################################
  task-management-service:
    build:
      context: ./src/TaskManagementService/
      dockerfile: Dockerfile
    container_name: task-management-service
    restart: 'unless-stopped'
    volumes:
      - ./config/local/hostdevice:/var/lib/DocuSign/Flags/hostdevice
    networks:
      - backend
    healthcheck:
      test: [
          'CMD',
          'curl',
          '-f',
          #"--http2-prior-knowledge", # Uncomment for HTTP/2, e.g. gRPC
          'http://task-management-service:5000/health',
        ]
      interval: 60s
      timeout: 3s
      start_period: 10s
      retries: 10
    depends_on:
      redis:
        condition: service_healthy
      seq:
        condition: service_healthy
    environment:
      ASPNETCORE_URLS: http://+:5000
      ASPNETCORE_ENVIRONMENT: Development
      OptionalFeatures__EnableRedis: true
      ConnectionStrings__RedisConnectionString: task-management-redis:6379,abortConnect=false,ssl=false
  ##############################################
  ## Redis
  ##############################################
  redis:
    image: redis:alpine
    container_name: task-management-redis
    hostname: task-management-redis
    restart: 'unless-stopped'
    ports:
      - '6379:6379'
    networks:
      - backend
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 60s
      timeout: 10s
      retries: 10
  ##############################################
  ## Seq
  ##############################################
  seq:
    image: datalust/seq:latest
    container_name: task-management-seq
    hostname: task-management-seq
    ports:
      - '5341:5341'
      - '5342:80'
    restart: 'unless-stopped'
    networks:
      - backend
    environment:
      - ACCEPT_EULA=Y
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://task-management-seq:5341']
      interval: 60s
      timeout: 10s
      retries: 10
##############################################
## Network
##############################################
networks:
  backend:
    driver: bridge
