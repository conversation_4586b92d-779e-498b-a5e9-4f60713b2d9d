using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace TaskManagementService.Controllers;

[ApiController]
[Route("ratelimit")]
public class RateLimitController(ILogger<RateLimitController> logger) : ControllerBase
{
    private readonly ILogger<RateLimitController> _logger = logger;

    [HttpGet("check")]
    public async Task<IActionResult> CheckRateLimitAsync()
    {
        await Task.Delay(10);
        _logger.LogInformation("Rate limit check endpoint called.");
        return Ok(new { message = "Rate limit check successful." });
    }
}
