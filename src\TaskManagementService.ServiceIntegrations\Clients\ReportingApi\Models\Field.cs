// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class Field : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The conditionals property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Conditional>? Conditionals { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Conditional> Conditionals { get; set; }
#endif
        /// <summary>The gate property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Gate { get; set; }
#nullable restore
#else
        public string Gate { get; set; }
#endif
        /// <summary>The key property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Key { get; set; }
#nullable restore
#else
        public string Key { get; set; }
#endif
        /// <summary>The label property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Label { get; set; }
#nullable restore
#else
        public string Label { get; set; }
#endif
        /// <summary>The multiSelect property</summary>
        public bool? MultiSelect { get; set; }
        /// <summary>The path property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Path { get; set; }
#nullable restore
#else
        public string Path { get; set; }
#endif
        /// <summary>The type property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Type { get; set; }
#nullable restore
#else
        public string Type { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Field"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Field CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Field();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "conditionals", n => { Conditionals = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Conditional>(global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Conditional.CreateFromDiscriminatorValue)?.AsList(); } },
                { "gate", n => { Gate = n.GetStringValue(); } },
                { "key", n => { Key = n.GetStringValue(); } },
                { "label", n => { Label = n.GetStringValue(); } },
                { "multiSelect", n => { MultiSelect = n.GetBoolValue(); } },
                { "path", n => { Path = n.GetStringValue(); } },
                { "type", n => { Type = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.ReportingApi.Models.Conditional>("conditionals", Conditionals);
            writer.WriteStringValue("gate", Gate);
            writer.WriteStringValue("key", Key);
            writer.WriteStringValue("label", Label);
            writer.WriteBoolValue("multiSelect", MultiSelect);
            writer.WriteStringValue("path", Path);
            writer.WriteStringValue("type", Type);
        }
    }
}
#pragma warning restore CS0618
