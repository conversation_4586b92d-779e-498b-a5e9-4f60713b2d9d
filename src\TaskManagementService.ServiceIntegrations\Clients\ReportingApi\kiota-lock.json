{"descriptionHash": "82B164A6BF3F581E54E9A5D54110B89286BF0E53A6C7B6BD40D964CC0AD3173623F63EE4A22157B29957861C236019C8B8BFF2FC70B41021CC6EB8C11A549B03", "descriptionLocation": "../../contracts/reportingapi.json", "lockFileVersion": "1.0.0", "kiotaVersion": "1.20.0", "clientClassName": "ReportingApiClient", "typeAccessModifier": "Public", "clientNamespaceName": "TaskManagementService.ServiceIntegrations.Clients.ReportingApi", "language": "CSharp", "usesBackingStore": false, "excludeBackwardCompatible": false, "includeAdditionalData": true, "disableSSLValidation": false, "serializers": ["Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory"], "deserializers": ["Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory"], "structuredMimeTypes": ["application/json"], "includePatterns": [], "excludePatterns": [], "disabledValidationRules": []}