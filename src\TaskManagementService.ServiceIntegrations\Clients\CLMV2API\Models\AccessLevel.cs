// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class AccessLevel : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The Create property</summary>
        public bool? Create { get; set; }
        /// <summary>The Move property</summary>
        public bool? Move { get; set; }
        /// <summary>The Read property</summary>
        public bool? Read { get; set; }
        /// <summary>The See property</summary>
        public bool? See { get; set; }
        /// <summary>The SetAccess property</summary>
        public bool? SetAccess { get; set; }
        /// <summary>The Write property</summary>
        public bool? Write { get; set; }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel"/> and sets the default values.
        /// </summary>
        public AccessLevel()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.AccessLevel();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "Create", n => { Create = n.GetBoolValue(); } },
                { "Move", n => { Move = n.GetBoolValue(); } },
                { "Read", n => { Read = n.GetBoolValue(); } },
                { "See", n => { See = n.GetBoolValue(); } },
                { "SetAccess", n => { SetAccess = n.GetBoolValue(); } },
                { "Write", n => { Write = n.GetBoolValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteBoolValue("Create", Create);
            writer.WriteBoolValue("Move", Move);
            writer.WriteBoolValue("Read", Read);
            writer.WriteBoolValue("See", See);
            writer.WriteBoolValue("SetAccess", SetAccess);
            writer.WriteBoolValue("Write", Write);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
