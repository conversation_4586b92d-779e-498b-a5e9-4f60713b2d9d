﻿using System;

using DocuSign.eSign.Api;
using DocuSign.eSign.Client;

using Microsoft.Kiota.Abstractions.Authentication;

using TaskManagementService.Infrastructure.Interfaces;

namespace TaskManagementService.Infrastructure.Factories;
public sealed class ESignApiClientFactory : IEsignApiClient
{
    private IEnvelopesApi? _envelopesApi;
    private ISigningGroupsApi? _signingGroupsApi;
    private readonly IAccessTokenProvider _tokenProvider;

    public ESignApiClientFactory(IAccessTokenProvider httpContextService)
    {
        _tokenProvider = httpContextService;
    }

    public async Task<IEnvelopesApi> GetEsignEnvelopeApiClientAsync(DocuSignClient docusignClient)
    {
        ArgumentNullException.ThrowIfNull(docusignClient, nameof(docusignClient));
        if (_envelopesApi != null)
        {
            return _envelopesApi;
        }

        var jwtUserToken = await _tokenProvider.GetAuthorizationTokenAsync(null!);
        docusignClient.Configuration.DefaultHeader.Add("Authorization", "Bearer " + jwtUserToken);
        _envelopesApi = new EnvelopesApi(docusignClient);
        return _envelopesApi;
    }

    public async Task<ISigningGroupsApi> GetEsignSigningGroupsApiClientAsync(DocuSignClient docusignClient)
    {
        ArgumentNullException.ThrowIfNull(docusignClient, nameof(docusignClient));
        if (_signingGroupsApi != null)
        {
            return _signingGroupsApi;
        }

        var jwtUserToken = await _tokenProvider.GetAuthorizationTokenAsync(null!);
        docusignClient.Configuration.DefaultHeader.Add("Authorization", "Bearer " + jwtUserToken);
        _signingGroupsApi = new SigningGroupsApi(docusignClient);
        return _signingGroupsApi;
    }
}
