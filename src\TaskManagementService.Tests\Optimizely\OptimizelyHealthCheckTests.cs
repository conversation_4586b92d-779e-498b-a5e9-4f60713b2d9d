using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

using DocuSign.OneConfig.Extensions.Msf;

using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

using Moq;
using Moq.Contrib.HttpClient;

using TaskManagementService.Config;
using TaskManagementService.Optimizely;

using Xunit;

namespace TaskManagementService.Tests.Optimizely;

[Trait("TestType", "UnitTest")]
public class OptimizelyHealthCheckTests
{
    private readonly Mock<ILogger<OptimizelyHealthCheck>> _mockLogger;
    private readonly Mock<IOptions<HealthCheckSettings>> _mockHealthCheckSettings;
    private readonly Mock<HttpMessageHandler> _mockMessageHandler;
    private readonly HttpClient _httpClient;

    public OptimizelyHealthCheckTests()
    {
        _mockLogger = new Mock<ILogger<OptimizelyHealthCheck>>();
        _mockHealthCheckSettings = new Mock<IOptions<HealthCheckSettings>>();
        _mockMessageHandler = new Mock<HttpMessageHandler>();
        _httpClient = _mockMessageHandler.CreateClient();

        // Setup default health check settings
        var healthCheckSettings = new HealthCheckSettings
        {
            Optimizely = new OptimizelyHealthCheckOptions
            {
                LogOnlyMode = false // Default to false for testing
            }
        };
        _mockHealthCheckSettings.Setup(x => x.Value).Returns(healthCheckSettings);
    }

    [Fact]
    public void ConstructorWhenOptionsIsNullThrowsArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => new OptimizelyHealthCheck(null!, _mockHealthCheckSettings.Object, _mockLogger.Object, _httpClient));
    }

    [Fact]
    public async Task CheckHealthAsyncWhenOptimizelyIsDisabledReturnsHealthy()
    {
        // Arrange
        var options = new OneConfigOptimizelyOptions { EnableGlobalProject = false };
        var mockOptions = new Mock<IOptions<OneConfigOptimizelyOptions>>();
        mockOptions.Setup(x => x.Value).Returns(options);

        var healthCheck = new OptimizelyHealthCheck(mockOptions.Object, _mockHealthCheckSettings.Object, _mockLogger.Object, _httpClient);
        var context = new HealthCheckContext();

        // Act
        var result = await healthCheck.CheckHealthAsync(context, CancellationToken.None);

        // Assert
        Assert.Equal(HealthStatus.Healthy, result.Status);
        Assert.Equal("Optimizely is disabled", result.Description);
    }

    [Fact]
    public async Task CheckHealthAsyncWithValidProjectReturnsHealthy()
    {
        // Arrange
        const string sdkKey = "valid-sdk-key";
        var options = new OneConfigOptimizelyOptions
        {
            EnableGlobalProject = true,
            GlobalProjectSdkKey = sdkKey
        };
        var mockOptions = new Mock<IOptions<OneConfigOptimizelyOptions>>();
        mockOptions.Setup(x => x.Value).Returns(options);

        _mockMessageHandler.SetupRequest($"https://cdn.optimizely.com/datafiles/{sdkKey}.json")
            .ReturnsResponse(HttpStatusCode.OK, "{}");

        var healthCheck = new OptimizelyHealthCheck(mockOptions.Object, _mockHealthCheckSettings.Object, _mockLogger.Object, _httpClient);

        // Act
        var result = await healthCheck.CheckHealthAsync(new HealthCheckContext());

        // Assert
        Assert.Equal(HealthStatus.Healthy, result.Status);
    }

    [Fact]
    public async Task CheckHealthAsyncWithInvalidSdkKeyReturnsUnhealthy()
    {
        // Arrange
        const string sdkKey = "invalid-sdk-key";
        var options = new OneConfigOptimizelyOptions
        {
            EnableGlobalProject = true,
            GlobalProjectSdkKey = sdkKey
        };
        var mockOptions = new Mock<IOptions<OneConfigOptimizelyOptions>>();
        mockOptions.Setup(x => x.Value).Returns(options);

        _mockMessageHandler.SetupRequest($"https://cdn.optimizely.com/datafiles/{sdkKey}.json")
            .ReturnsResponse(HttpStatusCode.NotFound, string.Empty);

        var healthCheck = new OptimizelyHealthCheck(mockOptions.Object, _mockHealthCheckSettings.Object, _mockLogger.Object, _httpClient);

        // Act
        var result = await healthCheck.CheckHealthAsync(new HealthCheckContext());

        // Assert
        Assert.Equal(HealthStatus.Unhealthy, result.Status);
    }

    [Fact]
    public async Task CheckHealthAsyncWithMixedProjectsReturnsDegraded()
    {
        // Arrange
        const string validSdkKey = "valid-sdk-key";
        const string invalidSdkKey = "invalid-sdk-key";
        var options = new OneConfigOptimizelyOptions();
        options.Projects.Add("valid-project", new OneConfigOptimizelyProjectOptions { SdkKey = validSdkKey });
        options.Projects.Add("invalid-project", new OneConfigOptimizelyProjectOptions { SdkKey = invalidSdkKey });

        var mockOptions = new Mock<IOptions<OneConfigOptimizelyOptions>>();
        mockOptions.Setup(x => x.Value).Returns(options);

        _mockMessageHandler.SetupRequest($"https://cdn.optimizely.com/datafiles/{validSdkKey}.json")
            .ReturnsResponse(HttpStatusCode.OK, "{}");
        _mockMessageHandler.SetupRequest($"https://cdn.optimizely.com/datafiles/{invalidSdkKey}.json")
            .ReturnsResponse(HttpStatusCode.NotFound, string.Empty);

        var healthCheck = new OptimizelyHealthCheck(mockOptions.Object, _mockHealthCheckSettings.Object, _mockLogger.Object, _httpClient);

        // Act
        var result = await healthCheck.CheckHealthAsync(new HealthCheckContext());

        // Assert
        Assert.Equal(HealthStatus.Degraded, result.Status);
    }

    [Fact]
    public async Task CheckHealthAsyncWithRequestTimeoutReturnsUnhealthy()
    {
        // Arrange
        const string sdkKey = "timeout-sdk-key";
        var options = new OneConfigOptimizelyOptions
        {
            EnableGlobalProject = true,
            GlobalProjectSdkKey = sdkKey
        };
        var mockOptions = new Mock<IOptions<OneConfigOptimizelyOptions>>();
        mockOptions.Setup(x => x.Value).Returns(options);

        _mockMessageHandler.SetupRequest($"https://cdn.optimizely.com/datafiles/{sdkKey}.json")
            .Throws<TaskCanceledException>();

        var healthCheck = new OptimizelyHealthCheck(mockOptions.Object, _mockHealthCheckSettings.Object, _mockLogger.Object, _httpClient);

        // Act
        var result = await healthCheck.CheckHealthAsync(new HealthCheckContext());

        // Assert
        Assert.Equal(HealthStatus.Unhealthy, result.Status);
    }
}
