using System;

using DocuSign.OneConfig.Extensions.Msf;

using TaskManagementService.Core.Interfaces;

namespace TaskManagementService.Optimizely;

/// <summary>
/// Provides user context for Optimizely feature flag evaluation.
/// </summary>
public sealed class UserContextProvider(IRequestContextService requestContextService)
    : IUserContextProvider
{
    private readonly IRequestContextService _requestContextService = requestContextService ?? throw new ArgumentNullException(nameof(requestContextService));

    /// <summary>
    /// Gets the user ID from the request context.
    /// </summary>
    public string UserId => _requestContextService.UserId.ToString();

    /// <summary>
    /// Gets the account ID from the request context.
    /// </summary>
    public string AccountId => _requestContextService.AccountId.ToString();
}
