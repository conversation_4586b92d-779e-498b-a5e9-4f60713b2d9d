// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class ActivityHistory : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The HistoryEvents property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Activity>? HistoryEvents { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Activity> HistoryEvents { get; set; }
#endif
        /// <summary>The Href property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Href { get; set; }
#nullable restore
#else
        public string Href { get; set; }
#endif
        /// <summary>The Source property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Source { get; set; }
#nullable restore
#else
        public string Source { get; set; }
#endif
        /// <summary>The TaskId property</summary>
        public Guid? TaskId { get; set; }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ActivityHistory"/> and sets the default values.
        /// </summary>
        public ActivityHistory()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ActivityHistory"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ActivityHistory CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ActivityHistory();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "HistoryEvents", n => { HistoryEvents = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Activity>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Activity.CreateFromDiscriminatorValue)?.AsList(); } },
                { "Href", n => { Href = n.GetStringValue(); } },
                { "Source", n => { Source = n.GetStringValue(); } },
                { "TaskId", n => { TaskId = n.GetGuidValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Activity>("HistoryEvents", HistoryEvents);
            writer.WriteStringValue("Href", Href);
            writer.WriteStringValue("Source", Source);
            writer.WriteGuidValue("TaskId", TaskId);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
