# Publish Pull Request

Creates a GitHub pull request, automatically generating the title and description with Jira integration.

## Instructions

Your goal is to create a GitHub pull request. Follow these steps precisely:

### 1. Identify the Jira Ticket

* Get the current branch name using `git rev-parse --abbrev-ref HEAD`
* Extract the Jira ticket ID from the branch name. The ticket format is expected to be `IAMTASK-xxxxx` (e.g.,  `IAMTASK-94384`). A typical branch name would be `IAMTASK-94384-gemini-cli-prompts`
* If you cannot find a ticket ID in the branch name, ask the user to provide one

### 2. Analyze Code Changes

* Get the detailed difference between the current branch and `origin/main` by running `git diff origin/main..HEAD`
* Carefully analyze the output of the diff to create a comprehensive, human-readable summary of the changes
* The summary should be a bulleted list explaining the key modifications (e.g., "Modified `MyClass.cs` to add a new method `CalculateScore()`"). Explain the *purpose* of the changes

### 3. Gather Other Information

* Read the content of the pull request template file located at `.github/pull_request_template.md`
* Get the subject of the most recent commit on the branch using `git log -1 --pretty=%s`

### 4. Construct the Pull Request Title and Body

* Construct the **title** by combining the Jira ticket ID and the subject of the most recent commit using {TICKET_ID} and {COMMIT_SUBJECT}. Use conventional commits style if possible
* Start with the full content from the PR template file to create the **body**
* **First**, in the body, replace the line `JIRA Ticket: https://docusign.atlassian.net/browse/IAMTASK-XXXXX` with `JIRA Ticket: https://docusign.atlassian.net/browse/{TICKET_ID}`
* **Second**, replace the placeholder `_What is this pull request for? Provide an overview about what this pull request is for at a high level._` with the **title** you just constructed
* **Third**, replace the placeholder `_What changes were made? Add information about what changes (e.g., code, config) were made and why these choices were made._` with the detailed summary you generated from the `git diff` analysis in step 2

### 5. Create the Pull Request

* Write the final pull request body to a temporary file named `pr_body.md`
* Use the `gh pr create` command. Use the title you constructed. For the body, use the `--body-file pr_body.md` flag, pointing to the file you just created
* Add the label "Copilot CLI"
* Pass through any additional arguments provided to this command, such as `--reviewer`, directly to the `gh` command
* After the command executes, delete the temporary file `pr_body.md`

### 6. Confirm

* Report to the user that the pull request was created successfully

## Usage

Use this prompt when you need to create a pull request with proper Jira integration and automated title/description generation based on your code changes.
