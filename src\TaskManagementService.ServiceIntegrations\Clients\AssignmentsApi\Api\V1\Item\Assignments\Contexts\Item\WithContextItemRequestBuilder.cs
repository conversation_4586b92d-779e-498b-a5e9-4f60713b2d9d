// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using System;
using TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models;
namespace TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Contexts.Item
{
    /// <summary>
    /// Builds and executes requests for operations under \api\v1\{accountId}\Assignments\contexts\{contextId}
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class WithContextItemRequestBuilder : BaseRequestBuilder
    {
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Contexts.Item.WithContextItemRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public WithContextItemRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/v1/{accountId}/Assignments/contexts/{contextId}{?count*,includeHistory*,pagingCursor*,status*}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Contexts.Item.WithContextItemRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public WithContextItemRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/v1/{accountId}/Assignments/contexts/{contextId}{?count*,includeHistory*,pagingCursor*,status*}", rawUrl)
        {
        }
        /// <summary>
        /// Deletes all assignments for a context.
        /// </summary>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
        /// <exception cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails">When receiving a 400 status code</exception>
        /// <exception cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails">When receiving a 401 status code</exception>
        /// <exception cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails">When receiving a 500 status code</exception>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task DeleteAsync(Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task DeleteAsync(Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            var requestInfo = ToDeleteRequestInformation(requestConfiguration);
            var errorMapping = new Dictionary<string, ParsableFactory<IParsable>>
            {
                { "400", global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails.CreateFromDiscriminatorValue },
                { "401", global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails.CreateFromDiscriminatorValue },
                { "500", global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails.CreateFromDiscriminatorValue },
            };
            await RequestAdapter.SendNoContentAsync(requestInfo, errorMapping, cancellationToken).ConfigureAwait(false);
        }
        /// <summary>
        /// Gets assignments by context.
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponseListResponse"/></returns>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
        /// <exception cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails">When receiving a 400 status code</exception>
        /// <exception cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails">When receiving a 401 status code</exception>
        /// <exception cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails">When receiving a 500 status code</exception>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponseListResponse?> GetAsync(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Contexts.Item.WithContextItemRequestBuilder.WithContextItemRequestBuilderGetQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponseListResponse> GetAsync(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Contexts.Item.WithContextItemRequestBuilder.WithContextItemRequestBuilderGetQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            var requestInfo = ToGetRequestInformation(requestConfiguration);
            var errorMapping = new Dictionary<string, ParsableFactory<IParsable>>
            {
                { "400", global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails.CreateFromDiscriminatorValue },
                { "401", global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails.CreateFromDiscriminatorValue },
                { "500", global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.ErrorDetails.CreateFromDiscriminatorValue },
            };
            return await RequestAdapter.SendAsync<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponseListResponse>(requestInfo, global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Models.AssignmentResponseListResponse.CreateFromDiscriminatorValue, errorMapping, cancellationToken).ConfigureAwait(false);
        }
        /// <summary>
        /// Deletes all assignments for a context.
        /// </summary>
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToDeleteRequestInformation(Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToDeleteRequestInformation(Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default)
        {
#endif
            var requestInfo = new RequestInformation(Method.DELETE, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json");
            return requestInfo;
        }
        /// <summary>
        /// Gets assignments by context.
        /// </summary>
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Contexts.Item.WithContextItemRequestBuilder.WithContextItemRequestBuilderGetQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Contexts.Item.WithContextItemRequestBuilder.WithContextItemRequestBuilderGetQueryParameters>> requestConfiguration = default)
        {
#endif
            var requestInfo = new RequestInformation(Method.GET, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json");
            return requestInfo;
        }
        /// <summary>
        /// Returns a request builder with the provided arbitrary URL. Using this method means any other path or query parameters are ignored.
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Contexts.Item.WithContextItemRequestBuilder"/></returns>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        public global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Contexts.Item.WithContextItemRequestBuilder WithUrl(string rawUrl)
        {
            return new global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Contexts.Item.WithContextItemRequestBuilder(rawUrl, RequestAdapter);
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class WithContextItemRequestBuilderDeleteRequestConfiguration : RequestConfiguration<DefaultQueryParameters>
        {
        }
        /// <summary>
        /// Gets assignments by context.
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class WithContextItemRequestBuilderGetQueryParameters 
        {
            [QueryParameter("count")]
            public int? Count { get; set; }
            [QueryParameter("includeHistory")]
            public bool? IncludeHistory { get; set; }
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            [QueryParameter("pagingCursor")]
            public string? PagingCursor { get; set; }
#nullable restore
#else
            [QueryParameter("pagingCursor")]
            public string PagingCursor { get; set; }
#endif
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            [QueryParameter("status")]
            public string[]? Status { get; set; }
#nullable restore
#else
            [QueryParameter("status")]
            public string[] Status { get; set; }
#endif
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class WithContextItemRequestBuilderGetRequestConfiguration : RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.AssignmentsApi.Api.V1.Item.Assignments.Contexts.Item.WithContextItemRequestBuilder.WithContextItemRequestBuilderGetQueryParameters>
        {
        }
    }
}
#pragma warning restore CS0618
