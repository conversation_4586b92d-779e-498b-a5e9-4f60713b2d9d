﻿using MediatR;

using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

namespace TaskManagementService.Core.Handlers;

public class UnassignTaskCommandHandler : IRequestHandler<UnassignTaskCommand, List<string>>
{
    private readonly ILogger<UnassignTaskCommandHandler> _logger;
    private readonly IClmTaskService _clmTaskServices;

    public UnassignTaskCommandHandler(ILogger<UnassignTaskCommandHandler> logger, IClmTaskService clmTaskServices)
    {
        _logger = logger;
        _clmTaskServices = clmTaskServices;
    }

    public Task<List<string>> Handle(UnassignTaskCommand request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));

        _logger.LogInformation("Attempting to unassign task for task: {TaskId}", request.TaskId);

        return _clmTaskServices.PostUnassignTaskAsync(request.AccountId, request.TaskId);
    }
}
