// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using System;
using TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups
{
    /// <summary>
    /// Builds and executes requests for operations under \v2\{accountId}\members\{id}\groups
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class GroupsRequestBuilder : BaseRequestBuilder
    {
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups.GroupsRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public GroupsRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/v2/{accountId}/members/{id}/groups{?pageSortParams%2EcaseInsensitive*,pageSortParams%2Efilter*,pageSortParams%2EfilterExact*,pageSortParams%2Elimit*,pageSortParams%2Eoffset*,pageSortParams%2EsortDirection*,pageSortParams%2EsortProperty*}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups.GroupsRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public GroupsRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/v2/{accountId}/members/{id}/groups{?pageSortParams%2EcaseInsensitive*,pageSortParams%2Efilter*,pageSortParams%2EfilterExact*,pageSortParams%2Elimit*,pageSortParams%2Eoffset*,pageSortParams%2EsortDirection*,pageSortParams%2EsortProperty*}", rawUrl)
        {
        }
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup"/></returns>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup?> GetAsync(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups.GroupsRequestBuilder.GroupsRequestBuilderGetQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup> GetAsync(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups.GroupsRequestBuilder.GroupsRequestBuilderGetQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            var requestInfo = ToGetRequestInformation(requestConfiguration);
            return await RequestAdapter.SendAsync<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup>(requestInfo, global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.ApiCollectionGroup.CreateFromDiscriminatorValue, default, cancellationToken).ConfigureAwait(false);
        }
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups.GroupsRequestBuilder.GroupsRequestBuilderGetQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups.GroupsRequestBuilder.GroupsRequestBuilderGetQueryParameters>> requestConfiguration = default)
        {
#endif
            var requestInfo = new RequestInformation(Method.GET, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json, application/scim+json");
            return requestInfo;
        }
        /// <summary>
        /// Returns a request builder with the provided arbitrary URL. Using this method means any other path or query parameters are ignored.
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups.GroupsRequestBuilder"/></returns>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        public global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups.GroupsRequestBuilder WithUrl(string rawUrl)
        {
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups.GroupsRequestBuilder(rawUrl, RequestAdapter);
        }
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        #pragma warning disable CS1591
        public partial class GroupsRequestBuilderGetQueryParameters 
        #pragma warning restore CS1591
        {
            [QueryParameter("pageSortParams%2EcaseInsensitive")]
            public bool? PageSortParamsCaseInsensitive { get; set; }
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            [QueryParameter("pageSortParams%2Efilter")]
            public string? PageSortParamsFilter { get; set; }
#nullable restore
#else
            [QueryParameter("pageSortParams%2Efilter")]
            public string PageSortParamsFilter { get; set; }
#endif
            [QueryParameter("pageSortParams%2EfilterExact")]
            public bool? PageSortParamsFilterExact { get; set; }
            [QueryParameter("pageSortParams%2Elimit")]
            public int? PageSortParamsLimit { get; set; }
            [QueryParameter("pageSortParams%2Eoffset")]
            public int? PageSortParamsOffset { get; set; }
            [QueryParameter("pageSortParams%2EsortDirection")]
            public int? PageSortParamsSortDirection { get; set; }
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
            [QueryParameter("pageSortParams%2EsortProperty")]
            public string? PageSortParamsSortProperty { get; set; }
#nullable restore
#else
            [QueryParameter("pageSortParams%2EsortProperty")]
            public string PageSortParamsSortProperty { get; set; }
#endif
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class GroupsRequestBuilderGetRequestConfiguration : RequestConfiguration<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.V2.Item.Members.Item.Groups.GroupsRequestBuilder.GroupsRequestBuilderGetQueryParameters>
        {
        }
    }
}
#pragma warning restore CS0618
