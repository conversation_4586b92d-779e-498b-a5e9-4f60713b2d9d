using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using MediatR;

using Microsoft.Extensions.Logging;

using Moq;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Handlers;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

using Xunit;

using TaskGroupMember = TaskManagementService.Core.Models.TaskGroupMember;

namespace TaskManagementService.Tests.Handlers;

[Trait("TestType", "UnitTest")]
public class GetUserTasksQueryHandlerTest
{
    [Fact]
    public async Task Handle()
    {
        var mockLogger = new Mock<ILogger<GetUserTasksQueryHandler>>();
        var mockTaskService = new Mock<ITaskService>();
        var mockMediator = new Mock<IMediator>();
        var mockAgreementAttributesConfig = new Mock<IAgreementAttributesConfig>();

        var mockAccountId = new Guid("********-0004-0001-000b-************");
        var mockTaskId = new Guid("*************-0001-000b-************");
        var mockAgreementId = new Guid("*************-0001-000b-************");
        var mockTaskName = "test";
        var mockWorkflowName = "test2";
        var mockTitle = "title";
        var mockDescription = "description";
        var mockUrl = "url";
        var startDate = "2024-12-23T00:39:51.397Z";
        var dueDate = "2025-01-23T00:39:51.397Z";
        var startDateTimeOffset = new DateTimeOffset(new DateTime(2025, 1, 1));
        var modifiedDateTimeOffset = new DateTimeOffset(new DateTime(2025, 1, 2));

        var mockAgreement = new UserTaskAgreement() { CreatedAt = startDateTimeOffset, Id = mockAgreementId, ModifiedAt = modifiedDateTimeOffset, Etag = 1 };

        var mockTaskGroupId = new Guid("********-0004-0001-000b-************");
        var mockTaskGroupName = "test";

        var mockTaskGroup = new TaskGroupWithPermissions() { Id = mockTaskGroupId.ToString(), Name = mockTaskGroupName, Source = TaskSource.ClmWorkflow };

        var mockGetUserTasksQuery =
            new GetUserTasksQuery(mockAccountId, new TaskFilter());

        var mockTask = new UserTask()
        {
            Id = mockTaskId,
            Name = mockTaskName,
            Source = TaskSource.ClmWorkflow,
            DueDate = dueDate,
            CreatedDate = startDate,
            Title = mockTitle,
            WorkflowName = mockWorkflowName,
            Type = TaskType.EditDocument,
            Description = mockDescription,
            NavigationUrl = mockUrl,
            TaskGroupAssignee = mockTaskGroup,
            AgreementIds = [mockAgreementId]
        };

        mockTaskService
            .Setup(x => x.GetTasksAsync(mockAccountId, It.IsAny<TaskFilter>()))
            .Returns(Task.FromResult(new List<UserTask> { mockTask }));
        mockAgreementAttributesConfig
            .Setup(x => x.ShouldFetchAttributeDataFromAdm)
            .Returns(true);
        mockMediator
            .Setup(x => x.Send(It.IsAny<GetUserAgreementsQuery>(), CancellationToken.None))
            .Returns(Task.FromResult(new Dictionary<Guid, UserTaskAgreement>() { { mockAgreementId, mockAgreement } }));

        var getUserTasksQueryHandler =
            new GetUserTasksQueryHandler(mockLogger.Object, [mockTaskService.Object], mockMediator.Object, mockAgreementAttributesConfig.Object);

        var result = await getUserTasksQueryHandler.Handle(mockGetUserTasksQuery, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Single(result.Items);
        var task = result.Items[0];
        Assert.Equal(mockTaskId, task.Id);
        Assert.Equal(mockTaskName, task.Name);
        Assert.Equal(TaskSource.ClmWorkflow, task.Source);
        Assert.Equal(mockUrl, task.NavigationUrl);
        Assert.Equal(dueDate, task.DueDate);
        Assert.Equal(startDate, task.CreatedDate);
        Assert.Equal(mockTitle, task.Title);
        Assert.Equal(mockWorkflowName, task.WorkflowName);
        Assert.Equal(mockDescription, task.Description);

        var taskGroupAssignee = task.TaskGroupAssignee;
        Assert.NotNull(taskGroupAssignee);
        Assert.Equal(mockTaskGroupId.ToString(), taskGroupAssignee.Id);
        Assert.Equal(mockTaskGroupName, taskGroupAssignee.Name);
        Assert.Equal(TaskSource.ClmWorkflow, taskGroupAssignee.Source);

        var agreements = task.Agreements.ToList();
        Assert.Single(agreements);
        var agreement = agreements[0];
        Assert.Equal(startDateTimeOffset, agreement.CreatedAt);
        Assert.Equal(modifiedDateTimeOffset, agreement.ModifiedAt);
        Assert.Equal(1, agreement.Etag);
        Assert.Equal(mockAgreementId, agreement.Id);
    }

    [Fact]
    public async Task HandleNoAdm()
    {
        var mockLogger = new Mock<ILogger<GetUserTasksQueryHandler>>();
        var mockTaskService = new Mock<ITaskService>();
        var mockMediator = new Mock<IMediator>();
        var mockAgreementAttributesConfig = new Mock<IAgreementAttributesConfig>();

        var mockAccountId = new Guid("********-0004-0001-000b-************");
        var mockTaskId = new Guid("*************-0001-000b-************");
        var mockAgreementId = new Guid("*************-0001-000b-************");
        var mockTaskName = "test";
        var mockWorkflowName = "test2";
        var mockTitle = "title";
        var mockDescription = "description";
        var mockUrl = "url";
        var startDate = "2024-12-23T00:39:51.397Z";
        var dueDate = "2025-01-23T00:39:51.397Z";

        var mockTaskGroupId = new Guid("********-0004-0001-000b-************");
        var mockTaskGroupName = "test";

        var mockTaskGroup = new TaskGroupWithPermissions() { Id = mockTaskGroupId.ToString(), Name = mockTaskGroupName, Source = TaskSource.ClmWorkflow };

        var mockGetUserTasksQuery =
            new GetUserTasksQuery(mockAccountId, new TaskFilter());

        var mockTask = new UserTask()
        {
            Id = mockTaskId,
            Name = mockTaskName,
            Source = TaskSource.ClmWorkflow,
            DueDate = dueDate,
            CreatedDate = startDate,
            Title = mockTitle,
            WorkflowName = mockWorkflowName,
            Type = TaskType.EditDocument,
            Description = mockDescription,
            NavigationUrl = mockUrl,
            TaskGroupAssignee = mockTaskGroup,
            AgreementIds = [mockAgreementId]
        };

        mockTaskService
            .Setup(x => x.GetTasksAsync(mockAccountId, It.IsAny<TaskFilter>()))
            .Returns(Task.FromResult(new List<UserTask> { mockTask }));
        mockAgreementAttributesConfig
            .Setup(x => x.ShouldFetchAttributeDataFromAdm)
            .Returns(false);

        var getUserTasksQueryHandler =
            new GetUserTasksQueryHandler(mockLogger.Object, [mockTaskService.Object], mockMediator.Object, mockAgreementAttributesConfig.Object);

        var result = await getUserTasksQueryHandler.Handle(mockGetUserTasksQuery, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Single(result.Items);
        var task = result.Items[0];
        Assert.Equal(mockTaskId, task.Id);
        Assert.Equal(mockTaskName, task.Name);
        Assert.Equal(TaskSource.ClmWorkflow, task.Source);
        Assert.Equal(mockUrl, task.NavigationUrl);
        Assert.Equal(dueDate, task.DueDate);
        Assert.Equal(startDate, task.CreatedDate);
        Assert.Equal(mockTitle, task.Title);
        Assert.Equal(mockWorkflowName, task.WorkflowName);
        Assert.Equal(mockDescription, task.Description);

        var taskGroupAssignee = task.TaskGroupAssignee;
        Assert.NotNull(taskGroupAssignee);
        Assert.Equal(mockTaskGroupId.ToString(), taskGroupAssignee.Id);
        Assert.Equal(mockTaskGroupName, taskGroupAssignee.Name);
        Assert.Equal(TaskSource.ClmWorkflow, taskGroupAssignee.Source);
    }

    [Fact]
    public async Task HandleNoAgreement()
    {
        var mockLogger = new Mock<ILogger<GetUserTasksQueryHandler>>();
        var mockTaskService = new Mock<ITaskService>();
        var mockMediator = new Mock<IMediator>();
        var mockAgreementAttributesConfig = new Mock<IAgreementAttributesConfig>();

        var mockAccountId = new Guid("********-0004-0001-000b-************");
        var mockTaskId = new Guid("*************-0001-000b-************");
        var mockAgreementId = new Guid("*************-0001-000b-************");

        var mockGetUserTasksQuery =
            new GetUserTasksQuery(mockAccountId, new TaskFilter());

        var mockTask = new UserTask()
        {
            Id = mockTaskId,
            Source = TaskSource.ClmWorkflow,
            AgreementIds = [mockAgreementId]
        };

        mockTaskService
            .Setup(x => x.GetTasksAsync(mockAccountId, It.IsAny<TaskFilter>()))
            .Returns(Task.FromResult(new List<UserTask> { mockTask }));
        mockAgreementAttributesConfig
            .Setup(x => x.ShouldFetchAttributeDataFromAdm)
            .Returns(true);
        mockMediator
            .Setup(x => x.Send(It.IsAny<GetUserAgreementsQuery>(), CancellationToken.None))
            .Returns(Task.FromResult(new Dictionary<Guid, UserTaskAgreement>() { }));

        var getUserTasksQueryHandler =
            new GetUserTasksQueryHandler(mockLogger.Object, [mockTaskService.Object], mockMediator.Object, mockAgreementAttributesConfig.Object);

        var result = await getUserTasksQueryHandler.Handle(mockGetUserTasksQuery, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal(0, result.Total);
    }

    [Fact]
    public async Task NullHandle()
    {
        var mockLogger = new Mock<ILogger<GetUserTasksQueryHandler>>();
        var mockTaskService = new Mock<ITaskService>();
        var mockMediator = new Mock<IMediator>();
        var mockAgreementAttributesConfig = new Mock<IAgreementAttributesConfig>();

        var getUserTasksQueryHandler =
            new GetUserTasksQueryHandler(mockLogger.Object, [mockTaskService.Object], mockMediator.Object, mockAgreementAttributesConfig.Object);

        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            getUserTasksQueryHandler.Handle(null, CancellationToken.None));
    }

    [Fact]
    public async Task HandleWithSort()
    {
        var mockLogger = new Mock<ILogger<GetUserTasksQueryHandler>>();
        var mockTaskService = new Mock<ITaskService>();
        var mockMediator = new Mock<IMediator>();
        var mockAgreementAttributesConfig = new Mock<IAgreementAttributesConfig>();

        var mockAccountId = new Guid("********-0004-0001-000b-************");
        var mockTaskId = new Guid("*************-0001-000b-************");
        var mockAgreementId = new Guid("*************-0001-000b-************");
        var mockTaskName = "test";
        var mockWorkflowName = "test2";
        var mockTitle = "title";
        var mockDescription = "description";
        var mockUrl = "url";
        var startDate = "2024-12-23T00:39:51.397Z";
        var dueDate = "2025-01-23T00:39:51.397Z";
        var startDateTimeOffset = new DateTimeOffset(new DateTime(2025, 1, 1));
        var modifiedDateTimeOffset = new DateTimeOffset(new DateTime(2025, 1, 2));

        var mockAgreement = new UserTaskAgreement() { CreatedAt = startDateTimeOffset, Id = mockAgreementId, ModifiedAt = modifiedDateTimeOffset, Etag = 1 };

        var mockTaskGroupId = new Guid("********-0004-0001-000b-************");
        var mockTaskGroupName = "test";

        var mockTaskGroup = new TaskGroupWithPermissions { Id = mockTaskGroupId.ToString(), Name = mockTaskGroupName, Source = TaskSource.ClmWorkflow };

        var mockGetUserTasksQuery =
            new GetUserTasksQuery(mockAccountId, new TaskFilter { TaskSort = new TaskSort { ResultCount = 0 } });

        var mockTask = new UserTask()
        {
            Id = mockTaskId,
            Name = mockTaskName,
            Source = TaskSource.ClmWorkflow,
            DueDate = dueDate,
            CreatedDate = startDate,
            Title = mockTitle,
            WorkflowName = mockWorkflowName,
            Type = TaskType.EditDocument,
            Description = mockDescription,
            NavigationUrl = mockUrl,
            TaskGroupAssignee = mockTaskGroup,
            AgreementIds = [mockAgreementId]
        };

        mockTaskService
            .Setup(x => x.GetTasksAsync(mockAccountId, It.IsAny<TaskFilter>()))
            .Returns(Task.FromResult(new List<UserTask> { mockTask }));
        mockAgreementAttributesConfig
            .Setup(x => x.ShouldFetchAttributeDataFromAdm)
            .Returns(true);
        mockMediator
            .Setup(x => x.Send(It.IsAny<GetUserAgreementsQuery>(), CancellationToken.None))
            .Returns(Task.FromResult(new Dictionary<Guid, UserTaskAgreement>() { { mockAgreementId, mockAgreement } }));

        var getUserTasksQueryHandler =
            new GetUserTasksQueryHandler(mockLogger.Object, [mockTaskService.Object], mockMediator.Object, mockAgreementAttributesConfig.Object);

        var result = await getUserTasksQueryHandler.Handle(mockGetUserTasksQuery, CancellationToken.None);

        Assert.True(result.Items.Count == 0);

        mockGetUserTasksQuery =
            new GetUserTasksQuery(mockAccountId, new TaskFilter { TaskSort = new TaskSort { ResultCount = 1 } });

        mockTaskService
            .Setup(x => x.GetTasksAsync(mockAccountId, It.IsAny<TaskFilter>()))
            .Returns(Task.FromResult(new List<UserTask> { mockTask, mockTask }));

        getUserTasksQueryHandler =
            new GetUserTasksQueryHandler(mockLogger.Object, [mockTaskService.Object], mockMediator.Object, mockAgreementAttributesConfig.Object);

        result = await getUserTasksQueryHandler.Handle(mockGetUserTasksQuery, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Single(result.Items);
        var task = result.Items[0];
        Assert.Equal(mockTaskId, task.Id);
        Assert.Equal(mockTaskName, task.Name);
        Assert.Equal(TaskSource.ClmWorkflow, task.Source);
        Assert.Equal(mockUrl, task.NavigationUrl);
        Assert.Equal(dueDate, task.DueDate);
        Assert.Equal(startDate, task.CreatedDate);
        Assert.Equal(mockTitle, task.Title);
        Assert.Equal(mockWorkflowName, task.WorkflowName);
        Assert.Equal(mockDescription, task.Description);

        var taskGroupAssignee = task.TaskGroupAssignee;
        Assert.NotNull(taskGroupAssignee);
        Assert.Equal(mockTaskGroupId.ToString(), taskGroupAssignee.Id);
        Assert.Equal(mockTaskGroupName, taskGroupAssignee.Name);
        Assert.Equal(TaskSource.ClmWorkflow, taskGroupAssignee.Source);

        var agreements = task.Agreements.ToList();
        Assert.Single(agreements);
        var agreement = agreements[0];
        Assert.Equal(startDateTimeOffset, agreement.CreatedAt);
        Assert.Equal(modifiedDateTimeOffset, agreement.ModifiedAt);
        Assert.Equal(1, agreement.Etag);
        Assert.Equal(mockAgreementId, agreement.Id);
    }
}
