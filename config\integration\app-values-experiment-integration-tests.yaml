experiment:
  enabled: true
  duration: 10m
  progressDeadlineSeconds: 900
  name: experiment
  analysis:
    template: pre-deployment
  mergeVirtualServices:
    enabled: false

replicaCount: 1

autoscaling:
  enabled: false

virtualService:
  enabled: false

virtualServiceMesh:
  enabled: false

periodicTests:
  enabled: false

rollout:
  analysisTemplate:
    spec:
      preDeploymentAnalysis:
        backoffLimit: 1
        activeDeadlineSeconds: 300
        metadata:
          annotations:
            proxy.istio.io/config: '{ "holdApplicationUntilProxyStarts": true }'
        containers:
          - &analysisTemplateRunTests
            #This assumes that a docker image is created and is available for container deployment
            name: task-management-service-integration-tests # Container name
            image: task-management-service-integration-tests # Image name coming from azure pipeline yml
            imagePullPolicy: IfNotPresent
            command: ["/bin/bash"]
            args: ["/app/run-analysis.sh"]
            resources:
              cpu: 1
              memory: 1Gi
            env:
              - name: TEST_SERVER_URL
                value: "http://{{ .Release.Name }}:80"
              - name: TEST_STAGE
                value: "PullRequestNoMocks"
              - name: PUBLIC_URL
                value: "https://{{ if .Values.ingress.subDomain }}{{ .Values.ingress.subDomain }}.{{ end }}{{ .Values.publicApi.name }}.{{ .Values.ingress.domain }}/"
              - name: INTERNAL_URL
                value: "https://{{ if .Values.ingress.subDomain }}{{ .Values.ingress.subDomain }}.{{ end }}{{ .Values.internalApi.name }}.{{ .Values.ingress.domain }}/"
      inflightDeploymentAnalysis: {}
      postDeploymentAnalysis: {}
