// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class TaskSummaryWorkItem : IAdditionalDataHolder, IParsable
    #pragma warning restore CS1591
    {
        /// <summary>Stores additional data not described in the OpenAPI description found when deserializing. Can be used for serialization as well.</summary>
        public IDictionary<string, object> AdditionalData { get; set; }
        /// <summary>The AssignDate property</summary>
        public DateTimeOffset? AssignDate { get; set; }
        /// <summary>The AssigneeId property</summary>
        public Guid? AssigneeId { get; set; }
        /// <summary>The AssignorId property</summary>
        public Guid? AssignorId { get; set; }
        /// <summary>The CreatedDate property</summary>
        public DateTimeOffset? CreatedDate { get; set; }
        /// <summary>The Documents property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document>? Documents { get; set; }
#nullable restore
#else
        public List<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document> Documents { get; set; }
#endif
        /// <summary>The DueDate property</summary>
        public DateTimeOffset? DueDate { get; set; }
        /// <summary>The Id property</summary>
        public Guid? Id { get; set; }
        /// <summary>The Information property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Information { get; set; }
#nullable restore
#else
        public string Information { get; set; }
#endif
        /// <summary>The Name property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Name { get; set; }
#nullable restore
#else
        public string Name { get; set; }
#endif
        /// <summary>The Source property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Source { get; set; }
#nullable restore
#else
        public string Source { get; set; }
#endif
        /// <summary>The Type property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Type { get; set; }
#nullable restore
#else
        public string Type { get; set; }
#endif
        /// <summary>The WorkflowName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? WorkflowName { get; set; }
#nullable restore
#else
        public string WorkflowName { get; set; }
#endif
        /// <summary>The WorkflowQueueId property</summary>
        public Guid? WorkflowQueueId { get; set; }
        /// <summary>The WorkItemUrl property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? WorkItemUrl { get; set; }
#nullable restore
#else
        public string WorkItemUrl { get; set; }
#endif
        /// <summary>
        /// Instantiates a new <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskSummaryWorkItem"/> and sets the default values.
        /// </summary>
        public TaskSummaryWorkItem()
        {
            AdditionalData = new Dictionary<string, object>();
        }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskSummaryWorkItem"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskSummaryWorkItem CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.TaskSummaryWorkItem();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "AssignDate", n => { AssignDate = n.GetDateTimeOffsetValue(); } },
                { "AssigneeId", n => { AssigneeId = n.GetGuidValue(); } },
                { "AssignorId", n => { AssignorId = n.GetGuidValue(); } },
                { "CreatedDate", n => { CreatedDate = n.GetDateTimeOffsetValue(); } },
                { "Documents", n => { Documents = n.GetCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document>(global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document.CreateFromDiscriminatorValue)?.AsList(); } },
                { "DueDate", n => { DueDate = n.GetDateTimeOffsetValue(); } },
                { "Id", n => { Id = n.GetGuidValue(); } },
                { "Information", n => { Information = n.GetStringValue(); } },
                { "Name", n => { Name = n.GetStringValue(); } },
                { "Source", n => { Source = n.GetStringValue(); } },
                { "Type", n => { Type = n.GetStringValue(); } },
                { "WorkItemUrl", n => { WorkItemUrl = n.GetStringValue(); } },
                { "WorkflowName", n => { WorkflowName = n.GetStringValue(); } },
                { "WorkflowQueueId", n => { WorkflowQueueId = n.GetGuidValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteDateTimeOffsetValue("AssignDate", AssignDate);
            writer.WriteGuidValue("AssigneeId", AssigneeId);
            writer.WriteGuidValue("AssignorId", AssignorId);
            writer.WriteDateTimeOffsetValue("CreatedDate", CreatedDate);
            writer.WriteCollectionOfObjectValues<global::TaskManagementService.ServiceIntegrations.Clients.CLMV2API.Models.Document>("Documents", Documents);
            writer.WriteDateTimeOffsetValue("DueDate", DueDate);
            writer.WriteGuidValue("Id", Id);
            writer.WriteStringValue("Information", Information);
            writer.WriteStringValue("Name", Name);
            writer.WriteStringValue("Source", Source);
            writer.WriteStringValue("Type", Type);
            writer.WriteStringValue("WorkflowName", WorkflowName);
            writer.WriteGuidValue("WorkflowQueueId", WorkflowQueueId);
            writer.WriteStringValue("WorkItemUrl", WorkItemUrl);
            writer.WriteAdditionalData(AdditionalData);
        }
    }
}
#pragma warning restore CS0618
