﻿using MediatR;

using TaskManagementService.Core.Config;
using TaskManagementService.Core.Enums;
using TaskManagementService.Core.Interfaces;
using TaskManagementService.Core.Models;
using TaskManagementService.Core.Queries;

namespace TaskManagementService.Core.Handlers;

public class GetUserTasksQueryHandler(
    ILogger<GetUserTasksQueryHandler> logger,
    IEnumerable<ITaskService> taskServices,
    IMediator mediator,
    IAgreementAttributesConfig agreementAttributesConfig)
    : IRequestHandler<GetUserTasksQuery, PaginatedList<UserTask>>
{
    public async Task<PaginatedList<UserTask>> Handle(GetUserTasksQuery request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));

        var fetchTasks = new List<Task<List<UserTask>>>();
        foreach (var taskService in taskServices)
        {
            fetchTasks.Add(taskService.GetTasksAsync(request.AccountId, request.TaskFilter));
        }

        await Task.WhenAll(fetchTasks);

        if (!agreementAttributesConfig.ShouldFetchAttributeDataFromAdm)
        {
            var userTaskList = new List<UserTask>();
            foreach (var task in fetchTasks)
            {
                userTaskList.AddRange(await task);
            }

            return new PaginatedList<UserTask>(userTaskList, 0, 250, userTaskList.Count);
        }

        var userTasks = new Dictionary<Guid, UserTask>();

        // An agreement can have multiple tasks
        var agreementTaskMap = new Dictionary<Guid, List<Guid>>();

        foreach (var task in fetchTasks)
        {
            var tasks = await task;
            var sortedTasks = SortTasks(request.TaskFilter.TaskSort, tasks);
            foreach (var userTask in sortedTasks)
            {
                userTasks.TryAdd(userTask.Id, userTask);

                if (userTask.Source is not (TaskSource.ClmWorkflow or TaskSource.AssignmentsApi))
                {
                    continue;
                }

                var agreementId = userTask.AgreementIds.FirstOrDefault();
                if (agreementId == Guid.Empty)
                {
                    continue;
                }

                if (agreementTaskMap.TryGetValue(agreementId, out var taskList))
                {
                    taskList.Add(userTask.Id);
                }
                else
                {
                    agreementTaskMap.Add(agreementId, [userTask.Id]);
                }
            }
        }

        var agreementIds = agreementTaskMap.Keys.ToHashSet();
        var filteredAgreements = await mediator.Send(new GetUserAgreementsQuery(request.AccountId, agreementIds, request.TaskFilter.AgreementFilters, []), cancellationToken);
        logger.LogInformation("Task Agreements Count: {AgreementsCount}, Filtered Agreements Count: {TaskCount}.", agreementIds.Count, filteredAgreements.Count);

        foreach (var agreementId in agreementIds)
        {
            if (!agreementTaskMap.TryGetValue(agreementId, out var taskIds))
            {
                continue;
            }

            // If the agreement is not returned, it is likely that it is filtered out or not yet ingested in ADM.
            if (!filteredAgreements.TryGetValue(agreementId, out var agreement))
            {
                foreach (var taskId in taskIds)
                {
                    userTasks.Remove(taskId);
                }
            }
            else
            {
                foreach (var taskId in taskIds)
                {
                    if (userTasks.TryGetValue(taskId, out var userTask))
                    {
                        userTask.AddAgreement(agreement);
                    }
                }
            }
        }

        logger.LogInformation("Fetched {TaskCount} tasks", userTasks.Count);
        return new PaginatedList<UserTask>(userTasks.Values.ToList(), 0, 250, userTasks.Count);
    }

    private static List<UserTask> SortTasks(TaskSort? taskSort, List<UserTask> tasks)
    {
        if (taskSort == null)
        {
            return tasks;
        }

        var returnTasks = tasks;
        switch (taskSort.SortColumn)
        {
            case SortColumn.AssignedDate:
                returnTasks = tasks.OrderBy(task => task.AssignedDate).ToList();
                break;
            case SortColumn.DueDate:
                returnTasks = tasks.OrderBy(task => task.DueDate).ToList();
                break;
            default:
                break;
        }

        if (taskSort.ResultCount.HasValue)
        {
            returnTasks = returnTasks.Take(taskSort.ResultCount.Value).ToList();
        }

        return returnTasks;
    }
}
